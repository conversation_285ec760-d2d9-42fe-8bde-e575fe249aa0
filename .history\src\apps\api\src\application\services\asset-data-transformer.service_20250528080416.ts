import { Injectable, Logger } from '@nestjs/common';
import {
  KgxRecord,
  StandardAssetData,
  ValueFormatResponse,
  PreciousMetalsFormatResponse,
  BaseMetalsFormatResponse,
  CryptoFormatResponse,
  TransformationOptions,
  AssetType,
  ASSET_TYPE_UNITS
} from '../interfaces/asset-data.interface';

@Injectable()
export class AssetDataTransformerService {
  private readonly logger = new Logger(AssetDataTransformerService.name);

  /**
   * Transform raw KGX records to standardized asset data
   * @param records Raw database records
   * @param options Transformation options
   * @returns Array of standardized asset data
   */
  transformToStandardFormat(
    records: KgxRecord[],
    options: TransformationOptions = {}
  ): StandardAssetData[] {
    return records.map(record => this.transformSingleRecord(record, options));
  }

  /**
   * Transform a single KGX record to standardized format
   * @param record Raw database record
   * @param options Transformation options
   * @returns Standardized asset data
   */
  private transformSingleRecord(
    record: KgxRecord,
    options: TransformationOptions
  ): StandardAssetData {
    const assetType = this.determineAssetType(record);
    const unit = this.determineUnit(record, assetType);

    return {
      Symbol: record.symbol ?? '',
      Name: record.name ?? record.symbol ?? '',
      Price: record.price ?? null,
      High: record.high ?? record.price ?? null,
      Low: record.low ?? record.price ?? null,
      Mid: record.price ?? null,
      Ask: record.ask ?? (record.price ? record.price * 1.001 : null),
      Bid: record.bid ?? (record.price ? record.price * 0.999 : null),
      Change: record.change ?? record.price_change ?? 0,
      ChangeTrade: record.price_change ?? 0,
      ChangeUSD: record.change_usd ?? record.change_due_usd ?? 0,
      ChangePercentage: record.price_change_percent ?? 0,
      ChangePercentTrade: record.change_due_trade_percent ?? record.price_change_percent ?? 0,
      ChangePercentUSD: record.change_due_usd_percent ?? null,
      Currency: 'USD',
      Unit: unit,
      Timestamp: this.formatTimestamp(record.timestamp, options.dateFormat, options.timeFormat),
      KgxValue: record.kgx_value ?? null,
      AssetType: assetType,
      Volume: record.volume ?? null,
      MarketCap: record.market_cap ?? null,
      metadata: record.metadata ?? null,
      extra: record.extra ?? null
    };
  }

  /**
   * Transform to getValue format (direct array)
   */
  transformToValueFormat(
    records: KgxRecord[],
    options: TransformationOptions = {}
  ): ValueFormatResponse[] {
    return this.transformToStandardFormat(records, options);
  }

  /**
   * Transform to getPM format (precious metals wrapper)
   */
  transformToPreciousMetalsFormat(
    records: KgxRecord[],
    options: TransformationOptions = {}
  ): PreciousMetalsFormatResponse {
    const standardData = this.transformToStandardFormat(records, options);

    // Filter to only include precious metals and crypto (legacy behavior)
    const filteredData = standardData.filter(item =>
      item.AssetType === AssetType.PRECIOUS_METALS ||
      item.AssetType === AssetType.CRYPTOCURRENCY
    );

    const response: PreciousMetalsFormatResponse = {
      CryptoMetals: {
        crypto: filteredData
      }
    };

    if (options.includeKgx) {
      response.CryptoMetals.KgxValues = filteredData.map(item => item.KgxValue);
    }

    return response;
  }

  /**
   * Transform to getBM format (base metals wrapper)
   */
  transformToBaseMetalsFormat(
    records: KgxRecord[],
    options: TransformationOptions = {}
  ): BaseMetalsFormatResponse {
    const standardData = this.transformToStandardFormat(records, options);

    // Filter to only include base metals
    const filteredData = standardData.filter(item =>
      item.AssetType === AssetType.BASE_METALS
    );

    // Base metals format excludes certain fields
    const baseMetalsData = filteredData.map(item => ({
      Symbol: item.Symbol,
      High: item.High,
      Low: item.Low,
      ChangeTrade: item.ChangeTrade,
      Price: item.Price,
      ChangeUSD: item.ChangeUSD,
      Change: item.Change,
      ChangePercentUSD: item.ChangePercentUSD,
      ChangePercentage: item.ChangePercentage,
      ChangePercentTrade: item.ChangePercentTrade,
      Timestamp: item.Timestamp,
      Currency: item.Currency
    }));

    return {
      BaseMetals: {
        BM: baseMetalsData
      }
    };
  }

  /**
   * Transform to getCR format (crypto wrapper)
   */
  transformToCryptoFormat(
    records: KgxRecord[],
    options: TransformationOptions = {}
  ): CryptoFormatResponse {
    const standardData = this.transformToStandardFormat(records, options);

    const response: CryptoFormatResponse = {
      Cryptocurrencies: {
        CR: standardData
      }
    };

    if (options.includeKgx) {
      response.Cryptocurrencies.KgxValues = standardData.map(item => item.KgxValue);
    }

    return response;
  }

  /**
   * Determine asset type from record data
   */
  private determineAssetType(record: KgxRecord): AssetType {
    if (record.asset_type) {
      switch (record.asset_type.toUpperCase()) {
        case 'CRYPTOCURRENCY':
          return AssetType.CRYPTOCURRENCY;
        case 'PRECIOUS_METALS':
          return AssetType.PRECIOUS_METALS;
        case 'BASE_METALS':
          return AssetType.BASE_METALS;
        case 'ENERGY':
          return AssetType.ENERGY;
        case 'FOREX':
          return AssetType.FOREX;
        default:
          return AssetType.CRYPTOCURRENCY;
      }
    }

    // Fallback logic based on symbol
    const symbol = record.symbol?.toUpperCase() || '';

    // Precious metals
    if (['GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM', 'XAU', 'XAG', 'XPT', 'XPD', 'AU', 'AG', 'PT', 'PD'].includes(symbol)) {
      return AssetType.PRECIOUS_METALS;
    }

    // Base metals
    if (['COPPER', 'ALUMINUM', 'ZINC', 'NICKEL', 'LEAD', 'TIN', 'CU', 'AL', 'ZN', 'NI', 'PB', 'SN'].includes(symbol)) {
      return AssetType.BASE_METALS;
    }

    // Energy
    if (['OIL', 'GAS', 'WTI', 'BRENT', 'CRUDE', 'GASOLINE', 'HEATING', 'NATURAL'].includes(symbol)) {
      return AssetType.ENERGY;
    }

    // Forex
    if (symbol.length === 6 && symbol.includes('USD')) {
      return AssetType.FOREX;
    }

    return AssetType.CRYPTOCURRENCY;
  }

  /**
   * Determine appropriate unit for asset
   */
  private determineUnit(record: KgxRecord, assetType: AssetType): string {
    // Use explicit unit if provided
    if (record.unit) {
      return record.unit;
    }

    // Use asset type mapping
    return ASSET_TYPE_UNITS[assetType];
  }

  /**
   * Format timestamp based on provided formats
   */
  private formatTimestamp(
    timestamp: string | number | Date | undefined,
    dateFormat?: string,
    timeFormat?: string
  ): string {
    if (!timestamp) {
      return new Date().toISOString().replace('T', ' ').replace(/\.\d+Z$/, '');
    }

    let date: Date;

    try {
      if (typeof timestamp === 'number') {
        date = new Date(timestamp * 1000);
      } else if (typeof timestamp === 'string') {
        const parsedDate = new Date(timestamp);
        if (isNaN(parsedDate.getTime())) {
          const unixTimestamp = parseInt(timestamp, 10);
          if (!isNaN(unixTimestamp)) {
            date = new Date(unixTimestamp * 1000);
          } else {
            throw new Error('Invalid timestamp format');
          }
        } else {
          date = parsedDate;
        }
      } else {
        date = timestamp;
      }

      if (isNaN(date.getTime())) {
        throw new Error('Invalid time value');
      }

      // Default format
      if (!dateFormat && !timeFormat) {
        return date.toISOString().replace('T', ' ').replace(/\.\d+Z$/, '');
      }

      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      let formattedDate: string;
      if (dateFormat === '1') {
        formattedDate = `${month}/${day}/${year}`;
      } else {
        formattedDate = `${year}-${month}-${day}`;
      }

      const hours = date.getHours();
      const minutes = String(date.getMinutes()).padStart(2, '0');
      const seconds = String(date.getSeconds()).padStart(2, '0');

      let formattedTime: string;
      if (timeFormat === '1') {
        const displayHours = hours === 0 ? 12 : hours > 12 ? hours - 12 : hours;
        const ampm = hours >= 12 ? 'PM' : 'AM';
        formattedTime = `${displayHours}:${minutes}:${seconds} ${ampm}`;
      } else {
        formattedTime = `${String(hours).padStart(2, '0')}:${minutes}:${seconds}`;
      }

      return `${formattedDate} ${formattedTime}`;
    } catch (error) {
      this.logger.warn(`Error formatting timestamp ${timestamp}: ${error.message}`);
      return new Date().toISOString().replace('T', ' ').replace(/\.\d+Z$/, '');
    }
  }
}
