{"run": {"command": "nx run api:serve", "startTime": "2025-05-28T15:18:58.385Z", "endTime": "2025-05-28T15:31:09.023Z", "inner": false}, "tasks": [{"taskId": "api:serve", "target": "serve", "projectName": "api", "hash": "c3ca8fd5780793c282d655c8a67c2231aa3d79bdafe33c1bc28dc4b380face64", "startTime": "2025-05-28T15:18:58.389Z", "endTime": "2025-05-28T15:31:09.022Z", "params": "", "cacheStatus": "cache-miss", "status": 1}]}