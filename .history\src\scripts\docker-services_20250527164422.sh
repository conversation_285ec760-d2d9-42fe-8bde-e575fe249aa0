#!/bin/bash

# Docker Services Management Script
# Simple wrapper around docker-compose for common development tasks

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Check if docker-compose.yml exists
check_compose_file() {
    if [ ! -f "docker-compose.yml" ]; then
        echo -e "${RED}❌ docker-compose.yml not found in current directory${NC}"
        echo -e "${YELLOW}💡 Make sure you're running this from the project root${NC}"
        exit 1
    fi
}

# Function to start services
start_services() {
    echo -e "${BLUE}🚀 Starting all services...${NC}"
    check_compose_file
    
    docker-compose up -d
    
    echo -e "${GREEN}✅ Services started successfully${NC}"
    echo -e "${BLUE}📊 Service status:${NC}"
    docker-compose ps
    
    echo -e "\n${YELLOW}💡 Useful commands:${NC}"
    echo -e "  View logs: ${BLUE}$0 logs${NC}"
    echo -e "  Stop services: ${BLUE}$0 stop${NC}"
    echo -e "  Check status: ${BLUE}$0 status${NC}"
}

# Function to stop services
stop_services() {
    echo -e "${YELLOW}🛑 Stopping all services...${NC}"
    check_compose_file
    
    docker-compose down
    echo -e "${GREEN}✅ Services stopped${NC}"
}

# Function to restart services
restart_services() {
    echo -e "${BLUE}🔄 Restarting all services...${NC}"
    check_compose_file
    
    docker-compose restart
    echo -e "${GREEN}✅ Services restarted${NC}"
    docker-compose ps
}

# Function to show service status
show_status() {
    echo -e "${BLUE}📊 Service Status:${NC}"
    check_compose_file
    
    docker-compose ps
    
    echo -e "\n${BLUE}💾 Resource Usage:${NC}"
    docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $(docker-compose ps -q) 2>/dev/null || echo "No running containers"
}

# Function to show logs
show_logs() {
    local service=${1:-""}
    check_compose_file
    
    if [ -z "$service" ]; then
        echo -e "${BLUE}📋 Showing logs for all services (press Ctrl+C to exit)...${NC}"
        docker-compose logs -f --tail=50
    else
        echo -e "${BLUE}📋 Showing logs for $service (press Ctrl+C to exit)...${NC}"
        docker-compose logs -f --tail=50 "$service"
    fi
}

# Function to execute command in service
exec_service() {
    local service=$1
    shift
    local command="$@"
    
    if [ -z "$service" ]; then
        echo -e "${RED}❌ Service name required${NC}"
        echo -e "${YELLOW}Usage: $0 exec <service> <command>${NC}"
        return 1
    fi
    
    check_compose_file
    
    echo -e "${BLUE}🔧 Executing in $service: $command${NC}"
    docker-compose exec "$service" $command
}

# Function to rebuild and restart a specific service
rebuild_service() {
    local service=$1
    
    if [ -z "$service" ]; then
        echo -e "${RED}❌ Service name required${NC}"
        echo -e "${YELLOW}Usage: $0 rebuild <service>${NC}"
        return 1
    fi
    
    check_compose_file
    
    echo -e "${BLUE}🔨 Rebuilding $service...${NC}"
    docker-compose build "$service"
    
    echo -e "${BLUE}🔄 Restarting $service...${NC}"
    docker-compose up -d "$service"
    
    echo -e "${GREEN}✅ $service rebuilt and restarted${NC}"
    docker-compose ps "$service"
}

# Function to show service health
show_health() {
    echo -e "${BLUE}🏥 Service Health Check:${NC}"
    check_compose_file
    
    # Get running services
    local services=$(docker-compose ps --services --filter status=running)
    
    if [ -z "$services" ]; then
        echo -e "${YELLOW}⚠️  No services are currently running${NC}"
        return
    fi
    
    for service in $services; do
        local container=$(docker-compose ps -q "$service")
        if [ ! -z "$container" ]; then
            local health=$(docker inspect --format='{{.State.Health.Status}}' "$container" 2>/dev/null || echo "no-healthcheck")
            local status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null || echo "unknown")
            
            case $health in
                "healthy")
                    echo -e "  ${GREEN}✅ $service: $status ($health)${NC}"
                    ;;
                "unhealthy")
                    echo -e "  ${RED}❌ $service: $status ($health)${NC}"
                    ;;
                "starting")
                    echo -e "  ${YELLOW}🔄 $service: $status ($health)${NC}"
                    ;;
                *)
                    echo -e "  ${BLUE}ℹ️  $service: $status${NC}"
                    ;;
            esac
        fi
    done
}

# Main execution
main() {
    local command=${1:-"status"}
    local service_name=${2:-""}
    
    case $command in
        "start"|"up")
            start_services
            ;;
        "stop"|"down")
            stop_services
            ;;
        "restart")
            restart_services
            ;;
        "status"|"ps")
            show_status
            ;;
        "logs")
            show_logs "$service_name"
            ;;
        "exec")
            shift
            exec_service "$@"
            ;;
        "rebuild")
            rebuild_service "$service_name"
            ;;
        "health")
            show_health
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $command${NC}"
            echo "Usage: $0 [command] [service_name]"
            echo ""
            echo "Service Management:"
            echo "  start, up         - Start all services"
            echo "  stop, down        - Stop all services"
            echo "  restart           - Restart all services"
            echo "  status, ps        - Show service status and resource usage"
            echo "  logs [service]    - Show logs (all services or specific service)"
            echo "  health            - Show service health status"
            echo ""
            echo "Development:"
            echo "  exec <service> <command>  - Execute command in service container"
            echo "  rebuild <service>         - Rebuild and restart specific service"
            echo ""
            echo "Examples:"
            echo "  $0 start                  - Start all services"
            echo "  $0 logs api               - Show API service logs"
            echo "  $0 exec api bash          - Open bash in API container"
            echo "  $0 rebuild collector      - Rebuild collector service"
            exit 1
            ;;
    esac
    
    echo -e "${GREEN}🎉 Operation completed successfully!${NC}"
}

# Run main function
main "$@" 