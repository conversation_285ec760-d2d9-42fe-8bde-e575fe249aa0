# Environment Switching Guide

This document explains how to switch between different development environments for the microservices architecture.

## Problem Solved

Previously, the health check endpoint was hardcoded to use Docker container names (`http://scheduler:3001`, `http://collector:3002`, etc.), which caused failures when running services locally in development mode. The health check would show services as "down" with `getaddrinfo ENOTFOUND` errors.

## Solution

The health check system now uses environment-aware service URLs that automatically adapt based on:
1. `NODE_ENV` environment variable
2. Specific service URL environment variables
3. Fallback defaults based on the environment

## Environment Files

### `.env.local` - Local Development
- All services running on localhost
- Databases on localhost
- Perfect for running services with npm/yarn/node

### `.env.docker` - Docker Development  
- Services using Docker container names
- Databases in Docker containers
- Perfect for docker-compose development

### `.env.dev` - Standard Development
- Same as local development
- Standard development configuration

### `.env.production` - Production
- Container-based service URLs
- Production database configurations

## Quick Environment Switching

### Using Scripts (Recommended)

**Linux/Mac:**
```bash
# Switch to local development (all services on localhost)
./scripts/switch-env.sh local

# Switch to Docker development (services in containers)
./scripts/switch-env.sh docker

# Switch to production
./scripts/switch-env.sh production

# Show current environment
./scripts/switch-env.sh status
```

**Windows PowerShell:**
```powershell
# Switch to local development
.\scripts\switch-env.ps1 local

# Switch to Docker development
.\scripts\switch-env.ps1 docker

# Switch to production
.\scripts\switch-env.ps1 production

# Show current environment
.\scripts\switch-env.ps1 status
```

### Manual Switching

Copy the desired environment file to `.env`:

```bash
# For local development
cp .env.local .env

# For Docker development
cp .env.docker .env

# For production
cp .env.production .env
```

## Environment Variables

The health check system looks for these environment variables in order:

1. **Specific service URLs:**
   - `SCHEDULER_URL`
   - `COLLECTOR_URL` 
   - `PROCESSOR_URL`
   - `WRITER_URL`

2. **Service-specific environment variables:**
   - `SCHEDULER_SERVICE_URL`
   - `COLLECTOR_SERVICE_URL`
   - `PROCESSOR_SERVICE_URL`
   - `WRITER_SERVICE_URL`

3. **Environment-aware defaults:**
   - Development: `http://localhost:{port}`
   - Production: `http://{service-name}:{port}`

## Health Check Implementation

The health check controller now includes a `getServiceUrl()` method that:

```typescript
private getServiceUrl(serviceName: string, defaultPort: number): string {
  const isDevelopment = process.env.NODE_ENV === "development";
  
  // Check for specific environment variable first
  const envVarName = `${serviceName.toUpperCase()}_URL`;
  const envUrl = process.env[envVarName];
  
  if (envUrl) {
    return envUrl;
  }
  
  // Fallback to environment-aware defaults
  if (isDevelopment) {
    return `http://localhost:${defaultPort}`;
  } else {
    // Production: use container names or service-specific env vars
    const serviceUrlEnvVar = `${serviceName.toUpperCase()}_SERVICE_URL`;
    return process.env[serviceUrlEnvVar] || `http://${serviceName}:${defaultPort}`;
  }
}
```

## Testing the Fix

After switching environments, test the health endpoint:

```bash
curl http://localhost:3000/api/health
```

**Expected result in local mode:**
```json
{
  "status": "up",
  "services": {
    "collector": { "status": "up", "error": null },
    "processor": { "status": "up", "error": null },
    "writer": { "status": "up", "error": null },
    "scheduler": { "status": "up", "error": null }
  }
}
```

## Development Workflows

### Local Development Workflow
1. Run `./scripts/switch-env.sh local`
2. Start databases locally (MySQL, PostgreSQL, Redis)
3. Start services with `npm run start:api`, `npm run start:collector`, etc.
4. All services communicate via localhost

### Docker Development Workflow  
1. Run `./scripts/switch-env.sh docker`
2. Start with `docker-compose up`
3. Services communicate via Docker network using container names

### Mixed Development Workflow
1. Start some services in Docker: `docker-compose up mysql postgres redis`
2. Use local environment: `./scripts/switch-env.sh local`
3. Start specific services locally: `npm run start:api`
4. API connects to local services, databases in Docker

## Troubleshooting

### Services showing as "down"
1. Check if you're using the correct environment file
2. Verify services are actually running on the expected ports
3. Check firewall/network connectivity

### Environment not switching
1. Ensure you're copying the right `.env.*` file to `.env`
2. Restart services after switching environments
3. Check that environment variables are being loaded correctly

### Script permissions (Linux/Mac)
```bash
chmod +x scripts/switch-env.sh
```

## Benefits

1. **Flexible Development:** Easy switching between local and Docker development
2. **Environment Consistency:** Same codebase works in all environments
3. **Debugging Friendly:** Can run individual services locally while others in Docker
4. **Production Ready:** Seamless deployment with container-based URLs
5. **Health Monitoring:** Accurate health checks in all environments
