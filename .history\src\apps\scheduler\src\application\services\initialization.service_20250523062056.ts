import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { getErrorMessage, getErrorStack } from "@data-pipeline/core";
import { MySQLService, PostgresService } from "@data-pipeline/storage";

/**
 * Service responsible for coordinating initialization of application components
 * This ensures that components are initialized in the correct order
 */
@Injectable()
export class InitializationService implements OnModuleInit {
  private readonly logger = new Logger(InitializationService.name);
  private initialized = false;

  constructor(
    private readonly mysqlService: MySQLService,
    private readonly postgresService: PostgresService
  ) {}

  /**
   * Initialize the application
   * This is called automatically by NestJS when the module is initialized
   */
  async onModuleInit() {
    await this.initialize();
  }

  /**
   * Initialize the application components
   * This method can be called manually if needed
   */
  async initialize(): Promise<void> {
    if (this.initialized) {
      return;
    }

    this.logger.log("Starting Scheduler application initialization...");

    try {
      this.logger.log("Waiting for database services to be ready...");

      await new Promise((resolve) => setTimeout(resolve, 1000));

      this.logger.log("Scheduler application initialization completed successfully");
      this.initialized = true;
    } catch (error) {
      this.logger.error(
        `Scheduler application initialization failed: ${getErrorMessage(error as Error)}`,
        getErrorStack(error as Error)
      );
    }
  }

  /**
   * Check if the application is initialized
   */
  isInitialized(): boolean {
    return this.initialized;
  }
}
