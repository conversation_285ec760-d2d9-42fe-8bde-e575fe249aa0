{"version": "5.1", "nxVersion": "16.0.0", "deps": {"@aws-sdk/client-s3": "^3.782.0", "@elastic/elasticsearch": "^8.17.1", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.1.0", "@nestjs/microservices": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@opentelemetry/auto-instrumentations-node": "^0.40.0", "@opentelemetry/exporter-trace-otlp-http": "^0.45.0", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-node": "^0.45.0", "@opentelemetry/semantic-conventions": "^1.18.1", "@types/eventsource": "^3.0.0", "@types/node-fetch": "^2.6.12", "@types/node-schedule": "^2.1.7", "ajv": "^8.17.1", "amqplib": "^0.10.7", "axios": "^1.7.7", "better-sqlite3": "^11.9.1", "bull": "^4.10.0", "bullmq": "^3.15.0", "cache-manager": "^6.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dotenv": "^16.0.0", "eventsource": "^3.0.6", "fast-csv": "^5.0.2", "ioredis": "^5.6.0", "joi": "^17.9.0", "jq": "^1.7.2", "kafkajs": "^2.2.0", "keyv": "^5.1.0", "moment-timezone": "^0.5.48", "mongodb": "^6.15.0", "mqtt": "^5.10.4", "mssql": "^11.0.1", "mysql2": "^3.2.0", "node-schedule": "^2.1.1", "oracledb": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.15.6", "pino": "^8.11.0", "pino-http": "^8.3.1", "prom-client": "^14.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "socket.io": "^4.8.1", "sqlite": "^5.1.1", "sqlite3": "^5.1.7", "ssh2-sftp-client": "^12.0.0", "tslib": "^2.8.1", "typeorm": "^0.3.0", "uuid": "^11.1.0", "ws": "^8.18.1", "xml2js": "^0.6.2", "yahoo-finance2": "^2.13.3", "yaml": "^2.2.0", "@faker-js/faker": "^9.6.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@nrwl/eslint-plugin-nx": "16.0.0", "@nrwl/jest": "16.0.0", "@nrwl/js": "16.0.0", "@nrwl/linter": "16.0.0", "@nrwl/nest": "16.0.0", "@nrwl/node": "16.0.0", "@nrwl/nx-cloud": "16.0.0", "@nrwl/webpack": "^19.8.4", "@nrwl/workspace": "16.0.0", "@opentelemetry/api": "~1.7.0", "@types/amqplib": "^0.10.7", "@types/better-sqlite3": "^7.6.13", "@types/bull": "^4.10.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mssql": "^9.1.7", "@types/node": "^18.0.0", "@types/oracledb": "^6.5.4", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.11.14", "@types/sqlite3": "^5.1.0", "@types/ssh2-sftp-client": "^9.0.4", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "nodemon": "^3.0.2", "nx": "16.0.0", "pino-pretty": "^13.0.0", "prettier": "^2.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.0.0", "ts-node": "^10.0.0", "ts-node-dev": "^2.0.0", "tsconfig-paths": "^4.0.0", "typescript": "^5.0.0", "webpack": "^5.0.0"}, "pathMappings": {"@data-pipeline/cache": ["libs/cache/src/index.ts"], "@data-pipeline/config": ["libs/config/src/index.ts"], "@data-pipeline/core": ["libs/core/src/index.ts"], "@data-pipeline/kgx": ["libs/kgx/src/index.ts"], "@data-pipeline/logging": ["libs/logging/src/index.ts"], "@data-pipeline/messaging": ["libs/messaging/src/index.ts"], "@data-pipeline/monitoring": ["libs/monitoring/src/index.ts"], "@data-pipeline/security": ["libs/security/src/index.ts"], "@data-pipeline/storage": ["libs/storage/src/index.ts"], "@data-pipeline/utils": ["libs/utils/src/index.ts"]}, "nxJsonPlugins": [], "nodes": {"monitoring": {"name": "monitoring", "type": "lib", "data": {"name": "monitoring", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/monitoring/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/monitoring", "tsConfig": "libs/monitoring/tsconfig.lib.json", "packageJson": "libs/monitoring/package.json", "main": "libs/monitoring/src/index.ts", "assets": ["libs/monitoring/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/monitoring/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/monitoring"], "options": {"jestConfig": "libs/monitoring/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/monitoring", "implicitDependencies": [], "files": [{"file": "libs/monitoring/project.json", "hash": "1333472025203474587"}, {"file": "libs/monitoring/src/index.ts", "hash": "5750645048090497952"}, {"file": "libs/monitoring/src/lib/health/health.controller.ts", "hash": "15997026507262866882", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/health/health.module.ts", "hash": "370356888534331922", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/health/health.service.ts", "hash": "3612702439516287638", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/health/index.ts", "hash": "16089615334936254981"}, {"file": "libs/monitoring/src/lib/logging/index.ts", "hash": "12549523785338334948"}, {"file": "libs/monitoring/src/lib/logging/logging.interceptor.ts", "hash": "16349256652750902335", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}, {"target": "npm:rxjs", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/logging/logging.interfaces.ts", "hash": "10830653018907440133"}, {"file": "libs/monitoring/src/lib/logging/logging.module.ts", "hash": "14574870540691309022", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}, {"target": "npm:@nestjs/core", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/logging/logging.service.ts", "hash": "1944344568156138085", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}, {"target": "npm:pino", "source": "monitoring", "type": "static"}, {"target": "npm:pino-http", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/metrics/index.ts", "hash": "4259274204969146314"}, {"file": "libs/monitoring/src/lib/metrics/metrics.controller.ts", "hash": "14109418957052029654", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/metrics/metrics.module.ts", "hash": "16437815481203307454", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/metrics/metrics.service.ts", "hash": "5087468702375407684", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}, {"target": "npm:prom-client", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/monitoring.module.ts", "hash": "6667442080426696003", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/tracing/index.ts", "hash": "3925628370491705445"}, {"file": "libs/monitoring/src/lib/tracing/tracing.interfaces.ts", "hash": "16821137431739899651"}, {"file": "libs/monitoring/src/lib/tracing/tracing.module.ts", "hash": "7105867422994142108", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/src/lib/tracing/tracing.service.ts", "hash": "18413462868341126644", "dependencies": [{"target": "npm:@nestjs/common", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/sdk-node", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/auto-instrumentations-node", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/exporter-trace-otlp-http", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/resources", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/semantic-conventions", "source": "monitoring", "type": "static"}, {"target": "npm:@opentelemetry/api", "source": "monitoring", "type": "static"}]}, {"file": "libs/monitoring/tsconfig.json", "hash": "4906954849963730315"}, {"file": "libs/monitoring/tsconfig.lib.json", "hash": "7558601114777799463"}]}}, "collector": {"name": "collector", "type": "app", "data": {"name": "collector", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/collector/src", "projectType": "application", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/collector", "main": "apps/collector/src/main.ts", "tsConfig": "apps/collector/tsconfig.app.json", "assets": ["apps/collector/src/assets"], "webpackConfig": "apps/collector/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/collector/src/environments/environment.ts", "with": "apps/collector/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:node", "options": {"buildTarget": "collector:build", "port": 9230}, "configurations": {"production": {"buildTarget": "collector:build:production"}}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/collector/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/collector"], "options": {"jestConfig": "apps/collector/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "apps/collector", "implicitDependencies": [], "files": [{"file": "apps/collector/Dockerfile", "hash": "15542934577693865055"}, {"file": "apps/collector/Dockerfile.dev", "hash": "6950740346036419596"}, {"file": "apps/collector/entrypoint.dev.sh", "hash": "9477252199897006511"}, {"file": "apps/collector/project.json", "hash": "3506939815039563050"}, {"file": "apps/collector/README-SIMPLIFIED.md", "hash": "7877957918340795326"}, {"file": "apps/collector/README.md", "hash": "12470041247893622169"}, {"file": "apps/collector/src/app.module.ts", "hash": "9433074313372061454", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/config", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "collector", "type": "static"}, {"target": "logging", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "collector", "type": "static"}, {"target": "security", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/core", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/application/application.module.ts", "hash": "12155853939336322995", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/application/handlers/collection-job.handler.ts", "hash": "6932569781844042565", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/application/services/collection.service.ts", "hash": "5915976514833145142", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/application/services/initialization.service.ts", "hash": "14165784654476340907", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/application/services/workflow-collector.service.ts", "hash": "2462694699424962119", "dependencies": [{"target": "messaging", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/collector/src/domain/collectors/collector.interface.ts", "hash": "5627981072850566762", "dependencies": [{"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/database.collector.ts", "hash": "16534284935717242180", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "npm:mysql2", "source": "collector", "type": "static"}, {"target": "npm:pg", "source": "collector", "type": "static"}, {"target": "npm:mssql", "source": "collector", "type": "static"}, {"target": "npm:oracledb", "source": "collector", "type": "static"}, {"target": "npm:better-sqlite3", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/file.collector.ts", "hash": "16529760760993092944", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:xml2js", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "npm:csv-parser", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/graphql.collector.ts", "hash": "12188232546795703506", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}, {"target": "npm:rxjs", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/http.collector.ts", "hash": "5366532131926119485", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}, {"target": "npm:rxjs", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/index.ts", "hash": "9775353114303507431"}, {"file": "apps/collector/src/domain/collectors/rest-api.collector.ts", "hash": "10313653095210011636", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}, {"target": "npm:rxjs", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/sftp.collector.ts", "hash": "14672120154633325205", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:csv-parser", "source": "collector", "type": "static"}, {"target": "npm:ssh2-sftp-client", "source": "collector", "type": "static"}, {"target": "npm:xml2js", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/stream.collector.ts", "hash": "10187510937169824032", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:kafkajs", "source": "collector", "type": "static"}, {"target": "npm:amqplib", "source": "collector", "type": "static"}, {"target": "npm:i<PERSON>is", "source": "collector", "type": "static"}, {"target": "npm:mqtt", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/collectors/websocket.collector.ts", "hash": "8334941169137668667", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "npm:ws", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/domain.module.ts", "hash": "12387300384412519614", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "logging", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/factories/collector.factory.ts", "hash": "6480618444861376783", "dependencies": [{"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/collector-plugin.interface.ts", "hash": "16165663553854333876", "dependencies": [{"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/base-metals-data.handler.ts", "hash": "5089704332358410514", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/crypto-data.handler.ts", "hash": "11616839277345802902", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/energy-data.handler.ts", "hash": "3118066795436987109", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/forex-data.handler.ts", "hash": "2118735270196568577", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/market-indices-data.handler.ts", "hash": "7960347304725963826", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "npm:axios", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/handlers/precious-metals-data.handler.ts", "hash": "14888531120464407833", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/interfaces/collection-trigger.interface.ts", "hash": "953042955401876988"}, {"file": "apps/collector/src/domain/plugins/kgx/interfaces/data-type-handler.interface.ts", "hash": "7727798552470634003", "dependencies": [{"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/plugins/kgx/interfaces/kgx-feature-flags.interface.ts", "hash": "14060829929362801349"}, {"file": "apps/collector/src/domain/plugins/kgx/kgx.plugin.ts", "hash": "13540792563865344807", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}, {"target": "npm:rxjs", "source": "collector", "type": "static"}, {"target": "npm:uuid", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/services/collector.service.ts", "hash": "4758236361454710364", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/services/plugin-registry.service.ts", "hash": "1969014426766008574", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/types/collection-metadata.type.ts", "hash": "15467671548718218162", "dependencies": [{"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/domain/types/collector-service-dependencies.type.ts", "hash": "18123327999323404074", "dependencies": [{"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/adapters/adapters.module.ts", "hash": "8027188614783245424", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/adapters/collector.adapter.ts", "hash": "1376177052308055080", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/config/config.module.ts", "hash": "16195144512492086620", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/config/config.service.ts", "hash": "17089222069759702271", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/config", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/database/database.module.ts", "hash": "4463537813104953921", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/database/repositories/base/generic-repository.base.ts", "hash": "16574719021582903699", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/database/services/mysql.service.ts", "hash": "8811913536802386409", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/health/database-health.service.ts", "hash": "2420087028200687460", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "storage", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/health/health.module.ts", "hash": "16328800825860223374", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/infrastructure.module.ts", "hash": "16649725490503519382", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/messaging/messaging.module.ts", "hash": "7213933455129193955", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "messaging", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/utils/error-handler.util.ts", "hash": "9294963430124219961", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/utils/index.ts", "hash": "16837720616510776847"}, {"file": "apps/collector/src/infrastructure/utils/repository-error.utils.ts", "hash": "8862878229477422648", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/infrastructure/utils/retry.util.ts", "hash": "13030239793969910655", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/main.ts", "hash": "7239764937902063506", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "npm:@nestjs/core", "source": "collector", "type": "static"}, {"target": "messaging", "source": "collector", "type": "static"}, {"target": "utils", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/controllers/collection.controller.ts", "hash": "10980099126151609779", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/controllers/collector.controller.ts", "hash": "5644367460705603326", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/controllers/health.controller.ts", "hash": "12387832551037759947", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}, {"target": "security", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/dtos/collection.dto.ts", "hash": "9092458149225781728", "dependencies": [{"target": "npm:class-validator", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/dtos/collector.dto.ts", "hash": "7487257968363099330", "dependencies": [{"target": "npm:class-validator", "source": "collector", "type": "static"}, {"target": "npm:class-transformer", "source": "collector", "type": "static"}, {"target": "core", "source": "collector", "type": "static"}]}, {"file": "apps/collector/src/presentation/presentation.module.ts", "hash": "5620119046697526761", "dependencies": [{"target": "npm:@nestjs/common", "source": "collector", "type": "static"}]}, {"file": "apps/collector/tsconfig.app.json", "hash": "9873971304957799460"}, {"file": "apps/collector/webpack.config.js", "hash": "15543519630485635912", "dependencies": [{"target": "npm:@nrwl/webpack", "source": "collector", "type": "static"}]}]}}, "vite_react_shadcn_ts": {"name": "vite_react_shadcn_ts", "type": "app", "data": {"root": "apps/dashboard", "sourceRoot": "apps/dashboard", "projectType": "application", "targets": {"dev": {"executor": "nx:run-script", "options": {"script": "dev"}}, "build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "nx:run-script", "options": {"script": "build"}, "configurations": {}}, "build:dev": {"executor": "nx:run-script", "options": {"script": "build:dev"}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "nx:run-script", "options": {"script": "lint"}, "configurations": {}}, "preview": {"executor": "nx:run-script", "options": {"script": "preview"}}, "stop": {"executor": "nx:run-script", "options": {"script": "stop"}}}, "implicitDependencies": [], "tags": [], "files": [{"file": "apps/dashboard/.gitignore", "hash": "5147386025172092799"}, {"file": "apps/dashboard/bun.lockb", "hash": "5494380259846882032"}, {"file": "apps/dashboard/components.json", "hash": "10581306706250207358"}, {"file": "apps/dashboard/Dockerfile", "hash": "11742717533828446404"}, {"file": "apps/dashboard/Dockerfile.dev", "hash": "14800103050595248314"}, {"file": "apps/dashboard/eslint.config.js", "hash": "13219544335505634730"}, {"file": "apps/dashboard/index.html", "hash": "13413797221774083503"}, {"file": "apps/dashboard/package.json", "hash": "7181621111024671679", "dependencies": [{"target": "npm:date-fns", "source": "vite_react_shadcn_ts", "type": "static"}, {"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}, {"target": "npm:react-dom", "source": "vite_react_shadcn_ts", "type": "static"}, {"target": "npm:@types/node", "source": "vite_react_shadcn_ts", "type": "static"}, {"target": "npm:eslint", "source": "vite_react_shadcn_ts", "type": "static"}, {"target": "npm:typescript", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/postcss.config.js", "hash": "1121830044826443979"}, {"file": "apps/dashboard/public/favicon.ico", "hash": "4418485421293566786"}, {"file": "apps/dashboard/public/placeholder.svg", "hash": "8334994000532407949"}, {"file": "apps/dashboard/public/robots.txt", "hash": "12805369797289910699"}, {"file": "apps/dashboard/README.md", "hash": "4686164053509472426"}, {"file": "apps/dashboard/src/App.css", "hash": "3739391659270974612"}, {"file": "apps/dashboard/src/App.tsx", "hash": "451571037173704488"}, {"file": "apps/dashboard/src/components/crypto/CryptoTable.tsx", "hash": "3581580651258701596", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/kgx/FormulaDisplay.tsx", "hash": "1723553699744406667", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/kgx/FormulaVisualizer.tsx", "hash": "18359521040849119262", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/layout/DashboardLayout.tsx", "hash": "1529359164616542319", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/layout/Navbar.tsx", "hash": "5235956282808830892", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/layout/Sidebar.tsx", "hash": "10115830005188948292", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/theme/ModeToggle.tsx", "hash": "14584372100977339621", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/theme/ThemeProvider.tsx", "hash": "13254087979298500921", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/accordion.tsx", "hash": "3167988994528357415", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/alert-dialog.tsx", "hash": "12931732376882220959", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/alert.tsx", "hash": "1861621377426642757", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/aspect-ratio.tsx", "hash": "12340592216594228921"}, {"file": "apps/dashboard/src/components/ui/avatar.tsx", "hash": "2172290189339051182", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/badge.tsx", "hash": "8406134610660828449", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/breadcrumb.tsx", "hash": "5977059709950181256", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/button.tsx", "hash": "11257389123344437734", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/calendar.tsx", "hash": "16761397970850291754", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/card.tsx", "hash": "9572552624677261021", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/carousel.tsx", "hash": "16025284428382211109", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/chart.tsx", "hash": "3593573621340112929", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/checkbox.tsx", "hash": "5277313382263280533", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/collapsible.tsx", "hash": "8040382521055521536"}, {"file": "apps/dashboard/src/components/ui/command.tsx", "hash": "3943851909728919160", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/context-menu.tsx", "hash": "9897002208018036120", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/dialog.tsx", "hash": "5629480931371809090", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/drawer.tsx", "hash": "506114188505693642", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/dropdown-menu.tsx", "hash": "171502028778230976", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/form.tsx", "hash": "15490598094551137363", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/hover-card.tsx", "hash": "14584555480307996271", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/input-otp.tsx", "hash": "14550022367864184989", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/input.tsx", "hash": "667422176216627357", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/label.tsx", "hash": "575553755677390382", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/menubar.tsx", "hash": "3589615094413232561", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/navigation-menu.tsx", "hash": "13877511562904234290", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/pagination.tsx", "hash": "12933609959293049490", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/popover.tsx", "hash": "11274085998591564428", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/progress.tsx", "hash": "7572288392203238062", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/radio-group.tsx", "hash": "12574634384370595430", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/resizable.tsx", "hash": "7545385077443522790"}, {"file": "apps/dashboard/src/components/ui/scroll-area.tsx", "hash": "7372559361306884482", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/select.tsx", "hash": "18172787381250052828", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/separator.tsx", "hash": "9152929968593344539", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/sheet.tsx", "hash": "12694553622966559155", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/sidebar.tsx", "hash": "17178065947406570189", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/skeleton.tsx", "hash": "7739192301396167804"}, {"file": "apps/dashboard/src/components/ui/slider.tsx", "hash": "13517559721624315127", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/sonner.tsx", "hash": "8069787631781441444"}, {"file": "apps/dashboard/src/components/ui/switch.tsx", "hash": "6768244898163120696", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/table.tsx", "hash": "12220893068497995597", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/tabs.tsx", "hash": "13475387194805012451", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/textarea.tsx", "hash": "17888994061576480359", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/toast.tsx", "hash": "1326391758004731988", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/toaster.tsx", "hash": "16997361785471586676"}, {"file": "apps/dashboard/src/components/ui/toggle-group.tsx", "hash": "7273051652050101833", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/toggle.tsx", "hash": "11317299348690723909", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/tooltip.tsx", "hash": "13508833649960061832", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/components/ui/use-toast.ts", "hash": "11206778307900572536"}, {"file": "apps/dashboard/src/config/api.config.ts", "hash": "9650408237624779172"}, {"file": "apps/dashboard/src/contexts/CryptoContext.tsx", "hash": "4645520890640672056", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/contexts/CryptoContextDefinition.ts", "hash": "1838368964270524537", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/contexts/CryptoState.ts", "hash": "233790554213343263"}, {"file": "apps/dashboard/src/contexts/FormulaContextDefinition.ts", "hash": "13620214403791875634", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/contexts/FormulaState.ts", "hash": "13617614265680173731"}, {"file": "apps/dashboard/src/contexts/useCryptoContext.ts", "hash": "18072844509542260712", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/hooks/use-mobile.tsx", "hash": "14900138732519213047", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/hooks/use-toast.ts", "hash": "16882284538177563600", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/hooks/useApi.ts", "hash": "8434335417273382899", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/index.css", "hash": "15645565304938737701"}, {"file": "apps/dashboard/src/lib/utils.ts", "hash": "4172178976833894552"}, {"file": "apps/dashboard/src/main.tsx", "hash": "11623424698078024436", "dependencies": [{"target": "npm:react-dom", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/pages/CryptoData.tsx", "hash": "11752629479482327686", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/pages/Index.tsx", "hash": "13950838120286844152"}, {"file": "apps/dashboard/src/pages/KgxFormula.tsx", "hash": "15611060251401239396", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/pages/KgxFormulaConfiguration.tsx", "hash": "13748174930800851063", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/pages/NotFound.tsx", "hash": "44763568098644782", "dependencies": [{"target": "npm:react", "source": "vite_react_shadcn_ts", "type": "static"}]}, {"file": "apps/dashboard/src/services/api.service.ts", "hash": "2338757029686171584"}, {"file": "apps/dashboard/src/types/api.types.ts", "hash": "8411503899592056000"}, {"file": "apps/dashboard/src/types/global.d.ts", "hash": "13606900833365254110"}, {"file": "apps/dashboard/src/vite-env.d.ts", "hash": "12946363841406869089"}, {"file": "apps/dashboard/tailwind.config.ts", "hash": "18365495824448314029"}, {"file": "apps/dashboard/tsconfig.app.json", "hash": "13264594803095487797"}, {"file": "apps/dashboard/tsconfig.json", "hash": "12572075090769222941"}, {"file": "apps/dashboard/tsconfig.node.json", "hash": "15419727030014400781"}, {"file": "apps/dashboard/vite.config.ts", "hash": "5409725136490336003"}]}}, "processor": {"name": "processor", "type": "app", "data": {"name": "processor", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/processor/src", "projectType": "application", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/processor", "main": "apps/processor/src/main.ts", "tsConfig": "apps/processor/tsconfig.app.json", "assets": ["apps/processor/src/assets"], "webpackConfig": "apps/processor/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/processor/src/environments/environment.ts", "with": "apps/processor/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/js:node", "options": {"buildTarget": "processor:build", "port": 9231}, "configurations": {"production": {"buildTarget": "processor:build:production"}}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/processor/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/processor"], "options": {"jestConfig": "apps/processor/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "apps/processor", "implicitDependencies": [], "files": [{"file": "apps/processor/Dockerfile", "hash": "**********973380310"}, {"file": "apps/processor/Dockerfile.dev", "hash": "14227711357446533731"}, {"file": "apps/processor/project.json", "hash": "13696680665962897707"}, {"file": "apps/processor/README.md", "hash": "13274161932286188057"}, {"file": "apps/processor/src/app.module.ts", "hash": "2700063209245190949", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}, {"target": "security", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/application/application.module.ts", "hash": "12702467647926004636", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/application/errors/processing.error.ts", "hash": "18350985070657358278"}, {"file": "apps/processor/src/application/handlers/processing-job.handler.ts", "hash": "13748114574190068705", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/application/services/initialization.service.ts", "hash": "11551979583053460305", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/application/services/processing.service.ts", "hash": "5928783264275243197", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/application/services/workflow-processor.service.ts", "hash": "8571115585931620414", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "messaging", "source": "processor", "type": "static"}, {"target": "utils", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/processor/src/config/market-hours.config.ts", "hash": "16121082906320854178"}, {"file": "apps/processor/src/config/usdx-method.config.ts", "hash": "10248450514208333461"}, {"file": "apps/processor/src/domain/domain.module.ts", "hash": "5677335560641267545", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/entities/processing-pipeline.entity.ts", "hash": "13175488893119083454", "dependencies": [{"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/entities/processing-result.entity.ts", "hash": "6996496907455182182", "dependencies": [{"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/clients/crypto-api.ts", "hash": "8042515505892748251", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}, {"target": "npm:rxjs", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/clients/forex-client.ts", "hash": "12932074018191241056", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}, {"target": "npm:rxjs", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/clients/hades-client.ts", "hash": "13998991008695971814", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "npm:pg", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/clients/kds2-client.ts", "hash": "17169833392495307781", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:axios", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/clients/usd-index-client.ts", "hash": "1991596161660584460", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}, {"target": "npm:typeorm", "source": "processor", "type": "static"}, {"target": "npm:rxjs", "source": "processor", "type": "static"}, {"target": "npm:uuid", "source": "processor", "type": "static"}, {"target": "npm:axios", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/config/crypto-symbols-config.ts", "hash": "7959359772251175292", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:typeorm", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/config/kgx-config.ts", "hash": "6442654092366880489", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:typeorm", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/kgx.plugin.ts", "hash": "833849535126842000", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/kgx.processor.ts", "hash": "15728015949606053631", "dependencies": [{"source": "processor", "target": "npm:@nestjs/common", "type": "static"}, {"source": "processor", "target": "npm:uuid", "type": "static"}, {"source": "processor", "target": "core", "type": "static"}, {"source": "processor", "target": "storage", "type": "static"}, {"source": "processor", "target": "kgx", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/README.md", "hash": "5859068741737256687"}, {"file": "apps/processor/src/domain/plugins/kgx/services/historical-data.service.ts", "hash": "6639166510487173719", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:typeorm", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "npm:date-fns", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/services/kdxy-calculator.service.ts", "hash": "18400909844608671005", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/services/kgx-calculator.service.ts", "hash": "5777245593899215896", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/services/kgx-crypto-pipeline.service.ts", "hash": "15457282395981679053", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/services/market-hours.service.ts", "hash": "16078813778663427375", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:moment-timezone", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/services/usd-index.service.ts", "hash": "5298114747686781297", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:typeorm", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/transformers/crypto-data-transformer.ts", "hash": "10792835854915431811", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/transformers/data-transformer.ts", "hash": "6220069670002129620", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/transformers/forex-data-transformer.ts", "hash": "10595100263857706103", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/transformers/market-indices-data-transformer.ts", "hash": "10056765750843224797", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/transformers/precious-metals-data-transformer.ts", "hash": "14707569618207861646", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/utils/external-fallback.util.ts", "hash": "1867228745192934986", "dependencies": [{"target": "npm:axios", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/kgx/utils/yahoo.ts", "hash": "10921827222206218230", "dependencies": [{"target": "npm:yahoo-finance2", "source": "processor", "type": "static"}, {"target": "npm:date-fns", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/plugins/processor-plugin.interface.ts", "hash": "892605333131071433"}, {"file": "apps/processor/src/domain/plugins/processor-plugin.registry.ts", "hash": "13510119554849761203", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/processors/aggregation.processor.ts", "hash": "16146756335073438190", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/processors/filter.processor.ts", "hash": "11950734046826887790", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/processors/index.ts", "hash": "13262940868042786990"}, {"file": "apps/processor/src/domain/processors/normalization.processor.ts", "hash": "1370883171761381941", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/processors/processor.interface.ts", "hash": "12723815795912987925", "dependencies": [{"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/processors/transformation.processor.ts", "hash": "18154141522472865735", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/services/pipeline.service.ts", "hash": "1324748279138181114", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/domain/services/processor.service.ts", "hash": "4825611064231041683", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:uuid", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/adapters/adapters.module.ts", "hash": "4446554232300499739", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/adapters/processor.adapter.ts", "hash": "3499811856074172863", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "npm:uuid", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/clients/clients.module.ts", "hash": "859756561630328828", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/clients/collector-client.service.ts", "hash": "10223597656521149403", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "processor", "type": "static"}, {"target": "npm:rxjs", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/config/config.module.ts", "hash": "16195144512492086620", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/config/config.service.ts", "hash": "12269909175397662374", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/database/database.module.ts", "hash": "8392558341256742256", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/config", "source": "processor", "type": "static"}, {"target": "kgx", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/database/migrations/1700000000000-create-usdx-tables.ts", "hash": "5169710984284549239", "dependencies": [{"target": "npm:typeorm", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/database/repositories/base/generic-repository.base.ts", "hash": "360956158852493171", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/database/services/database.service.ts", "hash": "10008433060862669683", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/health/database-health.service.ts", "hash": "4803649043535540384", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/health/health.module.ts", "hash": "7559576890551129727", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/infrastructure.module.ts", "hash": "5154558556079895145", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/infrastructure/messaging/messaging.module.ts", "hash": "3617008149757242412", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "logging", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "processor", "type": "static"}, {"target": "messaging", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/main.ts", "hash": "16632504145761132676", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:@nestjs/core", "source": "processor", "type": "static"}, {"target": "messaging", "source": "processor", "type": "static"}, {"target": "utils", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/controllers/health.controller.ts", "hash": "8219458553825593171", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/controllers/pipeline.controller.ts", "hash": "13677465011001673824", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "npm:uuid", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/controllers/processing.controller.ts", "hash": "9727518742732298951", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/dtos/pipeline.dto.ts", "hash": "9421163408679867798", "dependencies": [{"target": "npm:class-validator", "source": "processor", "type": "static"}, {"target": "npm:class-transformer", "source": "processor", "type": "static"}, {"target": "core", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/dtos/processing-result.dto.ts", "hash": "2724447180784529508", "dependencies": [{"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/dtos/processing.dto.ts", "hash": "2741877095815672243", "dependencies": [{"target": "npm:class-validator", "source": "processor", "type": "static"}, {"target": "storage", "source": "processor", "type": "static"}]}, {"file": "apps/processor/src/presentation/presentation.module.ts", "hash": "11034607261382036469", "dependencies": [{"target": "npm:@nestjs/common", "source": "processor", "type": "static"}]}, {"file": "apps/processor/tsconfig.app.json", "hash": "14219769411140430087"}, {"file": "apps/processor/webpack.config.js", "hash": "14082570880392810944", "dependencies": [{"target": "npm:@nrwl/webpack", "source": "processor", "type": "static"}]}]}}, "scheduler": {"name": "scheduler", "type": "app", "data": {"name": "scheduler", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/scheduler/src", "projectType": "application", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/scheduler", "main": "apps/scheduler/src/main.ts", "tsConfig": "apps/scheduler/tsconfig.app.json", "assets": ["apps/scheduler/src/assets"], "webpackConfig": "apps/scheduler/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/scheduler/src/environments/environment.ts", "with": "apps/scheduler/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/node:node", "options": {"buildTarget": "scheduler:build", "port": 9234}, "configurations": {"production": {"buildTarget": "scheduler:build:production"}}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/scheduler/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/scheduler"], "options": {"jestConfig": "apps/scheduler/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "apps/scheduler", "implicitDependencies": [], "files": [{"file": "apps/scheduler/Dockerfile", "hash": "47680280200332752"}, {"file": "apps/scheduler/Dockerfile.dev", "hash": "11241657113753055606"}, {"file": "apps/scheduler/project.json", "hash": "1524193688914422751"}, {"file": "apps/scheduler/README.md", "hash": "897248710664713272"}, {"file": "apps/scheduler/src/app.module.ts", "hash": "9142606007944917483", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/config", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "scheduler", "type": "static"}, {"target": "logging", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "scheduler", "type": "static"}, {"target": "security", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/application.module.ts", "hash": "1644469613909231514", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/commands/create-job.command.ts", "hash": "17173647818776270203", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/commands/delete-collector.command.ts", "hash": "1341192253704137702"}, {"file": "apps/scheduler/src/application/commands/delete-job.command.ts", "hash": "5016099475115607377"}, {"file": "apps/scheduler/src/application/commands/index.ts", "hash": "2631109145626279518"}, {"file": "apps/scheduler/src/application/commands/job.command-handler.ts", "hash": "13181262158464802813", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/commands/register-collector.command.ts", "hash": "7830353367968694359", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/commands/update-collector.command.ts", "hash": "1915870211786750979", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/commands/update-job.command.ts", "hash": "15394878359282173423", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/create-job.handler.ts", "hash": "9942883121544782732", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/delete-collector.handler.ts", "hash": "14976282494417136343", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/delete-job.handler.ts", "hash": "2823292852386303635", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/get-collector.handler.ts", "hash": "17926182697221098427", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/get-collectors.handler.ts", "hash": "1818258783368532738", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/get-job.handler.ts", "hash": "1423428498007284675", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/get-jobs.handler.ts", "hash": "12563358382990500875", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/index.ts", "hash": "11786296718168396478"}, {"file": "apps/scheduler/src/application/handlers/register-collector.handler.ts", "hash": "3060118430374158812", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/update-collector.handler.ts", "hash": "2889401886822056739", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/handlers/update-job.handler.ts", "hash": "6097706941260768560", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/queries/get-collector.query.ts", "hash": "8494525418608521440"}, {"file": "apps/scheduler/src/application/queries/get-collectors.query.ts", "hash": "2877112103417397074", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/queries/get-job.query.ts", "hash": "1804643215626941751"}, {"file": "apps/scheduler/src/application/queries/get-jobs.query.ts", "hash": "14806691323318014328", "dependencies": [{"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/queries/index.ts", "hash": "4419162668524365407"}, {"file": "apps/scheduler/src/application/queries/job.query-handler.ts", "hash": "3951910903396371396", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/services/collector-job-sync.service.ts", "hash": "17852724895551720748", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/services/initialization.service.ts", "hash": "8343039124253136391", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/services/scheduler.service.ts", "hash": "9038307596905899782", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/application/services/workflow-scheduler.service.ts", "hash": "5757142397362272914", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "messaging", "source": "scheduler", "type": "static"}, {"target": "utils", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/scheduler/src/domain/domain.module.ts", "hash": "6225104368901622709", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/domain/services/collector-job-sync.service.ts", "hash": "17464416347650386795", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/domain/services/collector.service.ts", "hash": "2922891574016015447", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:uuid", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/domain/services/index.ts", "hash": "847520580240541360"}, {"file": "apps/scheduler/src/domain/services/job.service.ts", "hash": "10386591957609643204", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "npm:uuid", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/domain/services/schedule.service.ts", "hash": "2174689825141355947", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:uuid", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/domain/services/scheduler.service.ts", "hash": "14649625595606397592", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/adapters/adapters.module.ts", "hash": "13741005473120479817", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/adapters/job.adapter.ts", "hash": "16362420580118480282", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/adapters/scheduler.adapter.ts", "hash": "904891906374529400", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/config/config.module.ts", "hash": "16195144512492086620", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/config/config.service.ts", "hash": "12442286776369006821", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/config", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/database/database.module.ts", "hash": "2289308937499512187", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/database/repositories/base/generic-repository.base.ts", "hash": "13539515492658826509", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/database/services/database.service.ts", "hash": "2937400816490808820", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/database/services/mysql.service.ts", "hash": "9631899587757846847", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/infrastructure.module.ts", "hash": "7548283403469310274", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/messaging/messaging.module.ts", "hash": "15896220595779348278", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "messaging", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/services/advanced-scheduler.service.ts", "hash": "4485177324552121918", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}, {"target": "npm:node-schedule", "source": "scheduler", "type": "static"}, {"target": "npm:uuid", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/infrastructure/services/services.module.ts", "hash": "13525661412262797259", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/main.ts", "hash": "17769607193138826797", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "npm:@nestjs/core", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "messaging", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/controllers/collector.controller.ts", "hash": "7668131854536049876", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}, {"target": "storage", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/controllers/health.controller.ts", "hash": "12292763401027210351", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/controllers/job.controller.ts", "hash": "5618848123883160708", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/controllers/scheduler.controller.ts", "hash": "11299471296632182508", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/controllers/workflow.controller.ts", "hash": "11393138827212135809", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}, {"target": "messaging", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/dtos/collector.dto.ts", "hash": "10207781056804874139", "dependencies": [{"target": "npm:class-validator", "source": "scheduler", "type": "static"}, {"target": "npm:class-transformer", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/dtos/index.ts", "hash": "1792537366180982691"}, {"file": "apps/scheduler/src/presentation/dtos/job.dto.ts", "hash": "8054577631583534847", "dependencies": [{"target": "npm:class-validator", "source": "scheduler", "type": "static"}, {"target": "core", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/src/presentation/presentation.module.ts", "hash": "9864679486000035998", "dependencies": [{"target": "npm:@nestjs/common", "source": "scheduler", "type": "static"}]}, {"file": "apps/scheduler/tsconfig.app.json", "hash": "1041418209411743880"}, {"file": "apps/scheduler/webpack.config.js", "hash": "9014660379616503769", "dependencies": [{"target": "npm:@nrwl/webpack", "source": "scheduler", "type": "static"}]}]}}, "messaging": {"name": "messaging", "type": "lib", "data": {"name": "messaging", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/messaging/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/messaging", "tsConfig": "libs/messaging/tsconfig.lib.json", "packageJson": "libs/messaging/package.json", "main": "libs/messaging/src/index.ts", "assets": ["libs/messaging/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/messaging/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/messaging"], "options": {"jestConfig": "libs/messaging/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/messaging", "implicitDependencies": [], "files": [{"file": "libs/messaging/package.json", "hash": "5024836932871708190"}, {"file": "libs/messaging/project.json", "hash": "13338637004010207839"}, {"file": "libs/messaging/src/index.ts", "hash": "10566292520812232332"}, {"file": "libs/messaging/src/lib/bull/bull.interfaces.ts", "hash": "5092732646340181628", "dependencies": [{"target": "npm:bull", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bull/bull.module.ts", "hash": "581347505153047621", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bull/bull.service.ts", "hash": "4086401827445543797", "dependencies": [{"target": "npm:bull", "source": "messaging", "type": "static"}, {"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bull/index.ts", "hash": "13923120621898924191"}, {"file": "libs/messaging/src/lib/bullmq/bullmq.interfaces.ts", "hash": "4306648388211983526", "dependencies": [{"target": "npm:bullmq", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bullmq/bullmq.module.ts", "hash": "764919232374283397", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bullmq/bullmq.service.ts", "hash": "14077682307064135026", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}, {"target": "npm:bullmq", "source": "messaging", "type": "static"}, {"target": "npm:i<PERSON>is", "source": "messaging", "type": "static"}, {"target": "utils", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/bullmq/index.ts", "hash": "2857510549895997774"}, {"file": "libs/messaging/src/lib/events/event-bus.service.ts", "hash": "7373763043614311961", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}, {"target": "npm:uuid", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/events/event.interface.ts", "hash": "5326447157605597982"}, {"file": "libs/messaging/src/lib/events/index.ts", "hash": "9960710843943395174"}, {"file": "libs/messaging/src/lib/sse/index.ts", "hash": "4715380457072471704"}, {"file": "libs/messaging/src/lib/sse/sse.module.ts", "hash": "10304062867677770061", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/sse/sse.service.ts", "hash": "7012602322603010565", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}, {"target": "npm:rxjs", "source": "messaging", "type": "static"}, {"target": "utils", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/utils/error.utils.ts", "hash": "13888544324597272401"}, {"file": "libs/messaging/src/lib/workflow/index.ts", "hash": "1634568592769215195"}, {"file": "libs/messaging/src/lib/workflow/workflow.module.ts", "hash": "9100313102369602793", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/lib/workflow/workflow.service.ts", "hash": "12087336116409757659", "dependencies": [{"target": "npm:@nestjs/common", "source": "messaging", "type": "static"}, {"target": "npm:ajv", "source": "messaging", "type": "static"}, {"target": "utils", "source": "messaging", "type": "static"}, {"target": "core", "source": "messaging", "type": "static"}]}, {"file": "libs/messaging/src/types/js-yaml.d.ts", "hash": "12055638649896364974"}, {"file": "libs/messaging/src/utils.ts", "hash": "17011509495369814801"}, {"file": "libs/messaging/tsconfig.json", "hash": "4906954849963730315"}, {"file": "libs/messaging/tsconfig.lib.json", "hash": "3578820788245331221"}]}}, "security": {"name": "security", "type": "lib", "data": {"name": "security", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/security/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/security", "tsConfig": "libs/security/tsconfig.lib.json", "packageJson": "libs/security/package.json", "main": "libs/security/src/index.ts", "assets": ["libs/security/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/security/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/security"], "options": {"jestConfig": "libs/security/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/security", "implicitDependencies": [], "files": [{"file": "libs/security/package.json", "hash": "8094139926527699365"}, {"file": "libs/security/project.json", "hash": "8893051653924863698"}, {"file": "libs/security/src/index.ts", "hash": "2789508176908473225"}, {"file": "libs/security/src/lib/audit/audit-log.interceptor.ts", "hash": "3659442260279975000", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:rxjs", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/audit/audit-log.service.ts", "hash": "6979866547739550381", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/audit/audit.interfaces.ts", "hash": "17996791251991913025"}, {"file": "libs/security/src/lib/audit/index.ts", "hash": "17957814526656430227"}, {"file": "libs/security/src/lib/authentication/api-key.guard.ts", "hash": "15674497383512471570", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/core", "source": "security", "type": "static"}, {"target": "npm:rxjs", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/api-key.service.ts", "hash": "8780582087374254222", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/auth.guard.ts", "hash": "3673653624433346605", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/passport", "source": "security", "type": "static"}, {"target": "npm:rxjs", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/auth.interfaces.ts", "hash": "3913894813647329029"}, {"file": "libs/security/src/lib/authentication/combined-auth.guard.ts", "hash": "15442427666742391742", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/core", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/index.ts", "hash": "12585779724272504905"}, {"file": "libs/security/src/lib/authentication/jwt-auth.guard.ts", "hash": "15309615836533925458", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/passport", "source": "security", "type": "static"}, {"target": "npm:@nestjs/core", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/jwt.module.ts", "hash": "9424927949978883396", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "security", "type": "static"}, {"target": "npm:@nestjs/passport", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/jwt.service.ts", "hash": "15722400534155348475", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/jwt.strategy.ts", "hash": "10680137550087341343", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/passport", "source": "security", "type": "static"}, {"target": "npm:passport-jwt", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/oauth2.service.ts", "hash": "1383669976884951693", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:axios", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authentication/public.decorator.ts", "hash": "14103451203829182854", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authorization/index.ts", "hash": "15353138536311867362"}, {"file": "libs/security/src/lib/authorization/permissions.decorator.ts", "hash": "15499351092914067690", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authorization/permissions.guard.ts", "hash": "606006002236060885", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/core", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authorization/roles.decorator.ts", "hash": "10312179788015518095", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/authorization/roles.guard.ts", "hash": "14989193397718146318", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/core", "source": "security", "type": "static"}]}, {"file": "libs/security/src/lib/security.module.ts", "hash": "8721215380841365183", "dependencies": [{"target": "npm:@nestjs/common", "source": "security", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "security", "type": "static"}]}, {"file": "libs/security/tsconfig.lib.json", "hash": "5316911215209781238"}]}}, "logging": {"name": "logging", "type": "lib", "data": {"name": "logging", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/logging/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/logging", "tsConfig": "libs/logging/tsconfig.lib.json", "packageJson": "libs/logging/package.json", "main": "libs/logging/src/index.ts", "assets": ["libs/logging/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/logging/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/logging"], "options": {"jestConfig": "libs/logging/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/logging", "implicitDependencies": [], "files": [{"file": "libs/logging/project.json", "hash": "16731964693846754074"}, {"file": "libs/logging/src/index.ts", "hash": "11221392782955038930"}, {"file": "libs/logging/src/lib/formatters/error-formatter.ts", "hash": "16633084733280563730"}, {"file": "libs/logging/src/lib/formatters/index.ts", "hash": "2461385455335525523"}, {"file": "libs/logging/src/lib/formatters/request-formatter.ts", "hash": "11279702881004303921"}, {"file": "libs/logging/src/lib/json-logger/json-logger.module.ts", "hash": "14464204539221584542", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/json-logger/json-logger.service.ts", "hash": "2210437963884049297", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/pino/index.ts", "hash": "13481700086392499596"}, {"file": "libs/logging/src/lib/pino/named-logger.module.ts", "hash": "17246950653506261543", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/pino/named-logger.service.ts", "hash": "7228534947075666344", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}, {"target": "npm:pino", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/pino/pino-logger.interfaces.ts", "hash": "15651597252134352888", "dependencies": [{"target": "npm:pino", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/pino/pino-logger.module.ts", "hash": "16169571608097731966", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}]}, {"file": "libs/logging/src/lib/pino/pino-logger.service.ts", "hash": "1731488063082471138", "dependencies": [{"target": "npm:@nestjs/common", "source": "logging", "type": "static"}, {"target": "npm:pino", "source": "logging", "type": "static"}]}, {"file": "libs/logging/tsconfig.lib.json", "hash": "9870891907852726316"}]}}, "storage": {"name": "storage", "type": "lib", "data": {"name": "storage", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/storage/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/storage", "tsConfig": "libs/storage/tsconfig.lib.json", "packageJson": "libs/storage/package.json", "main": "libs/storage/src/index.ts", "assets": ["libs/storage/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/storage/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/storage"], "options": {"jestConfig": "libs/storage/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/storage", "implicitDependencies": [], "files": [{"file": "libs/storage/project.json", "hash": "9721490681095061465"}, {"file": "libs/storage/src/core.ts", "hash": "8987123571667499520"}, {"file": "libs/storage/src/entities/collector.entity.ts", "hash": "11926612871788421268", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/config.entity.ts", "hash": "15547473832467950313", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/event.entity.ts", "hash": "10544126553755300110"}, {"file": "libs/storage/src/entities/index.ts", "hash": "10785489251433065189"}, {"file": "libs/storage/src/entities/job.entity.ts", "hash": "10217331961550709146"}, {"file": "libs/storage/src/entities/kgx-unified.entity.ts", "hash": "2697625006252165282", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/kgx.entity.ts", "hash": "17629501009708873297", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/metric.entity.ts", "hash": "716073726402093534", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/pipeline.entity.ts", "hash": "12519338617788717101"}, {"file": "libs/storage/src/entities/processing-result.entity.ts", "hash": "6657133224762027450", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/processor-result.entity.ts", "hash": "11671258163845821802", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/entities/scheduler.entity.ts", "hash": "5716303930125430770"}, {"file": "libs/storage/src/entities/usd-index.entity.ts", "hash": "13711370511513367259", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/index.ts", "hash": "11481587650553818", "dependencies": [{"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/abstract/database.interface.ts", "hash": "6625533747823794005"}, {"file": "libs/storage/src/lib/adapters/crypto.adapter.ts", "hash": "5118167380989847315", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/adapters/generic.adapter.ts", "hash": "6041282690944460595", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/base-mysql.repository.ts", "hash": "7213918805941480455", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/base-postgres.repository.ts", "hash": "4779545976291176908", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/config/database-config.module.ts", "hash": "4275114408309447984", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/config/database-config.provider.ts", "hash": "499053513325471158", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:dotenv", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/constants/core.constants.ts", "hash": "12250955109133075934"}, {"file": "libs/storage/src/lib/database.module.ts", "hash": "5691938024211808191", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/db-initializer.ts", "hash": "9288363537310327591", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/dynamic-database.module.ts", "hash": "11738307789705075510", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/factory/database.factory.ts", "hash": "6120831547764559422", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/global-database.module.ts", "hash": "11212219728846146243", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/config", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/inbox/inbox-cleanup.service.ts", "hash": "16159061696376268469", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/inbox/inbox.entity.ts", "hash": "10778589285620767163", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/inbox/inbox.module.ts", "hash": "4289940827524544731", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/schedule", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/inbox/inbox.service.ts", "hash": "5108315099385526872", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/inbox/index.ts", "hash": "14167682362181377974"}, {"file": "libs/storage/src/lib/mysql/index.ts", "hash": "3006843861669858189"}, {"file": "libs/storage/src/lib/mysql/mysql.base.service.ts", "hash": "13972185039175153586", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:mysql2", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/mysql/mysql.interfaces.ts", "hash": "2208373613567868544"}, {"file": "libs/storage/src/lib/mysql/mysql.module.ts", "hash": "18085484660985450487", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/postgres/index.ts", "hash": "16511997988963391065"}, {"file": "libs/storage/src/lib/postgres/postgres.base.service.ts", "hash": "9735850389797256862", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:pg", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/postgres/postgres.interfaces.ts", "hash": "18229018819308892904"}, {"file": "libs/storage/src/lib/postgres/postgres.module.ts", "hash": "9712095573831852446", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/collector.repository.ts", "hash": "11548644833097797045", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/index.ts", "hash": "10977520176977100682"}, {"file": "libs/storage/src/lib/repositories/job.repository.ts", "hash": "7099923472624552793", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/pipeline.repository.ts", "hash": "11126125926575606435", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:uuid", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/processor.repository.ts", "hash": "3392497605864022893", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:uuid", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/repositories.module.ts", "hash": "12163350433548910870", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/repositories/scheduler.repository.ts", "hash": "5728995592863507049", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:uuid", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/connection-retry.module.ts", "hash": "2273003937473758858", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/connection-retry.service.ts", "hash": "2497245169429462814", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/core-database.module.ts", "hash": "17858562024167191518", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/database-initializer.module.ts", "hash": "2513626066227435950", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/database-initializer.service.ts", "hash": "5116767172629288402", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/health-check.module.ts", "hash": "7925626854675630115", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/health-check.service.ts", "hash": "13211230875424731924", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/mysql.service.ts", "hash": "4856410793917428235", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/services/postgres.service.ts", "hash": "1643476185739682350", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/utils/date-formatter.util.ts", "hash": "5068080943142234040"}, {"file": "libs/storage/src/lib/utils/error-handler.util.ts", "hash": "5567677472966989501", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/lib/utils/error.utils.ts", "hash": "13888544324597272401"}, {"file": "libs/storage/src/lib/utils/index.ts", "hash": "9859761544333375039"}, {"file": "libs/storage/src/migrations/api/api-migrations.ts", "hash": "6392272962643904843", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/base.migration.ts", "hash": "18343214064256388934", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/collector/collector-migrations.ts", "hash": "3126018735889914702", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/collector/index.ts", "hash": "8557820358633747929"}, {"file": "libs/storage/src/migrations/collector/metrics-table.migration.ts", "hash": "3142012290569852204", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/index.ts", "hash": "316392068492395912"}, {"file": "libs/storage/src/migrations/migration.module.ts", "hash": "1198354074711871896", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/config", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/migration.registry.ts", "hash": "16809909177301804996", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/migration.service.ts", "hash": "2107504522253771311", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:@nestjs/config", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/processor/index.ts", "hash": "3818966492121822923"}, {"file": "libs/storage/src/migrations/processor/processor-migrations.ts", "hash": "8363212383071259234", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/processor/sql/config-table.sql", "hash": "15491629657043764279"}, {"file": "libs/storage/src/migrations/README.md", "hash": "6789043758884200630"}, {"file": "libs/storage/src/migrations/scheduler/scheduler-migrations.ts", "hash": "10606966328139604399", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/shared/add-kgx-columns-migration.ts", "hash": "12295246949713767515", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/shared/consolidated-tables-migration.ts", "hash": "16255404853591035315", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/shared/index.ts", "hash": "10253070551096795261"}, {"file": "libs/storage/src/migrations/shared/shared-schema-migration.ts", "hash": "17496929326016128425", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/shared/unified-kgx-migration.ts", "hash": "4076625614416797691", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/utils/migration-utils.ts", "hash": "11491759268290729227", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/writer/data-hash-index.migration.ts", "hash": "5677188048045606898", "dependencies": [{"target": "npm:@nestjs/common", "source": "storage", "type": "static"}, {"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/migrations/writer/index.ts", "hash": "13912869842096235020"}, {"file": "libs/storage/src/migrations/writer/writer-migrations.ts", "hash": "950576004189001248", "dependencies": [{"target": "npm:typeorm", "source": "storage", "type": "static"}]}, {"file": "libs/storage/src/schemas/api.schema.ts", "hash": "14018518212189218184"}, {"file": "libs/storage/src/schemas/index.ts", "hash": "4563077055856895981"}, {"file": "libs/storage/src/schemas/kgx/kgx-crypto.mysql.schema.ts", "hash": "18098730526601760492"}, {"file": "libs/storage/src/schemas/kgx/kgx-crypto.postgres.schema.ts", "hash": "1251403757978434399"}, {"file": "libs/storage/src/schemas/scheduler.schema.ts", "hash": "15554896676637639172"}, {"file": "libs/storage/src/schemas/shared.schema.ts", "hash": "6080956658686785900"}, {"file": "libs/storage/tsconfig.json", "hash": "16876168064498955967"}, {"file": "libs/storage/tsconfig.lib.json", "hash": "9175219935232372152"}, {"file": "libs/storage/tsconfig.spec.json", "hash": "5722547793982858863"}]}}, "writer": {"name": "writer", "type": "app", "data": {"name": "writer", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/writer/src", "projectType": "application", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/writer", "main": "apps/writer/src/main.ts", "tsConfig": "apps/writer/tsconfig.app.json", "assets": ["apps/writer/src/assets"], "webpackConfig": "apps/writer/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/writer/src/environments/environment.ts", "with": "apps/writer/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/js:node", "options": {"buildTarget": "writer:build", "port": 9232}, "configurations": {"production": {"buildTarget": "writer:build:production"}}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/writer/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/writer"], "options": {"jestConfig": "apps/writer/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "apps/writer", "implicitDependencies": [], "files": [{"file": "apps/writer/Dockerfile", "hash": "601423048695725141"}, {"file": "apps/writer/Dockerfile.dev", "hash": "8791947314663521065"}, {"file": "apps/writer/project.json", "hash": "10385301591159579745"}, {"file": "apps/writer/README.md", "hash": "2572293282911487317"}, {"file": "apps/writer/src/app.module.ts", "hash": "15196354005490463410", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/config", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "writer", "type": "static"}, {"target": "security", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/application.module.ts", "hash": "3654699326469270550", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/handlers/base-data.handler.ts", "hash": "6563927250088775290", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/handlers/raw-data.handler.ts", "hash": "8668571232995270395", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/handlers/write-data.handler.ts", "hash": "14835910735099882483", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/services/initialization.service.ts", "hash": "15064662031339825219", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/services/workflow-writer.service.ts", "hash": "11494364352968888578", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "messaging", "source": "writer", "type": "static"}, {"target": "utils", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/application/services/write.service.ts", "hash": "1614514205012351256", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/writer/src/domain/domain.module.ts", "hash": "8189541153627421032", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/plugins/kgx/kgx-crypto-writer.service.ts", "hash": "9091591871799393739", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}, {"target": "kgx", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/plugins/kgx/kgx-crypto.module.ts", "hash": "3775972192501698783", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/plugins/kgx/kgx-crypto.plugin.ts", "hash": "3208911114920660643", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/plugins/writer-plugin.interface.ts", "hash": "4601584822643958888", "dependencies": [{"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/plugins/writer-plugin.registry.ts", "hash": "13533847480561619296", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/services/database-creator.service.ts", "hash": "3353440084926345756", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/services/destination.service.ts", "hash": "5647955575412219581", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/services/generic-data-writer.service.ts", "hash": "13689325121588661853", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/services/writer.service.ts", "hash": "4445495337298261781", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/utils/kgx-data-parser.util.ts", "hash": "3486003071692123019", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/utils/utils.module.ts", "hash": "6505656716618441357", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/elasticsearch.writer.ts", "hash": "16393471510034503856", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@elastic/elasticsearch", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/file.writer.ts", "hash": "16716155477708084643", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}, {"target": "npm:csv-stringify", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/index.ts", "hash": "17648280406092054875"}, {"file": "apps/writer/src/domain/writers/mongodb.writer.ts", "hash": "2318157015854342643", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "npm:mongodb", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/mysql.writer.ts", "hash": "7348217027645463613", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/postgres.writer.ts", "hash": "16768690971533201807", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:uuid", "source": "writer", "type": "static"}, {"target": "npm:typeorm", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/s3.writer.ts", "hash": "13691606036743035595", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}, {"target": "npm:@aws-sdk/client-s3", "source": "writer", "type": "static"}, {"target": "npm:fast-csv", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/domain/writers/writer.interface.ts", "hash": "9038998718878210570", "dependencies": [{"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/config/config.module.ts", "hash": "4197412588314224064", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/config", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/config/config.service.ts", "hash": "10171150441996862832", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/config", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/database.module.ts", "hash": "18143619310454979834", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/typeorm", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/repositories/base/generic-repository.base.ts", "hash": "6406392278279950184", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/repositories/collection.repository.ts", "hash": "2740318853673689577", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/repositories/processor.repository.ts", "hash": "11946886544963998780", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/services/mysql.service.ts", "hash": "6866836701556374238", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/services/postgres.service.ts", "hash": "8746007167017259609", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/database/services/schema-inference.service.ts", "hash": "6191816235148950781", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/health/database-health.service.ts", "hash": "14835489930155882056", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/health/health.module.ts", "hash": "7562278740536169649", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "storage", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/infrastructure.module.ts", "hash": "5941107549058113213", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/infrastructure/messaging/messaging.module.ts", "hash": "15499125299011114867", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "logging", "source": "writer", "type": "static"}, {"target": "messaging", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/main.ts", "hash": "15361543801049307639", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "npm:@nestjs/core", "source": "writer", "type": "static"}, {"target": "messaging", "source": "writer", "type": "static"}, {"target": "utils", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/controllers/destination.controller.ts", "hash": "10677752226917363227", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/controllers/health.controller.ts", "hash": "4638824860337365511", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/controllers/write.controller.ts", "hash": "4902868097167419096", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/dtos/destination.dto.ts", "hash": "2170810248630699786", "dependencies": [{"target": "core", "source": "writer", "type": "static"}, {"target": "npm:class-validator", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/dtos/mapping.dto.ts", "hash": "1145197819396219030", "dependencies": [{"target": "npm:class-validator", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/dtos/write.dto.ts", "hash": "13147208382577079574", "dependencies": [{"target": "npm:class-validator", "source": "writer", "type": "static"}, {"target": "core", "source": "writer", "type": "static"}]}, {"file": "apps/writer/src/presentation/presentation.module.ts", "hash": "1530309924696182087", "dependencies": [{"target": "npm:@nestjs/common", "source": "writer", "type": "static"}]}, {"file": "apps/writer/tsconfig.app.json", "hash": "6969268701395850618"}, {"file": "apps/writer/webpack.config.js", "hash": "2430013203966249375", "dependencies": [{"target": "npm:@nrwl/webpack", "source": "writer", "type": "static"}]}]}}, "config": {"name": "config", "type": "lib", "data": {"name": "config", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/config/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/config", "tsConfig": "libs/config/tsconfig.lib.json", "packageJson": "libs/config/package.json", "main": "libs/config/src/index.ts", "assets": ["libs/config/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/config/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/config"], "options": {"jestConfig": "libs/config/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/config", "implicitDependencies": [], "files": [{"file": "libs/config/project.json", "hash": "14039843388868813932"}, {"file": "libs/config/src/index.ts", "hash": "17137794277271233873"}, {"file": "libs/config/src/lib/validation/config-validator.module.ts", "hash": "7312099996347413801", "dependencies": [{"target": "npm:@nestjs/common", "source": "config", "type": "static"}]}, {"file": "libs/config/src/lib/validation/config-validator.service.ts", "hash": "16780992840371419926", "dependencies": [{"target": "npm:@nestjs/common", "source": "config", "type": "static"}, {"target": "npm:ajv", "source": "config", "type": "static"}]}, {"file": "libs/config/src/lib/validation/index.ts", "hash": "14754119634863739205"}, {"file": "libs/config/src/lib/yaml/index.ts", "hash": "8763478557548149523"}, {"file": "libs/config/src/lib/yaml/interfaces/config-options.interface.ts", "hash": "11863731636067940990"}, {"file": "libs/config/src/lib/yaml/interfaces/index.ts", "hash": "13847415025194867466"}, {"file": "libs/config/src/lib/yaml/yaml-config.module.ts", "hash": "15214122037606635630", "dependencies": [{"target": "npm:@nestjs/common", "source": "config", "type": "static"}]}, {"file": "libs/config/src/lib/yaml/yaml-config.service.ts", "hash": "6863770327016938368", "dependencies": [{"target": "npm:@nestjs/common", "source": "config", "type": "static"}, {"target": "npm:yaml", "source": "config", "type": "static"}]}, {"file": "libs/config/tsconfig.lib.json", "hash": "12470020030571797992"}]}}, "cache": {"name": "cache", "type": "lib", "data": {"name": "cache", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/cache/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/cache", "tsConfig": "libs/cache/tsconfig.lib.json", "packageJson": "libs/cache/package.json", "main": "libs/cache/src/index.ts", "assets": ["libs/cache/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/cache/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/cache"], "options": {"jestConfig": "libs/cache/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/cache", "implicitDependencies": [], "files": [{"file": "libs/cache/package.json", "hash": "11294553492232656343"}, {"file": "libs/cache/project.json", "hash": "18184193244060840260"}, {"file": "libs/cache/src/index.ts", "hash": "2136357927126314504"}, {"file": "libs/cache/src/lib/redis/index.ts", "hash": "16135995196249951184"}, {"file": "libs/cache/src/lib/redis/redis-cache.interfaces.ts", "hash": "7222082912479527382"}, {"file": "libs/cache/src/lib/redis/redis-cache.module.ts", "hash": "5766056768816588659", "dependencies": [{"target": "npm:@nestjs/common", "source": "cache", "type": "static"}]}, {"file": "libs/cache/src/lib/redis/redis-cache.service.ts", "hash": "17693044611144152954", "dependencies": [{"target": "npm:@nestjs/common", "source": "cache", "type": "static"}, {"target": "npm:redis", "source": "cache", "type": "static"}]}, {"file": "libs/cache/src/lib/utils/error.utils.ts", "hash": "13888544324597272401"}, {"file": "libs/cache/src/utils.ts", "hash": "17011509495369814801"}, {"file": "libs/cache/tsconfig.json", "hash": "4906954849963730315"}, {"file": "libs/cache/tsconfig.lib.json", "hash": "8439964285821359195"}]}}, "utils": {"name": "utils", "type": "lib", "data": {"name": "utils", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/utils/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/utils", "tsConfig": "libs/utils/tsconfig.lib.json", "packageJson": "libs/utils/package.json", "main": "libs/utils/src/index.ts", "assets": ["libs/utils/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/utils/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/utils"], "options": {"jestConfig": "libs/utils/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/utils", "implicitDependencies": [], "files": [{"file": "libs/utils/jest.config.ts", "hash": "15996567871414498597"}, {"file": "libs/utils/package.json", "hash": "6821388205880561879"}, {"file": "libs/utils/project.json", "hash": "17565294115914266299"}, {"file": "libs/utils/src/index.ts", "hash": "14428353282588746736"}, {"file": "libs/utils/src/lib/env-config-loader.ts", "hash": "853531725235994825", "dependencies": [{"target": "npm:@nestjs/common", "source": "utils", "type": "static"}]}, {"file": "libs/utils/src/lib/error-utils.ts", "hash": "5752126638884131437", "dependencies": [{"target": "npm:@nestjs/common", "source": "utils", "type": "static"}]}, {"file": "libs/utils/src/lib/index.ts", "hash": "17224102894291153007"}, {"file": "libs/utils/tsconfig.json", "hash": "17973130987760565389"}, {"file": "libs/utils/tsconfig.lib.json", "hash": "3147823700304982515"}, {"file": "libs/utils/tsconfig.spec.json", "hash": "163997633145879976"}]}}, "core": {"name": "core", "type": "lib", "data": {"name": "core", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/core/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/core", "tsConfig": "libs/core/tsconfig.lib.json", "packageJson": "libs/core/package.json", "main": "libs/core/src/index.ts", "assets": ["libs/core/*.md"], "buildableProjectDepsInPackageJsonType": "dependencies"}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/core/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/core"], "options": {"jestConfig": "libs/core/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "implicitDependencies": ["utils"], "tags": [], "root": "libs/core", "files": [{"file": "libs/core/index.ts", "hash": "15503433633924650494"}, {"file": "libs/core/package.json", "hash": "5913091964432275508"}, {"file": "libs/core/project.json", "hash": "8819100353009566651"}, {"file": "libs/core/src/constants.ts", "hash": "4659807894051924554"}, {"file": "libs/core/src/index.ts", "hash": "9201322311926457971"}, {"file": "libs/core/src/interfaces.ts", "hash": "159296723313341"}, {"file": "libs/core/src/lib/config/schemas/processor.schema.json", "hash": "8520558511589023892"}, {"file": "libs/core/src/lib/constants/auth.contants.ts", "hash": "6836718051760467759"}, {"file": "libs/core/src/lib/constants/collectors.constants.ts", "hash": "8548966568060105597"}, {"file": "libs/core/src/lib/constants/index.ts", "hash": "15317524057842392021"}, {"file": "libs/core/src/lib/constants/jobs.constants.ts", "hash": "3188755533761381374"}, {"file": "libs/core/src/lib/constants/processors.constants.ts", "hash": "2977470508669828414"}, {"file": "libs/core/src/lib/constants/queues.constants.ts", "hash": "7437218064498084501"}, {"file": "libs/core/src/lib/constants/schedulers.constants.ts", "hash": "3683748537089194392"}, {"file": "libs/core/src/lib/constants/topics.constants.ts", "hash": "5462415568853742727"}, {"file": "libs/core/src/lib/constants/workfflow.interface.ts", "hash": "13484642319540671296"}, {"file": "libs/core/src/lib/constants/writers.constants.ts", "hash": "5306097367578138413"}, {"file": "libs/core/src/lib/exceptions/base.exception.ts", "hash": "10273181209111219704"}, {"file": "libs/core/src/lib/exceptions/index.ts", "hash": "6679427341017593075"}, {"file": "libs/core/src/lib/exceptions/not-found.exception.ts", "hash": "16314516406645924152"}, {"file": "libs/core/src/lib/exceptions/validation.exception.ts", "hash": "8806546931742576321"}, {"file": "libs/core/src/lib/interfaces/cache-service.interface.ts", "hash": "4741982112892736187"}, {"file": "libs/core/src/lib/interfaces/collector.interface.ts", "hash": "14674870321387089286"}, {"file": "libs/core/src/lib/interfaces/core.interface.ts", "hash": "5256745783304109218"}, {"file": "libs/core/src/lib/interfaces/index.ts", "hash": "2094813036594605950"}, {"file": "libs/core/src/lib/interfaces/job.interface.ts", "hash": "1905802948163080504"}, {"file": "libs/core/src/lib/interfaces/workflow-data.interface.ts", "hash": "18279039337442960266", "dependencies": [{"target": "npm:uuid", "source": "core", "type": "static"}]}, {"file": "libs/core/src/lib/interfaces/write-destination.entity.ts", "hash": "8526389443384796161"}, {"file": "libs/core/src/lib/interfaces/write-result.entity.ts", "hash": "9567744220649351092"}, {"file": "libs/core/src/lib/interfaces/writer.interface.ts", "hash": "9522228338020952576"}, {"file": "libs/core/src/lib/models/collector-event.model.ts", "hash": "5696120840042375804"}, {"file": "libs/core/src/lib/models/index.ts", "hash": "8011735852840405538"}, {"file": "libs/core/src/lib/plugin/plugin.interface.ts", "hash": "18422265879223788149"}, {"file": "libs/core/src/lib/plugin/plugin.registry.ts", "hash": "16565436964087046916", "dependencies": [{"target": "npm:@nestjs/common", "source": "core", "type": "static"}]}, {"file": "libs/core/src/lib/services/deduplication.service.ts", "hash": "15779216673727833957", "dependencies": [{"target": "npm:@nestjs/common", "source": "core", "type": "static"}, {"target": "npm:prom-client", "source": "core", "type": "static"}]}, {"file": "libs/core/src/lib/utils/array.utils.ts", "hash": "8481599272753574709"}, {"file": "libs/core/src/lib/utils/collection-hash.utils.ts", "hash": "10964778663951577895"}, {"file": "libs/core/src/lib/utils/data-integrity.utils.ts", "hash": "9454145842780577768"}, {"file": "libs/core/src/lib/utils/date.utils.ts", "hash": "2055226054641222148"}, {"file": "libs/core/src/lib/utils/error.utils.ts", "hash": "2924291212363670984"}, {"file": "libs/core/src/lib/utils/hash.utils.ts", "hash": "7939379778670751200"}, {"file": "libs/core/src/lib/utils/index.ts", "hash": "8259564930163449856"}, {"file": "libs/core/src/lib/utils/object.utils.ts", "hash": "5559231827919711338"}, {"file": "libs/core/src/lib/utils/retry.utils.ts", "hash": "13455213385703521802", "dependencies": [{"target": "npm:@nestjs/common", "source": "core", "type": "static"}]}, {"file": "libs/core/src/lib/utils/string.utils.ts", "hash": "14534543126472363008"}, {"file": "libs/core/src/lib/utils/workflow-logger.util.ts", "hash": "11277793339225963467", "dependencies": [{"target": "npm:@nestjs/common", "source": "core", "type": "static"}]}, {"file": "libs/core/tsconfig.json", "hash": "7011567668814872540"}, {"file": "libs/core/tsconfig.lib.json", "hash": "11044218043652126932"}]}}, "api": {"name": "api", "type": "app", "data": {"name": "api", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "apps/api/src", "projectType": "application", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/webpack:webpack", "outputs": ["{options.outputPath}"], "options": {"target": "node", "compiler": "tsc", "outputPath": "dist/apps/api", "main": "apps/api/src/main.ts", "tsConfig": "apps/api/tsconfig.app.json", "assets": ["apps/api/src/assets"], "webpackConfig": "apps/api/webpack.config.js"}, "configurations": {"production": {"optimization": true, "extractLicenses": true, "inspect": false, "fileReplacements": [{"replace": "apps/api/src/environments/environment.ts", "with": "apps/api/src/environments/environment.prod.ts"}]}}}, "serve": {"executor": "@nrwl/js:node", "options": {"buildTarget": "api:build", "port": 9233}, "configurations": {"production": {"buildTarget": "api:build:production"}}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["apps/api/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/apps/api"], "options": {"jestConfig": "apps/api/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "apps/api", "implicitDependencies": [], "files": [{"file": "apps/api/Dockerfile", "hash": "3296936571523384249"}, {"file": "apps/api/Dockerfile.dev", "hash": "6792466680822154232"}, {"file": "apps/api/project.json", "hash": "2809273688876838205"}, {"file": "apps/api/README.md", "hash": "5333997236757080777"}, {"file": "apps/api/src/app.module.ts", "hash": "14944865249748409825", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "logging", "source": "api", "type": "static"}, {"target": "npm:@nestjs/jwt", "source": "api", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "api", "type": "static"}, {"target": "kgx", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}, {"target": "npm:@nestjs/core", "source": "api", "type": "static"}, {"target": "npm:@nestjs/config", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/application.module.ts", "hash": "17895646294150308597", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "cache", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/facades/data.facade.ts", "hash": "15815468987793346575", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/interfaces/asset-data.interface.ts", "hash": "10273621895542476136"}, {"file": "apps/api/src/application/services/asset-data-transformer.service.ts", "hash": "14989753558353604229", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/services/crypto-data.service.ts", "hash": "4068608134792409969", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/services/data-cache.service.ts", "hash": "7410585896893413529", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "cache", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/services/initialization.service.ts", "hash": "11964892809670440296", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/services/kitco-cms-data.service.ts", "hash": "5561803026427900543", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "api", "type": "static"}, {"target": "npm:@nestjs/config", "source": "api", "type": "static"}, {"target": "npm:pg", "source": "api", "type": "static"}]}, {"file": "apps/api/src/application/services/workflow-api.service.ts", "hash": "1971453755371101088", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/assets/.gitkeep", "hash": "3244421341483603138"}, {"file": "apps/api/src/domain/domain.module.ts", "hash": "12255244498754614120", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}]}, {"file": "apps/api/src/domain/services/crypto-symbols-config.service.ts", "hash": "17573969991458328034", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "kgx", "source": "api", "type": "static"}]}, {"file": "apps/api/src/domain/services/data.service.ts", "hash": "5390686406946978223", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/adapters/adapters.module.ts", "hash": "13373995659631040863", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/adapters/pipeline.adapter.ts", "hash": "12910747035140243787", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/adapters/processor.adapter.ts", "hash": "9917450987361706524", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/client.module.ts", "hash": "6620090092928822372", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/collector.client.ts", "hash": "7326388770819202878", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}, {"target": "npm:rxjs", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/kitco-cms-sse-client.service.ts", "hash": "16328732022543524567", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/processor.client.ts", "hash": "12541508040749697153", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "npm:rxjs", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/scheduler.client.ts", "hash": "1171070048398033336", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}, {"target": "npm:rxjs", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/clients/writer.client.ts", "hash": "4428824035618706428", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "npm:rxjs", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/config/config.module.ts", "hash": "16195144512492086620", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/config/config.service.ts", "hash": "6905741216231831447", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/config", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/database.module.ts", "hash": "11515046664595103559", "dependencies": [{"source": "api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "api", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "api", "target": "storage", "type": "static"}, {"source": "api", "target": "npm:typeorm", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/repositories/dynamic-query.repository.ts", "hash": "9711839939645545762", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/repositories/mysql.repository.ts", "hash": "16323926774047161619", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/repositories/postgres.repository.ts", "hash": "4237170442371994247", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/services/mysql.service.ts", "hash": "9044669713205462144", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/database/services/postgres.service.ts", "hash": "13185672983508702536", "dependencies": [{"source": "api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "api", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "api", "target": "npm:typeorm", "type": "static"}, {"source": "api", "target": "storage", "type": "static"}]}, {"file": "apps/api/src/infrastructure/health/database-health.service.ts", "hash": "4011470883540792792", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/health/health.module.ts", "hash": "7559576890551129727", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/infrastructure.module.ts", "hash": "15344740100356537052", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}]}, {"file": "apps/api/src/infrastructure/messaging/messaging.module.ts", "hash": "18203506842477978835", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/event-emitter", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}]}, {"file": "apps/api/src/main.ts", "hash": "499613055528738587", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/core", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}, {"target": "core", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/auth.controller.ts", "hash": "10691892921957781705", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/collections.controller.ts", "hash": "1686345952782275965", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/collectors.controller.ts", "hash": "9940012301440523166", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/destinations.controller.ts", "hash": "3617766869716553063", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/health.controller.ts", "hash": "1709680087884018782", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}, {"target": "npm:rxjs", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/jobs.controller.ts", "hash": "809890770564009898", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/pipelines.controller.ts", "hash": "10167109330141276763", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "storage", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/processing.controller.ts", "hash": "11507897322715182720", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/v1/kgx/data.controller.ts", "hash": "15409112711878825826", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/v1/kgx/sse.controller.ts", "hash": "7958639279013181239", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}, {"target": "npm:uuid", "source": "api", "type": "static"}, {"target": "messaging", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/controllers/writing.controller.ts", "hash": "17096868397205905323", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/swagger", "source": "api", "type": "static"}, {"target": "utils", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/dtos/auth.dto.ts", "hash": "12624338354186414406", "dependencies": [{"target": "npm:class-validator", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/middleware/api-key-normalizer.middleware.ts", "hash": "2033186782027223762", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}]}, {"file": "apps/api/src/presentation/presentation.module.ts", "hash": "7902797997674077408", "dependencies": [{"target": "npm:@nestjs/common", "source": "api", "type": "static"}, {"target": "npm:@nestjs/axios", "source": "api", "type": "static"}, {"target": "logging", "source": "api", "type": "static"}, {"target": "security", "source": "api", "type": "static"}]}, {"file": "apps/api/tsconfig.app.json", "hash": "9873971304957799460"}, {"file": "apps/api/webpack.config.js", "hash": "17798342131513560077", "dependencies": [{"target": "npm:@nrwl/webpack", "source": "api", "type": "static"}]}]}}, "kgx": {"name": "kgx", "type": "lib", "data": {"name": "kgx", "$schema": "../../node_modules/nx/schemas/project-schema.json", "sourceRoot": "libs/kgx/src", "projectType": "library", "targets": {"build": {"dependsOn": ["^build"], "inputs": ["production", "^production"], "executor": "@nrwl/js:tsc", "outputs": ["{options.outputPath}"], "options": {"outputPath": "dist/libs/kgx", "tsConfig": "libs/kgx/tsconfig.lib.json", "packageJson": "libs/kgx/package.json", "main": "libs/kgx/src/index.ts", "assets": ["libs/kgx/*.md"]}, "configurations": {}}, "lint": {"inputs": ["default", "{workspaceRoot}/.eslintrc.json"], "executor": "@nrwl/linter:eslint", "outputs": ["{options.outputFile}"], "options": {"lintFilePatterns": ["libs/kgx/**/*.ts"]}, "configurations": {}}, "test": {"inputs": ["default", "^production", "{workspaceRoot}/jest.preset.js"], "executor": "@nrwl/jest:jest", "outputs": ["{workspaceRoot}/coverage/libs/kgx"], "options": {"jestConfig": "libs/kgx/jest.config.ts", "passWithNoTests": true}, "configurations": {}}}, "tags": [], "root": "libs/kgx", "implicitDependencies": [], "files": [{"file": "libs/kgx/package.json", "hash": "9078644596944516551"}, {"file": "libs/kgx/project.json", "hash": "5218996969309222540"}, {"file": "libs/kgx/src/constants/crypto.constants.ts", "hash": "17674430066981567289"}, {"file": "libs/kgx/src/entities/crypto-kgx.entity.ts", "hash": "3332468160904610817", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/crypto-price.entity.ts", "hash": "1129080014534512408", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/fx-prices.entity.ts", "hash": "1292978208858669247", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/indexes-close.entity.ts", "hash": "705739439589498177", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/indexes-live.entity.ts", "hash": "16596157302489964927", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/kdxy.entity.ts", "hash": "10404107276246420058", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/usd-index-value.entity.ts", "hash": "6787619065369107213", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/entities/usd-index.entity.ts", "hash": "5327588855215527715", "dependencies": [{"target": "npm:typeorm", "source": "kgx", "type": "static"}]}, {"file": "libs/kgx/src/index.ts", "hash": "16367150562469560998"}, {"file": "libs/kgx/src/models/kgx-crypto.model.ts", "hash": "14487656281939410962"}, {"file": "libs/kgx/tsconfig.json", "hash": "15856989918287211863"}, {"file": "libs/kgx/tsconfig.lib.json", "hash": "16426570990577228651"}]}}}, "externalNodes": {"npm:@aws-sdk/client-s3": {"type": "npm", "name": "npm:@aws-sdk/client-s3", "data": {"version": "^3.782.0", "packageName": "@aws-sdk/client-s3", "hash": "974f6f3fac93a6d810af3a0f484652ec6a352f3598a588495a586a141274afa7"}}, "npm:@elastic/elasticsearch": {"type": "npm", "name": "npm:@elastic/elasticsearch", "data": {"version": "^8.17.1", "packageName": "@elastic/elasticsearch", "hash": "ca7aead51e697318a9e1e0c9c10cec0f6a8b16ebb781de95127633ef56acf697"}}, "npm:@nestjs/axios": {"type": "npm", "name": "npm:@nestjs/axios", "data": {"version": "^4.0.0", "packageName": "@nestjs/axios", "hash": "7453cc44c271389a6b5b64dd43d3a0e081c3d6d9e40faef61fdbd268bbeb8447"}}, "npm:@nestjs/bull": {"type": "npm", "name": "npm:@nestjs/bull", "data": {"version": "^11.0.2", "packageName": "@nestjs/bull", "hash": "c7a6f70aed0324db482f4cfdcfc6d9fae999a4513358baca856e4ac328802288"}}, "npm:@nestjs/cache-manager": {"type": "npm", "name": "npm:@nestjs/cache-manager", "data": {"version": "^3.0.1", "packageName": "@nestjs/cache-manager", "hash": "37bc8e3e9b8e5188683ad753df44cbcac18b2cc4f238f7a4dc8b8a0ef3077cef"}}, "npm:@nestjs/common": {"type": "npm", "name": "npm:@nestjs/common", "data": {"version": "^10.0.0", "packageName": "@nestjs/common", "hash": "a7079680326c6b0ba3a8efdbddc6e976e68459e34137c7c5202cd1a3f68abe09"}}, "npm:@nestjs/config": {"type": "npm", "name": "npm:@nestjs/config", "data": {"version": "^3.0.0", "packageName": "@nestjs/config", "hash": "25465e54fb71124475841c97753755b7d54bf29683c90a890f557922ae2d2076"}}, "npm:@nestjs/core": {"type": "npm", "name": "npm:@nestjs/core", "data": {"version": "^10.0.0", "packageName": "@nestjs/core", "hash": "9660d910933802284ef35239d4834020821b4d8834734d1686c8f2da71f20e05"}}, "npm:@nestjs/event-emitter": {"type": "npm", "name": "npm:@nestjs/event-emitter", "data": {"version": "^3.0.1", "packageName": "@nestjs/event-emitter", "hash": "24c33d082e392e4a2c1f68a431d02498488d8a314338ada881e44f92f506e9d2"}}, "npm:@nestjs/jwt": {"type": "npm", "name": "npm:@nestjs/jwt", "data": {"version": "^10.1.0", "packageName": "@nestjs/jwt", "hash": "812f84c27609a53ce3aae796d5c5ab5b1720300d24247541da7f39005b4c3ad1"}}, "npm:@nestjs/microservices": {"type": "npm", "name": "npm:@nestjs/microservices", "data": {"version": "^10.0.0", "packageName": "@nestjs/microservices", "hash": "f52f5c6f8a22495c85ca085ef4f0417b3e0c736df3bc2b10d31ab10ebb2cac4d"}}, "npm:@nestjs/passport": {"type": "npm", "name": "npm:@nestjs/passport", "data": {"version": "^10.0.0", "packageName": "@nestjs/passport", "hash": "c54e844f9ff31e850d6d4ad0ed4c013cfc114d7b917b967ff82e90cb3b29a08f"}}, "npm:@nestjs/platform-express": {"type": "npm", "name": "npm:@nestjs/platform-express", "data": {"version": "^10.0.0", "packageName": "@nestjs/platform-express", "hash": "cf5859931384d91f158bf2862c93ad832bf12e3a06c0bfafa50ec7df9fb272ef"}}, "npm:@nestjs/schedule": {"type": "npm", "name": "npm:@nestjs/schedule", "data": {"version": "^5.0.1", "packageName": "@nestjs/schedule", "hash": "627d0229e548a9d7582b2e67aefba2b3ff01c78dfd30e1d3d06b3e595e974b67"}}, "npm:@nestjs/swagger": {"type": "npm", "name": "npm:@nestjs/swagger", "data": {"version": "^7.0.0", "packageName": "@nestjs/swagger", "hash": "9a2e1ead39e165b62920d236261734f21ea82f8c52ad4de80f19f0596b2075c6"}}, "npm:@nestjs/terminus": {"type": "npm", "name": "npm:@nestjs/terminus", "data": {"version": "^11.0.0", "packageName": "@nestjs/terminus", "hash": "9c18d6619bacc80be06cc13e46bd2ea5839013fb7f85f89ac5c1e7aff323253b"}}, "npm:@nestjs/typeorm": {"type": "npm", "name": "npm:@nestjs/typeorm", "data": {"version": "^10.0.0", "packageName": "@nestjs/typeorm", "hash": "071dee06c9c026175e6659cede0e5f6f4b23c4372f236138015ab52b6a5a5349"}}, "npm:@nestjs/websockets": {"type": "npm", "name": "npm:@nestjs/websockets", "data": {"version": "^10.0.0", "packageName": "@nestjs/websockets", "hash": "16553683bbd62d89e25b64ae2df6058ad03db701a80ce0356174a87fdc97e8bb"}}, "npm:@opentelemetry/auto-instrumentations-node": {"type": "npm", "name": "npm:@opentelemetry/auto-instrumentations-node", "data": {"version": "^0.40.0", "packageName": "@opentelemetry/auto-instrumentations-node", "hash": "da538ddb10435ce8f82abb6a112a94e7551adc94a4e523d95756f81ce644f8a6"}}, "npm:@opentelemetry/exporter-trace-otlp-http": {"type": "npm", "name": "npm:@opentelemetry/exporter-trace-otlp-http", "data": {"version": "^0.45.0", "packageName": "@opentelemetry/exporter-trace-otlp-http", "hash": "804744a6efe42db24087217190914ef73a7c06f069641c75743544ec93aaad8a"}}, "npm:@opentelemetry/resources": {"type": "npm", "name": "npm:@opentelemetry/resources", "data": {"version": "^1.18.1", "packageName": "@opentelemetry/resources", "hash": "5cf16e7752ab91d2d3157e8c96f2587c3ccac7f4c66772a536cd94ad6aeb36ca"}}, "npm:@opentelemetry/sdk-node": {"type": "npm", "name": "npm:@opentelemetry/sdk-node", "data": {"version": "^0.45.0", "packageName": "@opentelemetry/sdk-node", "hash": "ff09ed5ff9f2e758439873f83a43b3182f9865d6b9a10a28875c31fc4c04388c"}}, "npm:@opentelemetry/semantic-conventions": {"type": "npm", "name": "npm:@opentelemetry/semantic-conventions", "data": {"version": "^1.18.1", "packageName": "@opentelemetry/semantic-conventions", "hash": "4de6d59fab62a6305a6e73a388130eeb497406d8338b3f50a1409a0fe1bb58d1"}}, "npm:@types/eventsource": {"type": "npm", "name": "npm:@types/eventsource", "data": {"version": "^3.0.0", "packageName": "@types/eventsource", "hash": "966fcca15222bd6dbea881487eded990a26cda22e1e290bc579f199a4893ae04"}}, "npm:@types/node-fetch": {"type": "npm", "name": "npm:@types/node-fetch", "data": {"version": "^2.6.12", "packageName": "@types/node-fetch", "hash": "21a6c27b986e3ef49a3290638b8cc0daf83f833995c5577ff62a47c0e407094d"}}, "npm:@types/node-schedule": {"type": "npm", "name": "npm:@types/node-schedule", "data": {"version": "^2.1.7", "packageName": "@types/node-schedule", "hash": "b057b15ac093d2144d3574d46832ea598d114ea1b0c0ca1c0ef94c753d7f2e1f"}}, "npm:ajv": {"type": "npm", "name": "npm:ajv", "data": {"version": "^8.17.1", "packageName": "ajv", "hash": "942cec07305c324cb59be2caf6805a25a70e655b076792f6290f616d84013187"}}, "npm:amqplib": {"type": "npm", "name": "npm:amqplib", "data": {"version": "^0.10.7", "packageName": "amqplib", "hash": "efde7cfbd1694147c5bfb5563a7dc4e0bc3f1237d6929d9bd3225145419d9822"}}, "npm:axios": {"type": "npm", "name": "npm:axios", "data": {"version": "^1.7.7", "packageName": "axios", "hash": "f3fd0d9dfc823ffc76fefbab852e56a30eca4c6f3778458902dae37bf1d9b32a"}}, "npm:better-sqlite3": {"type": "npm", "name": "npm:better-sqlite3", "data": {"version": "^11.9.1", "packageName": "better-sqlite3", "hash": "ca10bb33ca5d6cfa2a7b6264737458bd253ccc177c0a6f23f723a5f23d46e77f"}}, "npm:bull": {"type": "npm", "name": "npm:bull", "data": {"version": "^4.10.0", "packageName": "bull", "hash": "371467d3e6e7675dc138fd5dad5fbe1dc53db965a28d022f5f55d5eea17f3a33"}}, "npm:bullmq": {"type": "npm", "name": "npm:bullmq", "data": {"version": "^3.15.0", "packageName": "bullmq", "hash": "ecacdbf25380bfec9e327a9afbe11e29a4067db7022225aaad07254bf6e9c1df"}}, "npm:cache-manager": {"type": "npm", "name": "npm:cache-manager", "data": {"version": "^6.4.2", "packageName": "cache-manager", "hash": "6d4c5aa12a4579479bf2a4ed5db8e45ac335baa1b4deb4a87cd65dd54faeda1e"}}, "npm:class-transformer": {"type": "npm", "name": "npm:class-transformer", "data": {"version": "^0.5.1", "packageName": "class-transformer", "hash": "c91f1422dcf537d6c9ca32d50f06cb281a2235375b13d17343c17250e0def243"}}, "npm:class-validator": {"type": "npm", "name": "npm:class-validator", "data": {"version": "^0.14.0", "packageName": "class-validator", "hash": "b62c2ae64661a9af332bbfe3a4773e2c45ae704be7caac04784730647754825f"}}, "npm:csv-parser": {"type": "npm", "name": "npm:csv-parser", "data": {"version": "^3.2.0", "packageName": "csv-parser", "hash": "73538d277773106b950d64acf89b535ee9e9b1e220cecd166c47e510cc706268"}}, "npm:csv-stringify": {"type": "npm", "name": "npm:csv-stringify", "data": {"version": "^6.5.2", "packageName": "csv-stringify", "hash": "b65f67f1569ccf6ebc8774d5c97dee636b68780acdc66fc83824b3eedb902fe2"}}, "npm:date-fns": {"type": "npm", "name": "npm:date-fns", "data": {"version": "^4.1.0", "packageName": "date-fns", "hash": "551a8f6ca69103cbcc445fa37f95cc090a547ea5896dbf71f1a38defbe6c0a64"}}, "npm:dotenv": {"type": "npm", "name": "npm:dotenv", "data": {"version": "^16.0.0", "packageName": "dotenv", "hash": "c11f8591c334fa62d293a6a993a5c46963a7f00e1916e508fee1f2423dd0c4d8"}}, "npm:eventsource": {"type": "npm", "name": "npm:eventsource", "data": {"version": "^3.0.6", "packageName": "eventsource", "hash": "48996410526597b23a82732742c53ab082c7f78edd28f49653725243c3694ae1"}}, "npm:fast-csv": {"type": "npm", "name": "npm:fast-csv", "data": {"version": "^5.0.2", "packageName": "fast-csv", "hash": "9f69847ee1f4e3f43400ce8096739b369a939a369b07f798b110b655d80eee84"}}, "npm:ioredis": {"type": "npm", "name": "npm:i<PERSON>is", "data": {"version": "^5.6.0", "packageName": "i<PERSON>is", "hash": "445bee06c00b8445ddfbbcfcfc06b98aebc1a01edb53b20a7b35a3ac6dde7de5"}}, "npm:joi": {"type": "npm", "name": "npm:joi", "data": {"version": "^17.9.0", "packageName": "joi", "hash": "495be4dce5dfb34e6c34af3f5e38ae03e4784875ed7ccce4bcc68da46383be5b"}}, "npm:jq": {"type": "npm", "name": "npm:jq", "data": {"version": "^1.7.2", "packageName": "jq", "hash": "7dce57bcc6be17002f0e1159721710d09193a4e9446c307f2ec20e501a1796ec"}}, "npm:kafkajs": {"type": "npm", "name": "npm:kafkajs", "data": {"version": "^2.2.0", "packageName": "kafka<PERSON>s", "hash": "ec8b47fd6a3ec5e00c78c0046821a2119c226886bf6017f96e67571b050d36c8"}}, "npm:keyv": {"type": "npm", "name": "npm:keyv", "data": {"version": "^5.1.0", "packageName": "keyv", "hash": "c183edee94e9a905d6cdb75bd95cafa80d17d11b6387b41b9c32f0db39fab9d4"}}, "npm:moment-timezone": {"type": "npm", "name": "npm:moment-timezone", "data": {"version": "^0.5.48", "packageName": "moment-timezone", "hash": "5210a843c3024bfbb9ae555a6ba240a4e916360f85531656692d2855adfae658"}}, "npm:mongodb": {"type": "npm", "name": "npm:mongodb", "data": {"version": "^6.15.0", "packageName": "mongodb", "hash": "a511856b352b432e4ebea6efea7086e0d2bbf6d5a157abc814eb51ba4c165dd8"}}, "npm:mqtt": {"type": "npm", "name": "npm:mqtt", "data": {"version": "^5.10.4", "packageName": "mqtt", "hash": "011ad7b262abe07bbec95e58a0cd41ed96fbe1b8d1a711c3fed2a264204ab56f"}}, "npm:mssql": {"type": "npm", "name": "npm:mssql", "data": {"version": "^11.0.1", "packageName": "mssql", "hash": "0dbe77e226d6821ddf2dec79907480b9338139d582ee944267ca4cfb804d5a5f"}}, "npm:mysql2": {"type": "npm", "name": "npm:mysql2", "data": {"version": "^3.2.0", "packageName": "mysql2", "hash": "4df838825e04e68aa96b99e319650404c0270a78ae5f4a48441e7b9d983fad50"}}, "npm:node-schedule": {"type": "npm", "name": "npm:node-schedule", "data": {"version": "^2.1.1", "packageName": "node-schedule", "hash": "746071fb74b79a8075fd224b559d37f7832d4df2084d8e22ea978db93c65d3a3"}}, "npm:oracledb": {"type": "npm", "name": "npm:oracledb", "data": {"version": "^6.8.0", "packageName": "oracledb", "hash": "a32853a748d5c4de5696f9628f54ba294f0ec4d660223392aa35f8c747976cc3"}}, "npm:passport": {"type": "npm", "name": "npm:passport", "data": {"version": "^0.6.0", "packageName": "passport", "hash": "374f450d8609f6f2f526bea023a903b16d669e9b12d521c47f5517b2c890343a"}}, "npm:passport-jwt": {"type": "npm", "name": "npm:passport-jwt", "data": {"version": "^4.0.1", "packageName": "passport-jwt", "hash": "c84e47b569376eedcc81bcd4bc59f925e629c3e60b3f5b39ac4ad9efb9127319"}}, "npm:pg": {"type": "npm", "name": "npm:pg", "data": {"version": "^8.15.6", "packageName": "pg", "hash": "6942dfdafdcd6f8abac2130530b7b41f5f13a1235ad806b1d55251d1506bf316"}}, "npm:pino": {"type": "npm", "name": "npm:pino", "data": {"version": "^8.11.0", "packageName": "pino", "hash": "c6a8a06a64d9324772c95c29a21292014bfa7cde130834948acac71377daee31"}}, "npm:pino-http": {"type": "npm", "name": "npm:pino-http", "data": {"version": "^8.3.1", "packageName": "pino-http", "hash": "dfa8cdb7e71b1620c17d141526c7d70d014c607c41a2a0e492a6fa9b739fa8cf"}}, "npm:prom-client": {"type": "npm", "name": "npm:prom-client", "data": {"version": "^14.2.0", "packageName": "prom-client", "hash": "3722c97b0d3143829a2a97eacef4949f39d8adcde586936d3ea5fda57216a3cc"}}, "npm:react": {"type": "npm", "name": "npm:react", "data": {"version": "^18.3.1", "packageName": "react", "hash": "ff4dde4edf09ec8228a70df239a060a0885617a242a6070c278341f7fce71058"}}, "npm:react-dom": {"type": "npm", "name": "npm:react-dom", "data": {"version": "^18.3.1", "packageName": "react-dom", "hash": "a0c164cbb0d9b401b0b84ac757b380c41588170d1db2bd6125763d50df1ad2f4"}}, "npm:redis": {"type": "npm", "name": "npm:redis", "data": {"version": "^4.6.0", "packageName": "redis", "hash": "e9e1e5ab338ac3b86139add917e336fbb5fc32bf14c288b94c9c5d456e155bf6"}}, "npm:reflect-metadata": {"type": "npm", "name": "npm:reflect-metadata", "data": {"version": "^0.1.13", "packageName": "reflect-metadata", "hash": "33e28b9a14340ac895a9d3e1b020a443fe2a6d574efb0b85f8bc43cbb02f27ed"}}, "npm:rxjs": {"type": "npm", "name": "npm:rxjs", "data": {"version": "^7.8.0", "packageName": "rxjs", "hash": "107a6a3fc13e57f299610eab1a33dc686ee08fd4687e0af6b755d959fe960c1b"}}, "npm:socket.io": {"type": "npm", "name": "npm:socket.io", "data": {"version": "^4.8.1", "packageName": "socket.io", "hash": "f0e48b24e80f9dad04433bdd65d570024524aebff7d69e91e24877408d55b018"}}, "npm:sqlite": {"type": "npm", "name": "npm:sqlite", "data": {"version": "^5.1.1", "packageName": "sqlite", "hash": "54ef9d2514118aa75e77e80a301e658d5a5c1cd185b6e76d66cfc616e74de2f0"}}, "npm:sqlite3": {"type": "npm", "name": "npm:sqlite3", "data": {"version": "^5.1.7", "packageName": "sqlite3", "hash": "d75db2ddba2e095115c338d7e34e66f0c361af402837ef65731c656eb7d1f865"}}, "npm:ssh2-sftp-client": {"type": "npm", "name": "npm:ssh2-sftp-client", "data": {"version": "^12.0.0", "packageName": "ssh2-sftp-client", "hash": "6db81baa9dd37284e0a4d21f5e5028bd793a8f3a8661a4e8417087b0afee3cc3"}}, "npm:tslib": {"type": "npm", "name": "npm:tslib", "data": {"version": "^2.8.1", "packageName": "tslib", "hash": "266f71c5cfdbd3ee344e7270e04fff3c59f3e0288ccd2b5552a32aa531f1f9aa"}}, "npm:typeorm": {"type": "npm", "name": "npm:typeorm", "data": {"version": "^0.3.0", "packageName": "typeorm", "hash": "a5410af2da42f80819483503e904c5caa542bd33ea69f8ab2dc6c34ad867ba8f"}}, "npm:uuid": {"type": "npm", "name": "npm:uuid", "data": {"version": "^11.1.0", "packageName": "uuid", "hash": "0a08f4ba04824197f7c3b84b1bcd1d2bcba6a38b76f004abceea1e672e56d4cc"}}, "npm:ws": {"type": "npm", "name": "npm:ws", "data": {"version": "^8.18.1", "packageName": "ws", "hash": "bc77e3c14f9e0a4410ed98dca32d53ef3a93a1dc0921cbbfc792c75819546770"}}, "npm:xml2js": {"type": "npm", "name": "npm:xml2js", "data": {"version": "^0.6.2", "packageName": "xml2js", "hash": "0b82a944133028da47160ee219b7b32ea35109f02889f38e39bff5d14b8dfd03"}}, "npm:yahoo-finance2": {"type": "npm", "name": "npm:yahoo-finance2", "data": {"version": "^2.13.3", "packageName": "yahoo-finance2", "hash": "e941f6f84bbb958cdcf56b666b65e0b6eecd37d6f18a5203adfcaecea4ace780"}}, "npm:yaml": {"type": "npm", "name": "npm:yaml", "data": {"version": "^2.2.0", "packageName": "yaml", "hash": "9afc4878241801199b6e16b7817ccc7065d14d9b0623812ca32fc7b370720183"}}, "npm:@faker-js/faker": {"type": "npm", "name": "npm:@faker-js/faker", "data": {"version": "^9.6.0", "packageName": "@faker-js/faker", "hash": "7f7e2ccc984bfe8b09e68d85806fdd80ecef4012e85702fc59e5321ded6257fe"}}, "npm:@nestjs/cli": {"type": "npm", "name": "npm:@nestjs/cli", "data": {"version": "^10.0.0", "packageName": "@nestjs/cli", "hash": "b12b5b72f6e70629b9c3dcbbff650671f7c7482cdd9d7029dc7a44e36e7285a7"}}, "npm:@nestjs/schematics": {"type": "npm", "name": "npm:@nestjs/schematics", "data": {"version": "^10.0.0", "packageName": "@nestjs/schematics", "hash": "5f0e0bd1964b8cfd7d306ac159c9b9b5fa8d21f46633c1ca882602a93eb06a0b"}}, "npm:@nestjs/testing": {"type": "npm", "name": "npm:@nestjs/testing", "data": {"version": "^10.0.0", "packageName": "@nestjs/testing", "hash": "86489ae7caaf8d476bf63a82929e754796135e780b3a6ab161c2adf3cfe67476"}}, "npm:@nrwl/eslint-plugin-nx": {"type": "npm", "name": "npm:@nrwl/eslint-plugin-nx", "data": {"version": "16.0.0", "packageName": "@nrwl/eslint-plugin-nx", "hash": "4c58128fe5ac2f27e66d96d42f0212f2705f6c95facf3f87707cf77a0e7d4b35"}}, "npm:@nrwl/jest": {"type": "npm", "name": "npm:@nrwl/jest", "data": {"version": "16.0.0", "packageName": "@nrwl/jest", "hash": "c102022512ef51068473630f399e1b97f4100fa6a2feb89fbe218b98c4939767"}}, "npm:@nrwl/js": {"type": "npm", "name": "npm:@nrwl/js", "data": {"version": "16.0.0", "packageName": "@nrwl/js", "hash": "162daa4a661f8d5a1c3b28316a268f019708fa670f6fd8debc88ac92b63dd835"}}, "npm:@nrwl/linter": {"type": "npm", "name": "npm:@nrwl/linter", "data": {"version": "16.0.0", "packageName": "@nrwl/linter", "hash": "27767410355e58ffd100cc660431c4da163cce8dba1798d7ff1b4f99b8a88fb6"}}, "npm:@nrwl/nest": {"type": "npm", "name": "npm:@nrwl/nest", "data": {"version": "16.0.0", "packageName": "@nrwl/nest", "hash": "7805b25c7c04af7dea02bfafec8d9b4c51c7fcccac4ccbfb2b74c4669cd73201"}}, "npm:@nrwl/node": {"type": "npm", "name": "npm:@nrwl/node", "data": {"version": "16.0.0", "packageName": "@nrwl/node", "hash": "05ccab757e16e84c7f31c371707ddc4f70483ae9143769346314e443fa3aec57"}}, "npm:@nrwl/nx-cloud": {"type": "npm", "name": "npm:@nrwl/nx-cloud", "data": {"version": "16.0.0", "packageName": "@nrwl/nx-cloud", "hash": "138861cb43ad02db3cd05b5dd7a9d6995931cd0169b6a81fd4f6976adc916a27"}}, "npm:@nrwl/webpack": {"type": "npm", "name": "npm:@nrwl/webpack", "data": {"version": "^19.8.4", "packageName": "@nrwl/webpack", "hash": "68385ac5ec2278bfede3f6326606d1ec8173c96e21b4eecc8c5630fa44bf3fe1"}}, "npm:@nrwl/workspace": {"type": "npm", "name": "npm:@nrwl/workspace", "data": {"version": "16.0.0", "packageName": "@nrwl/workspace", "hash": "6a248ac3431b4dab8b769496fcaaee6aeedca3279d3e47fda5d713d552d19135"}}, "npm:@opentelemetry/api": {"type": "npm", "name": "npm:@opentelemetry/api", "data": {"version": "~1.7.0", "packageName": "@opentelemetry/api", "hash": "6f04150fc031103adcf3cd7d97b3a319d116465194a4877e997392bc1e16503f"}}, "npm:@types/amqplib": {"type": "npm", "name": "npm:@types/amqplib", "data": {"version": "^0.10.7", "packageName": "@types/amqplib", "hash": "c6320a41cfba7d472b4a8886640f747ccf0d03fdc63f9e5862994af877192f97"}}, "npm:@types/better-sqlite3": {"type": "npm", "name": "npm:@types/better-sqlite3", "data": {"version": "^7.6.13", "packageName": "@types/better-sqlite3", "hash": "e5a4d09b0aed972c25ce4da08734285c1fbc0378fedb395163be70424d979405"}}, "npm:@types/bull": {"type": "npm", "name": "npm:@types/bull", "data": {"version": "^4.10.0", "packageName": "@types/bull", "hash": "8bb0c0cc745c0bc409d920380c0d0acd2cc9901cb09bac3b938e6c44f541592b"}}, "npm:@types/express": {"type": "npm", "name": "npm:@types/express", "data": {"version": "^4.17.0", "packageName": "@types/express", "hash": "f3aa93d10c49454c07f2057571fb06650fdc91a4340824c59a475ef468aa40c9"}}, "npm:@types/jest": {"type": "npm", "name": "npm:@types/jest", "data": {"version": "^29.5.14", "packageName": "@types/jest", "hash": "be480f6e87c702bb850e865416c64792d6558dad7cf34358d79ea32783d1e046"}}, "npm:@types/js-yaml": {"type": "npm", "name": "npm:@types/js-yaml", "data": {"version": "^4.0.9", "packageName": "@types/js-yaml", "hash": "7c91fddcb2d536faa6644d4ee2bbe6eb78f49eab1262093e42f8896687102320"}}, "npm:@types/mssql": {"type": "npm", "name": "npm:@types/mssql", "data": {"version": "^9.1.7", "packageName": "@types/mssql", "hash": "9387f57fc8b220154eac3440230f93ed4f93208d0efa25e9a7b853b0c4460053"}}, "npm:@types/node": {"type": "npm", "name": "npm:@types/node", "data": {"version": "^18.0.0", "packageName": "@types/node", "hash": "eca889ad970942c19fbc11307dfba37337e4567bd00315f8f0ca96589c462578"}}, "npm:@types/oracledb": {"type": "npm", "name": "npm:@types/oracledb", "data": {"version": "^6.5.4", "packageName": "@types/oracledb", "hash": "95341ea359976025103ff111286b7b2232917db7d600f67cc7dbf2bb2c8b14a9"}}, "npm:@types/passport-jwt": {"type": "npm", "name": "npm:@types/passport-jwt", "data": {"version": "^4.0.1", "packageName": "@types/passport-jwt", "hash": "d1241f38efe4c287d1d21e3a3fe00d71b2a9357d981b4f64d08ee03c8635d1d6"}}, "npm:@types/pg": {"type": "npm", "name": "npm:@types/pg", "data": {"version": "^8.11.14", "packageName": "@types/pg", "hash": "affc1e0b1175f5416822e3e8dbcfbc8126bfa795006038a929d11247892c1d2c"}}, "npm:@types/sqlite3": {"type": "npm", "name": "npm:@types/sqlite3", "data": {"version": "^5.1.0", "packageName": "@types/sqlite3", "hash": "48315aa252fe5cb6f253c59373c9882a143468f968549545a16e125cdaf851e8"}}, "npm:@types/ssh2-sftp-client": {"type": "npm", "name": "npm:@types/ssh2-sftp-client", "data": {"version": "^9.0.4", "packageName": "@types/ssh2-sftp-client", "hash": "484e863e61d3faabe2a0daf77690490bb3651fc09b5ced5519a9e5951b096a13"}}, "npm:@types/uuid": {"type": "npm", "name": "npm:@types/uuid", "data": {"version": "^10.0.0", "packageName": "@types/uuid", "hash": "f60c47d6d9e9a4e10a70e1646ce7d1b3bee85ace29ceb10a17208d621fc14134"}}, "npm:@types/ws": {"type": "npm", "name": "npm:@types/ws", "data": {"version": "^8.18.1", "packageName": "@types/ws", "hash": "dfc6d83db8a071bc2fe15688480c96ca1071836aa0924060777e8c51105aa23a"}}, "npm:@types/xml2js": {"type": "npm", "name": "npm:@types/xml2js", "data": {"version": "^0.4.14", "packageName": "@types/xml2js", "hash": "01e6a9af1f35df2b7c2c00809ac80f033cb57388ca47b5ef3702fe047ce84019"}}, "npm:@typescript-eslint/eslint-plugin": {"type": "npm", "name": "npm:@typescript-eslint/eslint-plugin", "data": {"version": "^5.0.0", "packageName": "@typescript-eslint/eslint-plugin", "hash": "fed86031ecc0ac7987b95a295c8f3aa96c5cc05d150c6de5228585bfaa565fc2"}}, "npm:@typescript-eslint/parser": {"type": "npm", "name": "npm:@typescript-eslint/parser", "data": {"version": "^5.0.0", "packageName": "@typescript-eslint/parser", "hash": "998dd4abf4183cb7aa31a771b6aee1ead68b82b8b3b1aa30c3d744029f7df907"}}, "npm:concurrently": {"type": "npm", "name": "npm:concurrently", "data": {"version": "^9.1.2", "packageName": "concurrently", "hash": "2b74aca37f4ca675f57b132374edb9f76c57f73ac16f1bfa305ca9b5ff537641"}}, "npm:cross-env": {"type": "npm", "name": "npm:cross-env", "data": {"version": "^7.0.3", "packageName": "cross-env", "hash": "0fa6509097be6fb61695cba9a5837cace2beb78232f381cb7b254ce87c2fa2c1"}}, "npm:eslint": {"type": "npm", "name": "npm:eslint", "data": {"version": "^8.0.0", "packageName": "eslint", "hash": "4653081e901b4a94719910038338c70bed99487094ce4df35182f20938c476c7"}}, "npm:eslint-config-prettier": {"type": "npm", "name": "npm:eslint-config-prettier", "data": {"version": "^8.0.0", "packageName": "eslint-config-prettier", "hash": "52021fa855d106aeebfc33d8964da28b004df59e1df1c93ad43952e2860ed9f4"}}, "npm:eslint-plugin-prettier": {"type": "npm", "name": "npm:eslint-plugin-prettier", "data": {"version": "^4.0.0", "packageName": "eslint-plugin-prettier", "hash": "d83b12d1540fe0efd50865cb94b20de0643f6044dfa9259334b229b989bb5040"}}, "npm:jest": {"type": "npm", "name": "npm:jest", "data": {"version": "^29.5.0", "packageName": "jest", "hash": "1aa34fc2a7c786d2c9c74cea3458d4664f6a2a23e39fa45f86bd796fdb7b3617"}}, "npm:nodemon": {"type": "npm", "name": "npm:nodemon", "data": {"version": "^3.0.2", "packageName": "nodemon", "hash": "b78711d6c85a57f8edc451288d9e32bda3236cd2b296c9ae1306e597c322c9ef"}}, "npm:nx": {"type": "npm", "name": "npm:nx", "data": {"version": "16.0.0", "packageName": "nx", "hash": "8fcf2ae6240b41647ab3f98769aaa24b88766f6f9777c81ad5d4dfe133684267"}}, "npm:pino-pretty": {"type": "npm", "name": "npm:pino-pretty", "data": {"version": "^13.0.0", "packageName": "pino-pretty", "hash": "080852cc12d93737c6fae81050bde1030f1f2d5ab5c1d37ef10356b3bd788a3f"}}, "npm:prettier": {"type": "npm", "name": "npm:prettier", "data": {"version": "^2.0.0", "packageName": "prettier", "hash": "01b4a2af6b18ced1bebc8f287e87f044c2c369144e60d1e4464220cee065320e"}}, "npm:ts-jest": {"type": "npm", "name": "npm:ts-jest", "data": {"version": "^29.1.0", "packageName": "ts-jest", "hash": "65aa0d173a2946730a1487267968ac4a1ed568234ea45d9d4e0fa1c4176f61ec"}}, "npm:ts-loader": {"type": "npm", "name": "npm:ts-loader", "data": {"version": "^9.0.0", "packageName": "ts-loader", "hash": "92cd1305a994cd46601b704b9d6d660b97be0e2c1c77f05d09f598084c1483e4"}}, "npm:ts-node": {"type": "npm", "name": "npm:ts-node", "data": {"version": "^10.0.0", "packageName": "ts-node", "hash": "35601bd9657e6c1e4bee7c70499bef6c3744255d2c01aaffc8bcf0098545b060"}}, "npm:ts-node-dev": {"type": "npm", "name": "npm:ts-node-dev", "data": {"version": "^2.0.0", "packageName": "ts-node-dev", "hash": "4528067c233c92db7ddefbf0488b571a336371cf3dcdc4f5d704544146c68142"}}, "npm:tsconfig-paths": {"type": "npm", "name": "npm:tsconfig-paths", "data": {"version": "^4.0.0", "packageName": "tsconfig-paths", "hash": "671c5e7fb68333cb0450bb89abd319461042f03121696ef1790801c7558d6417"}}, "npm:typescript": {"type": "npm", "name": "npm:typescript", "data": {"version": "^5.0.0", "packageName": "typescript", "hash": "6311f5a5b8f7718fcb3cce6e64814452f0a42c8cdef6520cfb6121bf27d73638"}}, "npm:webpack": {"type": "npm", "name": "npm:webpack", "data": {"version": "^5.0.0", "packageName": "webpack", "hash": "74e4606042a1d5b7618f64371f8addcee2845d1b906f5ce37dbd1e6da22ccffd"}}}, "dependencies": {"monitoring": [{"source": "monitoring", "target": "npm:@nestjs/common", "type": "static"}, {"source": "monitoring", "target": "npm:rxjs", "type": "static"}, {"source": "monitoring", "target": "npm:@nestjs/core", "type": "static"}, {"source": "monitoring", "target": "npm:pino", "type": "static"}, {"source": "monitoring", "target": "npm:pino-http", "type": "static"}, {"source": "monitoring", "target": "npm:prom-client", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/sdk-node", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/auto-instrumentations-node", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/exporter-trace-otlp-http", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/resources", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/semantic-conventions", "type": "static"}, {"source": "monitoring", "target": "npm:@opentelemetry/api", "type": "static"}], "collector": [{"source": "collector", "target": "npm:@nestjs/common", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/config", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "collector", "target": "logging", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/event-emitter", "type": "static"}, {"source": "collector", "target": "security", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/core", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/schedule", "type": "static"}, {"source": "collector", "target": "core", "type": "static"}, {"source": "collector", "target": "storage", "type": "static"}, {"source": "collector", "target": "messaging", "type": "static"}, {"source": "collector", "target": "npm:uuid", "type": "static"}, {"source": "collector", "target": "npm:mysql2", "type": "static"}, {"source": "collector", "target": "npm:pg", "type": "static"}, {"source": "collector", "target": "npm:mssql", "type": "static"}, {"source": "collector", "target": "npm:oracledb", "type": "static"}, {"source": "collector", "target": "npm:better-sqlite3", "type": "static"}, {"source": "collector", "target": "npm:xml2js", "type": "static"}, {"source": "collector", "target": "npm:csv-parser", "type": "static"}, {"source": "collector", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "collector", "target": "npm:rxjs", "type": "static"}, {"source": "collector", "target": "npm:ssh2-sftp-client", "type": "static"}, {"source": "collector", "target": "npm:kafkajs", "type": "static"}, {"source": "collector", "target": "npm:amqplib", "type": "static"}, {"source": "collector", "target": "npm:i<PERSON>is", "type": "static"}, {"source": "collector", "target": "npm:mqtt", "type": "static"}, {"source": "collector", "target": "npm:ws", "type": "static"}, {"source": "collector", "target": "npm:axios", "type": "static"}, {"source": "collector", "target": "utils", "type": "static"}, {"source": "collector", "target": "npm:class-validator", "type": "static"}, {"source": "collector", "target": "npm:class-transformer", "type": "static"}, {"source": "collector", "target": "npm:@nrwl/webpack", "type": "static"}], "vite_react_shadcn_ts": [{"source": "vite_react_shadcn_ts", "target": "npm:date-fns", "type": "static"}, {"source": "vite_react_shadcn_ts", "target": "npm:react", "type": "static"}, {"source": "vite_react_shadcn_ts", "target": "npm:react-dom", "type": "static"}, {"source": "vite_react_shadcn_ts", "target": "npm:@types/node", "type": "static"}, {"source": "vite_react_shadcn_ts", "target": "npm:eslint", "type": "static"}, {"source": "vite_react_shadcn_ts", "target": "npm:typescript", "type": "static"}], "processor": [{"source": "processor", "target": "npm:@nestjs/common", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/config", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/schedule", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/event-emitter", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "processor", "target": "logging", "type": "static"}, {"source": "processor", "target": "security", "type": "static"}, {"source": "processor", "target": "storage", "type": "static"}, {"source": "processor", "target": "core", "type": "static"}, {"source": "processor", "target": "messaging", "type": "static"}, {"source": "processor", "target": "utils", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "processor", "target": "kgx", "type": "static"}, {"source": "processor", "target": "npm:rxjs", "type": "static"}, {"source": "processor", "target": "npm:pg", "type": "static"}, {"source": "processor", "target": "npm:axios", "type": "static"}, {"source": "processor", "target": "npm:typeorm", "type": "static"}, {"source": "processor", "target": "npm:uuid", "type": "static"}, {"source": "processor", "target": "npm:date-fns", "type": "static"}, {"source": "processor", "target": "npm:moment-timezone", "type": "static"}, {"source": "processor", "target": "npm:yahoo-finance2", "type": "static"}, {"source": "processor", "target": "npm:@nestjs/core", "type": "static"}, {"source": "processor", "target": "npm:class-validator", "type": "static"}, {"source": "processor", "target": "npm:class-transformer", "type": "static"}, {"source": "processor", "target": "npm:@nrwl/webpack", "type": "static"}], "scheduler": [{"source": "scheduler", "target": "npm:@nestjs/common", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/config", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/schedule", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/event-emitter", "type": "static"}, {"source": "scheduler", "target": "logging", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "scheduler", "target": "security", "type": "static"}, {"source": "scheduler", "target": "core", "type": "static"}, {"source": "scheduler", "target": "storage", "type": "static"}, {"source": "scheduler", "target": "messaging", "type": "static"}, {"source": "scheduler", "target": "utils", "type": "static"}, {"source": "scheduler", "target": "npm:uuid", "type": "static"}, {"source": "scheduler", "target": "npm:node-schedule", "type": "static"}, {"source": "scheduler", "target": "npm:@nestjs/core", "type": "static"}, {"source": "scheduler", "target": "npm:class-validator", "type": "static"}, {"source": "scheduler", "target": "npm:class-transformer", "type": "static"}, {"source": "scheduler", "target": "npm:@nrwl/webpack", "type": "static"}], "messaging": [{"source": "messaging", "target": "npm:bull", "type": "static"}, {"source": "messaging", "target": "npm:@nestjs/common", "type": "static"}, {"source": "messaging", "target": "npm:bullmq", "type": "static"}, {"source": "messaging", "target": "npm:i<PERSON>is", "type": "static"}, {"source": "messaging", "target": "utils", "type": "static"}, {"source": "messaging", "target": "npm:uuid", "type": "static"}, {"source": "messaging", "target": "npm:rxjs", "type": "static"}, {"source": "messaging", "target": "npm:ajv", "type": "static"}, {"source": "messaging", "target": "core", "type": "static"}], "security": [{"source": "security", "target": "npm:@nestjs/common", "type": "static"}, {"source": "security", "target": "npm:rxjs", "type": "static"}, {"source": "security", "target": "npm:@nestjs/core", "type": "static"}, {"source": "security", "target": "npm:@nestjs/passport", "type": "static"}, {"source": "security", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "security", "target": "npm:passport-jwt", "type": "static"}, {"source": "security", "target": "npm:axios", "type": "static"}], "logging": [{"source": "logging", "target": "npm:@nestjs/common", "type": "static"}, {"source": "logging", "target": "npm:pino", "type": "static"}], "storage": [{"source": "storage", "target": "npm:typeorm", "type": "static"}, {"source": "storage", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "storage", "target": "npm:@nestjs/common", "type": "static"}, {"source": "storage", "target": "npm:dotenv", "type": "static"}, {"source": "storage", "target": "npm:@nestjs/config", "type": "static"}, {"source": "storage", "target": "npm:@nestjs/schedule", "type": "static"}, {"source": "storage", "target": "npm:mysql2", "type": "static"}, {"source": "storage", "target": "npm:pg", "type": "static"}, {"source": "storage", "target": "npm:uuid", "type": "static"}], "writer": [{"source": "writer", "target": "npm:@nestjs/common", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/config", "type": "static"}, {"source": "writer", "target": "logging", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/event-emitter", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "writer", "target": "security", "type": "static"}, {"source": "writer", "target": "core", "type": "static"}, {"source": "writer", "target": "storage", "type": "static"}, {"source": "writer", "target": "messaging", "type": "static"}, {"source": "writer", "target": "utils", "type": "static"}, {"source": "writer", "target": "kgx", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "writer", "target": "npm:uuid", "type": "static"}, {"source": "writer", "target": "npm:@elastic/elasticsearch", "type": "static"}, {"source": "writer", "target": "npm:csv-stringify", "type": "static"}, {"source": "writer", "target": "npm:mongodb", "type": "static"}, {"source": "writer", "target": "npm:typeorm", "type": "static"}, {"source": "writer", "target": "npm:@aws-sdk/client-s3", "type": "static"}, {"source": "writer", "target": "npm:fast-csv", "type": "static"}, {"source": "writer", "target": "npm:@nestjs/core", "type": "static"}, {"source": "writer", "target": "npm:class-validator", "type": "static"}, {"source": "writer", "target": "npm:@nrwl/webpack", "type": "static"}], "config": [{"source": "config", "target": "npm:@nestjs/common", "type": "static"}, {"source": "config", "target": "npm:ajv", "type": "static"}, {"source": "config", "target": "npm:yaml", "type": "static"}], "cache": [{"source": "cache", "target": "npm:@nestjs/common", "type": "static"}, {"source": "cache", "target": "npm:redis", "type": "static"}], "utils": [{"source": "utils", "target": "npm:@nestjs/common", "type": "static"}], "core": [{"source": "core", "target": "npm:uuid", "type": "static"}, {"source": "core", "target": "npm:@nestjs/common", "type": "static"}, {"source": "core", "target": "npm:prom-client", "type": "static"}, {"source": "core", "target": "utils", "type": "implicit"}], "api": [{"source": "api", "target": "npm:@nestjs/common", "type": "static"}, {"source": "api", "target": "logging", "type": "static"}, {"source": "api", "target": "npm:@nestjs/jwt", "type": "static"}, {"source": "api", "target": "npm:@nestjs/event-emitter", "type": "static"}, {"source": "api", "target": "kgx", "type": "static"}, {"source": "api", "target": "security", "type": "static"}, {"source": "api", "target": "npm:@nestjs/core", "type": "static"}, {"source": "api", "target": "npm:@nestjs/config", "type": "static"}, {"source": "api", "target": "storage", "type": "static"}, {"source": "api", "target": "cache", "type": "static"}, {"source": "api", "target": "core", "type": "static"}, {"source": "api", "target": "messaging", "type": "static"}, {"source": "api", "target": "npm:pg", "type": "static"}, {"source": "api", "target": "utils", "type": "static"}, {"source": "api", "target": "npm:@nestjs/axios", "type": "static"}, {"source": "api", "target": "npm:rxjs", "type": "static"}, {"source": "api", "target": "npm:@nestjs/typeorm", "type": "static"}, {"source": "api", "target": "npm:typeorm", "type": "static"}, {"source": "api", "target": "npm:@nestjs/swagger", "type": "static"}, {"source": "api", "target": "npm:uuid", "type": "static"}, {"source": "api", "target": "npm:class-validator", "type": "static"}, {"source": "api", "target": "npm:@nrwl/webpack", "type": "static"}], "kgx": [{"source": "kgx", "target": "npm:typeorm", "type": "static"}]}}