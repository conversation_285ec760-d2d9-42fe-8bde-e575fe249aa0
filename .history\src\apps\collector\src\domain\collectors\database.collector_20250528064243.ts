import { Injectable, Logger } from "@nestjs/common";
import { Collector } from "./collector.interface";
import { CollectionCollector, CollectorCollectionResult } from "@data-pipeline/storage";
import * as mysql from "mysql2/promise";
import { Pool } from "pg";
import * as mssql from "mssql";
import * as oracledb from "oracledb";
import Database from "better-sqlite3";
import {
  getErrorMessage,
  getErrorStack,
  formatError,
  CollectorType,
  CollectionStatus,
} from "@data-pipeline/core";

@Injectable()
export class DatabaseCollector implements Collector {
  private readonly logger = new Logger(DatabaseCollector.name);

  canHandle(collector: CollectionCollector): boolean {
    return collector.type === CollectorType.DATABASE;
  }

  async collect(collector: CollectionCollector): Promise<CollectorCollectionResult> {
    if (!this.canHandle(collector)) {
      throw new Error(
        `DatabaseCollector cannot handle collector type: ${collector.type}`
      );
    }

    const startTime = Date.now();
    const config = collector.config as any;

    try {
      this.logger.debug(
        `Collecting data from ${config.type} database: ${config.database}`
      );

      let data: any[] = [];

      switch (config.type) {
        case "mysql":
          data = await this.collectFromMySql(collector);
          break;
        case "postgres":
          data = await this.collectFromPostgres(collector);
          break;
        case "mssql":
          data = await this.collectFromMsSql(collector);
          break;
        case "oracle":
          data = await this.collectFromOracle(collector);
          break;
        case "sqlite":
          data = await this.collectFromSqlite(collector);
          break;
        default:
          throw new Error(`Unsupported database type: ${config.type}`);
      }

      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.debug(
        `Database collection completed in ${duration}ms: ${data.length} records`
      );

      return {
        id: `${collector.id}_${startTime}`,
        collector_id: collector.id,
        status: CollectionStatus.SUCCESS,
        data,
        metadata: {
          timestamp: new Date(),
          duration_ms: duration,
          record_count: data.length,
          database_type: config.type,
          database_name: config.database,
        },
        created_at: new Date(),
      };
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.error(
        `Error collecting from database: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );

      return {
        id: `${collector.id}_${startTime}`,
        collector_id: collector.id,
        status: CollectionStatus.FAILURE,
        data: null,
        metadata: {
          timestamp: new Date(),
          duration_ms: duration,
          error: formatError(error as Error),
          database_type: config.type,
          database_name: config.database,
        },
        created_at: new Date(),
      };
    }
  }

  validateConfig(collector: CollectionCollector): boolean {
    if (!this.canHandle(collector)) {
      return false;
    }

    const config = collector.config as any;

    if (!config.type || !config.database || !config.query) {
      return false;
    }

    if (!config.connectionString && (config.type !== "sqlite" && !config.host)) {
      return false;
    }

    return true;
  }

  private async collectFromMySql(
    collector: CollectionCollector
  ): Promise<any[]> {
    const config = collector.config as any;
    const auth = collector.auth?.basic;

    let connection;
    try {
      if (config.connectionString) {
        connection = await mysql.createConnection(config.connectionString);
      } else {
        connection = await mysql.createConnection({
          host: config.host,
          port: config.port || 3306,
          user: auth?.username,
          password: auth?.password,
          database: config.database,
          connectTimeout: config.timeout || 10000,
        });
      }

      const [rows] = await connection.query({
        sql: config.query,
        values: config.parameters || [],
        timeout: config.timeout || 30000,
      });

      const result =
        config.maxRows && config.maxRows > 0
          ? (rows as any[]).slice(0, config.maxRows)
          : rows;

      return result as any[];
    } finally {
      if (connection) {
        await connection.end();
      }
    }
  }

  private async collectFromPostgres(
    collector: CollectionCollector
  ): Promise<any[]> {
    const config = collector.config as any;
    const auth = collector.auth?.basic;

    let client;
    let pool;
    try {
      if (config.connectionString) {
        pool = new Pool({
          connectionString: config.connectionString,
          connectionTimeoutMillis: config.timeout || 10000,
        });
      } else {
        pool = new Pool({
          host: config.host,
          port: config.port || 5432,
          user: auth?.username,
          password: auth?.password,
          database: config.database,
          connectionTimeoutMillis: config.timeout || 10000,
        });
      }

      client = await pool.connect();

      const result = await client.query({
        text: config.query,
        values: config.parameters || [],
        rowMode: "array",
      });

      return config.maxRows && config.maxRows > 0
        ? result.rows.slice(0, config.maxRows)
        : result.rows;
    } finally {
      if (client) {
        client.release();
      }
      if (pool) {
        await pool.end();
      }
    }
  }

  private async collectFromMsSql(
    collector: CollectionCollector
  ): Promise<any[]> {
    const config = collector.config as any;
    const auth = collector.auth?.basic;

    try {
      let connectionConfig;
      if (config.connectionString) {
        connectionConfig = config.connectionString;
      } else {
        connectionConfig = {
          server: config.host,
          port: config.port || 1433,
          user: auth?.username,
          password: auth?.password,
          database: config.database,
          options: {
            trustServerCertificate: true,
            connectTimeout: config.timeout || 10000,
          },
        };
      }

      await mssql.connect(connectionConfig);

      const result = await mssql.query(config.query);

      return config.maxRows && config.maxRows > 0
        ? result.recordset.slice(0, config.maxRows)
        : result.recordset;
    } finally {
      if (mssql.pool) {
        await mssql.pool.close();
      }
    }
  }

  private async collectFromOracle(
    collector: CollectionCollector
  ): Promise<any[]> {
    const config = collector.config as any;
    const auth = collector.auth?.basic;

    let connection;
    try {
      if (config.connectionString) {
        connection = await oracledb.getConnection({
          connectString: config.connectionString,
        });
      } else {
        connection = await oracledb.getConnection({
          user: auth?.username,
          password: auth?.password,
          connectString: `${config.host}:${config.port || 1521}/${config.database
            }`,
        });
      }

      const result = await connection.execute(
        config.query,
        config.parameters || [],
        { outFormat: oracledb.OUT_FORMAT_OBJECT }
      );

      return config.maxRows && config.maxRows > 0
        ? (result.rows as any[]).slice(0, config.maxRows)
        : (result.rows as any[]);
    } finally {
      if (connection) {
        await connection.close();
      }
    }
  }

  private async collectFromSqlite(
    collector: CollectionCollector
  ): Promise<any[]> {
    const config = collector.config as any;

    const db = await open({
      filename: config.database,
      driver: sqlite3.Database,
    });

    try {
      const rows = await db.all(config.query, config.parameters || []);

      return config.maxRows && config.maxRows > 0
        ? rows.slice(0, config.maxRows)
        : rows;
    } finally {
      await db.close();
    }
  }
}
