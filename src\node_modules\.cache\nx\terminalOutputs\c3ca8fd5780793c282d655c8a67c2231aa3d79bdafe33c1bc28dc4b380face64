chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 667 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m
webpack compiled [1m[32msuccessfully[39m[22m (2f1e649197a452fe)
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 666 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m50:11[22m[39m[1m[39m[22m
[90mTS2554: [39mExpected 4 arguments, but got 5.
  [0m [90m 48 |[39m           postgresDataSource[33m.[39mgetRepository([33mUsdIndexValueEntity[39m)[33m,[39m
   [90m 49 |[39m           postgresDataSource[33m,[39m
  [31m[1m>[22m[39m[90m 50 |[39m           mysqlService
   [90m    |[39m           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 51 |[39m         )[33m;[39m
   [90m 52 |[39m       }[33m,[39m
   [90m 53 |[39m       inject[33m:[39m [getDataSourceToken([32m'postgres'[39m)[33m,[39m [33mMySQLService[39m][33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m7:3[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m  5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m  6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m  7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m    |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m  8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m  9 |[39m
   [90m 10 |[39m [90m/**[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m325:38[22m[39m[1m[39m[22m
[90mTS2339: [39mProperty 'mysqlService' does not exist on type 'PostgresService'.
  [0m [90m 323 |[39m [32m      `[39m[33m;[39m
   [90m 324 |[39m
  [31m[1m>[22m[39m[90m 325 |[39m       [36mconst[39m mysqlResult [33m=[39m [36mawait[39m [36mthis[39m[33m.[39mmysqlService[33m.[39mquery(mysqlQuery)[33m;[39m
   [90m     |[39m                                      [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 326 |[39m
   [90m 327 |[39m       [36mif[39m (mysqlResult [33m&&[39m mysqlResult[33m.[39mlength [33m>[39m [35m0[39m) {
   [90m 328 |[39m         [36mthis[39m[33m.[39mlogger[33m.[39mlog([32m`Found ${mysqlResult.length} asset records in MySQL`[39m)[33m;[39m[0m

webpack compiled with [1m[31m5 errors[39m[22m (f0377a9ce152e7cd)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 662 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m50:11[22m[39m[1m[39m[22m
[90mTS2554: [39mExpected 4 arguments, but got 5.
  [0m [90m 48 |[39m           postgresDataSource[33m.[39mgetRepository([33mUsdIndexValueEntity[39m)[33m,[39m
   [90m 49 |[39m           postgresDataSource[33m,[39m
  [31m[1m>[22m[39m[90m 50 |[39m           mysqlService
   [90m    |[39m           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 51 |[39m         )[33m;[39m
   [90m 52 |[39m       }[33m,[39m
   [90m 53 |[39m       inject[33m:[39m [getDataSourceToken([32m'postgres'[39m)[33m,[39m [33mMySQLService[39m][33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m7:3[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m  5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m  6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m  7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m    |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m  8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m  9 |[39m
   [90m 10 |[39m [90m/**[39m[0m

webpack compiled with [1m[31m4 errors[39m[22m (40e1000fa1cfed5d)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 662 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m12:10[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 10 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 11 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m          [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 13 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 14 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 15 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m12:27[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 10 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 11 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 13 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 14 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 15 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m12:46[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 10 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 11 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 12 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 13 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 14 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 15 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m33:16[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 31 |[39m     [33mCollectorRepository[39m[33m,[39m
   [90m 32 |[39m     {
  [31m[1m>[22m[39m[90m 33 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m    |[39m                [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 34 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
   [90m 35 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m 36 |[39m       }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m35:20[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 33 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m 34 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
  [31m[1m>[22m[39m[90m 35 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m    |[39m                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 36 |[39m       }[33m,[39m
   [90m 37 |[39m       inject[33m:[39m [[33mDatabaseConfigProvider[39m][33m,[39m
   [90m 38 |[39m     }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m49:16[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'getDataSourceToken'.
  [0m [90m 47 |[39m         )[33m;[39m
   [90m 48 |[39m       }[33m,[39m
  [31m[1m>[22m[39m[90m 49 |[39m       inject[33m:[39m [getDataSourceToken([32m'postgres'[39m)][33m,[39m
   [90m    |[39m                [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 50 |[39m     }[33m,[39m
   [90m 51 |[39m   ][33m,[39m
   [90m 52 |[39m   exports[33m:[39m [[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m55:5[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 53 |[39m     [33mMySQLRepository[39m[33m,[39m
   [90m 54 |[39m     [33mPostgresRepository[39m[33m,[39m
  [31m[1m>[22m[39m[90m 55 |[39m     [33mMySQLService[39m[33m,[39m
   [90m    |[39m     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 56 |[39m     [33mPostgresService[39m[33m,[39m
   [90m 57 |[39m     [33mPipelineRepository[39m[33m,[39m
   [90m 58 |[39m     [33mProcessorRepository[39m[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m7:3[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m  5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m  6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m  7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m    |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m  8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m  9 |[39m
   [90m 10 |[39m [90m/**[39m[0m

webpack compiled with [1m[31m10 errors[39m[22m (b7ca7b0e42e11cad)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 662 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:10[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m          [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:27[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:46[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m34:16[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 32 |[39m     [33mCollectorRepository[39m[33m,[39m
   [90m 33 |[39m     {
  [31m[1m>[22m[39m[90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m    |[39m                [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
   [90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m 37 |[39m       }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m36:20[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
  [31m[1m>[22m[39m[90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m    |[39m                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 37 |[39m       }[33m,[39m
   [90m 38 |[39m       inject[33m:[39m [[33mDatabaseConfigProvider[39m][33m,[39m
   [90m 39 |[39m     }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m56:5[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 54 |[39m     [33mMySQLRepository[39m[33m,[39m
   [90m 55 |[39m     [33mPostgresRepository[39m[33m,[39m
  [31m[1m>[22m[39m[90m 56 |[39m     [33mMySQLService[39m[33m,[39m
   [90m    |[39m     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 57 |[39m     [33mPostgresService[39m[33m,[39m
   [90m 58 |[39m     [33mPipelineRepository[39m[33m,[39m
   [90m 59 |[39m     [33mProcessorRepository[39m[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m7:3[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m  5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m  6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m  7 |[39m   [33mUsdIndexValueEntity[39m[33m,[39m
   [90m    |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m  8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m  9 |[39m
   [90m 10 |[39m [90m/**[39m[0m

webpack compiled with [1m[31m9 errors[39m[22m (df91401d5a569dce)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 662 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:10[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m          [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:27[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:46[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m34:16[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 32 |[39m     [33mCollectorRepository[39m[33m,[39m
   [90m 33 |[39m     {
  [31m[1m>[22m[39m[90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m    |[39m                [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
   [90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m 37 |[39m       }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m36:20[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
  [31m[1m>[22m[39m[90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m    |[39m                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 37 |[39m       }[33m,[39m
   [90m 38 |[39m       inject[33m:[39m [[33mDatabaseConfigProvider[39m][33m,[39m
   [90m 39 |[39m     }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m56:5[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 54 |[39m     [33mMySQLRepository[39m[33m,[39m
   [90m 55 |[39m     [33mPostgresRepository[39m[33m,[39m
  [31m[1m>[22m[39m[90m 56 |[39m     [33mMySQLService[39m[33m,[39m
   [90m    |[39m     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 57 |[39m     [33mPostgresService[39m[33m,[39m
   [90m 58 |[39m     [33mPipelineRepository[39m[33m,[39m
   [90m 59 |[39m     [33mProcessorRepository[39m[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m23:23[22m[39m[1m[39m[22m
[90mTS2552: [39mCannot find name 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 21 |[39m     [33m@[39m[33mInjectRepository[39m([33mCryptoPriceEntity[39m)
   [90m 22 |[39m     [36mprivate[39m readonly cryptoPriceRepository[33m:[39m [33mRepository[39m[33m<[39m[33mCryptoPriceEntity[39m[33m>[39m[33m,[39m
  [31m[1m>[22m[39m[90m 23 |[39m     [33m@[39m[33mInjectRepository[39m([33mUsdIndexValueEntity[39m)
   [90m    |[39m                       [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 24 |[39m     [36mprivate[39m readonly usdIndexRepository[33m:[39m [33mRepository[39m[33m<[39m[33mUsdIndexValueEntity[39m[33m>[39m[33m,[39m
   [90m 25 |[39m     [36mprivate[39m readonly dataSource[33m:[39m [33mDataSource[39m
   [90m 26 |[39m   ) { }[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m24:53[22m[39m[1m[39m[22m
[90mTS2552: [39mCannot find name 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 22 |[39m     [36mprivate[39m readonly cryptoPriceRepository[33m:[39m [33mRepository[39m[33m<[39m[33mCryptoPriceEntity[39m[33m>[39m[33m,[39m
   [90m 23 |[39m     [33m@[39m[33mInjectRepository[39m([33mUsdIndexValueEntity[39m)
  [31m[1m>[22m[39m[90m 24 |[39m     [36mprivate[39m readonly usdIndexRepository[33m:[39m [33mRepository[39m[33m<[39m[33mUsdIndexValueEntity[39m[33m>[39m[33m,[39m
   [90m    |[39m                                                     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 25 |[39m     [36mprivate[39m readonly dataSource[33m:[39m [33mDataSource[39m
   [90m 26 |[39m   ) { }
   [90m 27 |[39m[0m

webpack compiled with [1m[31m10 errors[39m[22m (cedce15940da523e)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
chunk (runtime: main) [1m[32mmain.js[39m[22m (main) 662 KiB [1m[33m[entry][39m[22m [1m[32m[rendered][39m[22m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:10[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m          [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:27[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                           [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m13:46[22m[39m[1m[39m[22m
[90mTS2724: [39m'"@data-pipeline/storage"' has no exported member named 'UsdIndexValueEntity'. Did you mean 'UsdIndexEntity'?
  [0m [90m 11 |[39m [36mimport[39m { [33mMySQLRepository[39m } [36mfrom[39m [32m"./repositories/mysql.repository"[39m[33m;[39m
   [90m 12 |[39m [36mimport[39m { [33mPostgresRepository[39m } [36mfrom[39m [32m"./repositories/postgres.repository"[39m[33m;[39m
  [31m[1m>[22m[39m[90m 13 |[39m [36mimport[39m { [33mCryptoKgxEntity[39m[33m,[39m [33mCryptoPriceEntity[39m[33m,[39m [33mUsdIndexValueEntity[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m    |[39m                                              [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 14 |[39m [36mimport[39m { [33mPostgresService[39m } [36mfrom[39m [32m"./services/postgres.service"[39m[33m;[39m
   [90m 15 |[39m [36mimport[39m { [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 16 |[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m34:16[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 32 |[39m     [33mCollectorRepository[39m[33m,[39m
   [90m 33 |[39m     {
  [31m[1m>[22m[39m[90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m    |[39m                [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
   [90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m 37 |[39m       }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m36:20[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 34 |[39m       provide[33m:[39m [33mMySQLService[39m[33m,[39m
   [90m 35 |[39m       useFactory[33m:[39m (configProvider[33m:[39m [33mDatabaseConfigProvider[39m) [33m=>[39m {
  [31m[1m>[22m[39m[90m 36 |[39m         [36mreturn[39m [36mnew[39m [33mMySQLService[39m(configProvider[33m.[39mgetMySQLConfig())[33m;[39m
   [90m    |[39m                    [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 37 |[39m       }[33m,[39m
   [90m 38 |[39m       inject[33m:[39m [[33mDatabaseConfigProvider[39m][33m,[39m
   [90m 39 |[39m     }[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/database.module.ts:[32m[1m56:5[22m[39m[1m[39m[22m
[90mTS2304: [39mCannot find name 'MySQLService'.
  [0m [90m 54 |[39m     [33mMySQLRepository[39m[33m,[39m
   [90m 55 |[39m     [33mPostgresRepository[39m[33m,[39m
  [31m[1m>[22m[39m[90m 56 |[39m     [33mMySQLService[39m[33m,[39m
   [90m    |[39m     [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 57 |[39m     [33mPostgresService[39m[33m,[39m
   [90m 58 |[39m     [33mPipelineRepository[39m[33m,[39m
   [90m 59 |[39m     [33mProcessorRepository[39m[33m,[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m5:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoKgxEntity'.
  [0m [90m 3 |[39m [36mimport[39m { [33mRepository[39m[33m,[39m [33mDataSource[39m } [36mfrom[39m [32m"typeorm"[39m[33m;[39m
   [90m 4 |[39m [36mimport[39m {
  [31m[1m>[22m[39m[90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m 7 |[39m   [33mUsdIndexEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m[0m

[1m[31mERROR[39m[22m in [1m./apps/api/src/infrastructure/database/services/postgres.service.ts:[32m[1m6:3[22m[39m[1m[39m[22m
[90mTS2305: [39mModule '"@data-pipeline/storage"' has no exported member 'CryptoPriceEntity'.
  [0m [90m 4 |[39m [36mimport[39m {
   [90m 5 |[39m   [33mCryptoKgxEntity[39m[33m,[39m
  [31m[1m>[22m[39m[90m 6 |[39m   [33mCryptoPriceEntity[39m[33m,[39m
   [90m   |[39m   [31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m[31m[1m^[22m[39m
   [90m 7 |[39m   [33mUsdIndexEntity[39m[33m,[39m
   [90m 8 |[39m } [36mfrom[39m [32m"@data-pipeline/storage"[39m[33m;[39m
   [90m 9 |[39m[0m

webpack compiled with [1m[31m8 errors[39m[22m (d589121a479c5c53)
[1m[31mThere was an error with the build. See above.[39m[22m
C:\workspace\devin\src\dist\apps\api\main.js was not restarted.
[1m[31mCommand failed: taskkill /pid 21992 /T /F[39m[22m
[1m[31mERROR: The process "21992" not found.[39m[22m
[1m[31m[39m[22m
