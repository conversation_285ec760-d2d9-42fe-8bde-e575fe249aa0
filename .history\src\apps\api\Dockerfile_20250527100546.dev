FROM node:20-alpine

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json ./

# Install all dependencies (including dev dependencies)
RUN yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 && \
    yarn global add nx nodemon ts-node

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/api ./apps/api/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3000

# Expose port
EXPOSE 3000

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/api", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node -r tsconfig-paths/register apps/api/src/main.ts"] 