import { <PERSON>, <PERSON>, Req, <PERSON><PERSON>, Logger, Query } from "@nestjs/common";
import { Public } from "@data-pipeline/security";
import { Request, Response } from "express";
import { v4 as uuidv4 } from "uuid";
import { SSEService } from "@data-pipeline/messaging";
import { PostgresService } from "../../../../infrastructure/database/services/postgres.service";

@Controller("sse")
export class SSEController {
  private readonly logger = new Logger(SSEController.name);

  constructor(
    private readonly sseService: SSEService,
    private readonly postgresService: PostgresService
  ) {}

  @Public()
  @Get("events")
  async events(@Req() req: Request, @Res() res: Response): Promise<void> {
    // Set headers for SSE
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("X-Accel-Buffering", "no"); // For NGINX

    // Enable CORS for SSE from frontend
    // When using credentials, we must specify an exact origin, not a wildcard
    const allowedOrigins = [
      "http://localhost:3010",
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:8080",
      "https://kgx-crypto-calculator.ngrok.io",
      "https://0859db9326ef.ngrok.app",
    ];
    const origin = req.headers.origin;

    if (origin && allowedOrigins.includes(origin)) {
      res.setHeader("Access-Control-Allow-Origin", origin);
    } else {
      // Default to the dashboard origin if no origin header is present
      res.setHeader("Access-Control-Allow-Origin", "http://localhost:8080");
    }

    res.setHeader("Access-Control-Allow-Methods", "GET");
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, X-API-Key"
    );
    res.setHeader("Access-Control-Allow-Credentials", "true");

    // Generate a unique client ID
    const clientId = uuidv4();

    // Create a subscription for this client
    const subscription = this.sseService.addClient(clientId);

    // Send initial connection message
    res.write(
      `data: ${JSON.stringify({
        type: "connection",
        message: "Connected to SSE",
      })}\n\n`
    );

    // Handle client disconnect
    req.on("close", () => {
      this.sseService.removeClient(clientId);
      this.logger.log(`Client ${clientId} connection closed`);
    });

    // Subscribe to messages and send them to the client
    subscription.subscribe({
      next: (message) => {
        let data = "";

        // Add ID if provided
        if (message.id) {
          data += `id: ${message.id}\n`;
        }

        // Add event type if provided
        if (message.type) {
          data += `event: ${message.type}\n`;
        }

        // Add retry if provided
        if (message.retry) {
          data += `retry: ${message.retry}\n`;
        }

        // Add data (required)
        data += `data: ${JSON.stringify(message.data)}\n\n`;

        res.write(data);

        // Flush the data immediately if supported
        const response = res as any;
        if (response.flush && typeof response.flush === "function") {
          response.flush();
        }
      },
      error: (error) => {
        this.logger.error(
          `Error in SSE stream for client ${clientId}: ${error.message}`,
          error.stack
        );
        res.end();
      },
      complete: () => {
        res.end();
      },
    });
  }

  @Get("crypto")
  async cryptoEvents(
    @Req() req: Request,
    @Res() res: Response,
    @Query("symbols") symbolsParam?: string
  ): Promise<void> {
    // Set headers for SSE
    res.setHeader("Content-Type", "text/event-stream");
    res.setHeader("Cache-Control", "no-cache");
    res.setHeader("Connection", "keep-alive");
    res.setHeader("X-Accel-Buffering", "no"); // For NGINX

    // Enable CORS for SSE from frontend
    // When using credentials, we must specify an exact origin, not a wildcard
    const allowedOrigins = [
      "http://localhost:3010",
      "http://localhost:3000",
      "http://localhost:3001",
      "http://localhost:8080",
      "https://kgx-crypto-calculator.ngrok.io",
      "https://0859db9326ef.ngrok.app",
    ];
    const origin = req.headers.origin;

    if (origin && allowedOrigins.includes(origin)) {
      res.setHeader("Access-Control-Allow-Origin", origin);
    } else {
      // Default to the dashboard origin if no origin header is present
      res.setHeader("Access-Control-Allow-Origin", "http://localhost:8080");
    }

    res.setHeader("Access-Control-Allow-Methods", "GET");
    res.setHeader(
      "Access-Control-Allow-Headers",
      "Content-Type, Authorization, X-API-Key"
    );
    res.setHeader("Access-Control-Allow-Credentials", "true");

    // Parse symbols from query parameter
    const symbols = symbolsParam ? symbolsParam.split(",") : undefined;

    // Generate a unique client ID
    const clientId = uuidv4();
    this.logger.log(
      `Crypto SSE client connected: ${clientId} for symbols: ${
        symbols ? symbols.join(", ") : "all"
      }`
    );

    // Send initial connection message
    res.write(
      `data: ${JSON.stringify({
        type: "connection",
        message: "Connected to Crypto SSE",
      })}\n\n`
    );

    // Register with SSE service to receive cross-instance messages
    const subscription = this.sseService.addClient(clientId);

    // Subscribe to messages from the SSE service
    const messageSubscription = subscription.subscribe((message) => {
      try {
        // If it's a crypto-related message, forward it to the client

        // --- BEGIN: Diagnostic Logging ---
        console.log(
          "[SSE] Raw message received:",
          message,
          JSON.stringify(message)
        );
        // --- END: Diagnostic Logging ---

        if (
          message.type === "crypto-kgx" ||
          message.type === "kitco-cms-updates" ||
          (message.data && message.data.dataType === "crypto")
        ) {
          // Format the event
          if (message.type) {
            res.write(`event: ${message.type}\n`);
          }

          // Send the data
          res.write(`data: ${JSON.stringify(message.data || message)}\n\n`);
        }
      } catch (error) {
        this.logger.error(
          `Error sending SSE message: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
      }
    });

    // Function to fetch and send crypto data
    const fetchAndSendCryptoData = async () => {
      try {
        this.logger.log(`Fetching crypto data for client ${clientId}...`);

        // Get crypto data from database
        const cryptoData = await this.postgresService.getCryptoPrices(symbols);

        this.logger.log(
          `Fetched ${
            cryptoData?.length || 0
          } crypto records from database for client ${clientId}`
        );

        if (cryptoData && cryptoData.length > 0) {
          this.logger.log(
            `Sending ${cryptoData.length} crypto records to client ${clientId}`
          );

          // Pass through the data as-is without transformation
          // This ensures we're using exactly what's in the database
          const enhancedData = cryptoData;

          this.logger.log(
            `Prepared enhanced data for client ${clientId}, sending via SSE...`
          );

          // Send both as named event and general message to support different client configurations

          // 1. Send as a specific event type for clients that use addEventListener
          res.write(`event: crypto-kgx\n`);
          res.write(`data: ${JSON.stringify({ data: enhancedData })}\n\n`);
          this.logger.log(`Sent crypto-kgx event to client ${clientId}`);

          // 2. Also send as a general message for clients using onmessage
          res.write(`data: ${JSON.stringify(enhancedData)}\n\n`);
          this.logger.log(`Sent general message to client ${clientId}`);

          // 3. Send to the SSE service for cross-instance communication
          await this.sseService.sendToTopic("crypto-kgx", {
            data: enhancedData,
          });
          this.logger.log(
            `Published to crypto-kgx topic for cross-instance communication`
          );
        } else {
          this.logger.warn(
            `No crypto data available to send to client ${clientId}`
          );

          // Check if the database is accessible
          try {
            const dbStatus = await this.postgresService.checkConnection();
            this.logger.log(
              `Database connection status: ${JSON.stringify(dbStatus)}`
            );
          } catch (dbError) {
            this.logger.error(
              `Database connection check failed: ${
                dbError instanceof Error ? dbError.message : String(dbError)
              }`
            );
          }
        }
      } catch (error) {
        this.logger.error(
          `Error fetching crypto data: ${
            error instanceof Error ? error.message : String(error)
          }`
        );
        res.write(
          `event: error\ndata: ${JSON.stringify({
            message: "Error fetching crypto data",
          })}\n\n`
        );
      }
    };

    // Send data immediately
    await fetchAndSendCryptoData();

    // Set up interval to fetch and send data every 5 seconds
    const intervalId = setInterval(fetchAndSendCryptoData, 5000);

    // Handle client disconnect
    req.on("close", () => {
      // Clean up
      if (messageSubscription) {
        messageSubscription.unsubscribe();
      }

      clearInterval(intervalId);
      this.sseService.removeClient(clientId);
      this.logger.log(`Crypto SSE client ${clientId} disconnected`);
      res.end();
    });
  }

  @Get("stats")
  getStats(): { clients: number } {
    return {
      clients: this.sseService.getClientCount(),
    };
  }

  @Public()
  @Get("test-crypto-broadcast")
  async testCryptoBroadcast(): Promise<{
    success: boolean;
    message: string;
    data?: any;
  }> {
    try {
      this.logger.log(
        "Manual test: Broadcasting crypto data to all SSE clients"
      );

      // Get crypto data from database
      const cryptoData = await this.postgresService.getCryptoPrices();

      if (!cryptoData || cryptoData.length === 0) {
        this.logger.warn("No crypto data available for test broadcast");
        return {
          success: false,
          message: "No crypto data available in the database",
        };
      }

      this.logger.log(
        `Broadcasting ${cryptoData.length} crypto records to all clients`
      );

      // Pass through the data as-is without transformation
      // This ensures we're using exactly what's in the database
      const enhancedData = cryptoData;

      // Send to SSE service
      await this.sseService.sendToTopic("crypto-kgx", {
        type: "crypto-kgx",
        data: enhancedData,
        metadata: {
          timestamp: new Date().toISOString(),
          source: "test-endpoint",
        },
      });

      return {
        success: true,
        message: `Successfully broadcast ${cryptoData.length} crypto records`,
        data: enhancedData.slice(0, 2), // Return first 2 items as sample
      };
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : String(error);
      this.logger.error(`Error in test crypto broadcast: ${errorMessage}`);
      return {
        success: false,
        message: `Error broadcasting crypto data: ${errorMessage}`,
      };
    }
  }
}
