# Extend from shared base image
FROM data-pipeline-base:dev

# Copy service-specific source code
COPY apps/api ./apps/api/

# Set service-specific environment variables
ENV PORT=3000

# Expose port
EXPOSE 3000

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/api", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/api/src/main.ts"] 