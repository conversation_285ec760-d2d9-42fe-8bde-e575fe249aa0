import { Injectable, Logger } from "@nestjs/common";
import { Job, MySQLServiceBase } from "@data-pipeline/storage";
import {
  JobType,
  ValidationException,
  AuthType,
  SchedulerType,
  getErrorMessage,
  getErrorStack,
} from "@data-pipeline/core";
import { SchedulerService } from "../../domain/services/scheduler.service";
import { JobService } from "../../domain/services/job.service";

export interface CreateJobCommand {
  name: string;
  type: JobType;
  schedule: string;
  data: any;
  enabled?: boolean;
  next_run?: Date;
}

export interface UpdateJobCommand {
  id: string;
  name?: string;
  type?: JobType;
  schedule?: string;
  data?: any;
  enabled?: boolean;
  next_run?: Date;
}

@Injectable()
export class JobCommandHandler {
  private readonly logger = new Logger(JobCommandHandler.name);
  private readonly schedulersTable = "schedulers";
  private defaultSchedulerId: string | null = null;

  constructor(
    private readonly jobService: JobService,
    private readonly scheduleService: SchedulerService,
    private readonly mySqlService: MySQLServiceBase
  ) {}

  async createJob(command: CreateJobCommand): Promise<Job> {
    // Validate cron expression
    if (!this.scheduleService.isValidCronExpression(command.schedule)) {
      throw new ValidationException(
        "Invalid cron expression",
        "INVALID_CRON_EXPRESSION",
        { schedule: command.schedule }
      );
    }

    // Ensure we have a default scheduler ID
    if (!this.defaultSchedulerId) {
      await this.ensureDefaultScheduler();
    }

    // Make sure the data object exists and has scheduler_id
    const data = command.data || {};
    if (!data.scheduler_id) {
      data.scheduler_id = this.defaultSchedulerId;
    }

    return this.jobService.create({
      name: command.name,
      type: command.type,
      schedule: command.schedule,
      data: data,
      enabled: command.enabled ?? true,
    });
  }

  async updateJob(command: UpdateJobCommand): Promise<Job> {
    // Validate cron expression if provided
    if (
      command.schedule &&
      !this.scheduleService.isValidCronExpression(command.schedule)
    ) {
      throw new ValidationException(
        "Invalid cron expression",
        "INVALID_CRON_EXPRESSION",
        { schedule: command.schedule }
      );
    }

    const job = await this.jobService.findById(command.id);

    if (!job) {
      throw new ValidationException(
        `Job with ID ${command.id} not found`,
        "JOB_NOT_FOUND"
      );
    }

    const updatedJob = await this.jobService.update(command.id, {
      name: command.name,
      type: command.type,
      schedule: command.schedule,
      data: command.data,
      enabled: command.enabled,
      next_run: command.next_run,
    });

    if (!updatedJob) {
      throw new ValidationException(
        `Failed to update job with ID ${command.id}`,
        "JOB_UPDATE_FAILED"
      );
    }

    return updatedJob;
  }

  async deleteJob(id: string): Promise<boolean> {
    const job = await this.jobService.findById(id);

    if (!job) {
      throw new ValidationException(
        `Job with ID ${id} not found`,
        "JOB_NOT_FOUND"
      );
    }

    return this.jobService.delete(id);
  }

  async enableJob(id: string): Promise<Job> {
    const job = await this.jobService.findById(id);

    if (!job) {
      throw new ValidationException(
        `Job with ID ${id} not found`,
        "JOB_NOT_FOUND"
      );
    }

    const enabledJob = await this.jobService.update(id, { enabled: true });

    if (!enabledJob) {
      throw new ValidationException(
        `Failed to enable job with ID ${id}`,
        "JOB_ENABLE_FAILED"
      );
    }

    return enabledJob;
  }

  async disableJob(id: string): Promise<Job> {
    const job = await this.jobService.findById(id);

    if (!job) {
      throw new ValidationException(
        `Job with ID ${id} not found`,
        "JOB_NOT_FOUND"
      );
    }

    const disabledJob = await this.jobService.update(id, { enabled: false });

    if (!disabledJob) {
      throw new ValidationException(
        `Failed to disable job with ID ${id}`,
        "JOB_DISABLE_FAILED"
      );
    }

    return disabledJob;
  }

  /**
   * Ensure a default scheduler exists for job associations
   */
  private async ensureDefaultScheduler(): Promise<void> {
    const defaultId = process.env.DEFAULT_SCHEDULER_ID || "scheduler_default";
    try {
      // Check if the default scheduler exists
      const query = `SELECT id FROM ${this.schedulersTable} WHERE id = ?`;
      const existing = await this.mySqlService.query(query, [defaultId]);

      if (!existing || existing.length === 0) {
        // Insert default scheduler
        const connectionJson = JSON.stringify({
          url: "",
          authType: AuthType.NONE,
        });
        const insertSql = `
          INSERT INTO ${this.schedulersTable}
            (id, name, description, type, connection, enabled, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        await this.mySqlService.query(insertSql, [
          defaultId,
          "Default Scheduler",
          "Auto-generated default scheduler",
          SchedulerType.API,
          connectionJson,
          1,
        ]);
        this.logger.log(`Created default scheduler with id ${defaultId}`);
      }
      this.defaultSchedulerId = defaultId;
      this.logger.log(`Using default scheduler with id ${defaultId}`);
    } catch (err) {
      this.logger.error(
        `Failed to ensure default scheduler: ${getErrorMessage(err as Error)}`,
        getErrorStack(err as Error)
      );
      throw err;
    }
  }
}
