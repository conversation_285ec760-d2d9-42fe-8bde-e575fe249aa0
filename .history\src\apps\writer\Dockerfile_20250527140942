FROM node:22-alpine AS build
RUN apk add --no-cache python3 make g++

WORKDIR /app

# Copy only package files first to leverage Docker cache for dependencies
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json ./
COPY apps/writer/project.json ./apps/writer/

# Install dependencies with better error handling and timeout
RUN yarn config set network-timeout 300000 && \
    yarn install --frozen-lockfile --network-timeout 300000 || \
    (for i in 1 2 3; do sleep 15 && yarn install --frozen-lockfile --network-timeout 300000; done) && \
    yarn global add nx

# Copy source code after installing dependencies
COPY libs ./libs/
COPY config ./config/
COPY apps/writer ./apps/writer/

# Build all libraries and service in a single layer to reduce image size
# First, build the utils library which is a dependency for other libraries
RUN yarn nx build utils

# Then build core which depends on utils
RUN yarn nx build core

# Create symlinks for the built packages to make them available to other builds
RUN mkdir -p ./node_modules/@data-pipeline && \
    ln -s /app/dist/libs/utils /app/node_modules/@data-pipeline/utils && \
    ln -s /app/dist/libs/core /app/node_modules/@data-pipeline/core

# Build kgx, logging, and security libraries first
RUN yarn nx build kgx && \
    yarn nx build logging && \
    yarn nx build security

# Create symlinks for kgx, logging, and security libraries
RUN ln -s /app/dist/libs/kgx /app/node_modules/@data-pipeline/kgx && \
    ln -s /app/dist/libs/logging /app/node_modules/@data-pipeline/logging && \
    ln -s /app/dist/libs/security /app/node_modules/@data-pipeline/security

# Now build the rest of the libraries and the writer service
RUN yarn nx build messaging && \
    yarn nx build storage && \
    yarn nx build writer

# Production image
FROM node:20-alpine

WORKDIR /app

# Set environment variables
ENV NODE_ENV=production \
    PORT=3004

# Copy only production dependencies
COPY package.json ./
RUN yarn config set network-timeout 300000 && \
    yarn install --production --frozen-lockfile --network-timeout 300000 || \
    (for i in 1 2 3; do sleep 15 && yarn install --production --frozen-lockfile --network-timeout 300000; done) && \
    yarn add axios -W

# Copy built artifacts from build stage
COPY --from=build /app/dist/apps/writer ./dist/apps/writer
COPY --from=build /app/dist/libs ./dist/libs
COPY --from=build /app/config ./config
COPY .env.local ./.env

# Create symlinks for local dependencies
RUN mkdir -p ./node_modules/@data-pipeline && \
    for dir in $(ls -d ./dist/libs/*); do \
    pkg_name=$(basename $dir); \
    ln -s /app/dist/libs/$pkg_name /app/node_modules/@data-pipeline/$pkg_name; \
    done

EXPOSE 3004

HEALTHCHECK --interval=30s --timeout=10s --retries=3 CMD wget -q -O- http://localhost:3004/api/health || exit 1

CMD ["node", "dist/apps/writer/main.js"]
