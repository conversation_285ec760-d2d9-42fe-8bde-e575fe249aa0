---
description: This rule provides best practices for coding standards in Seaborn, emphasizing clear, reproducible code, optimal performance, and secure data handling within AI and machine learning data science development.
globs: **/*.py
---
By following these best practices and coding standards, you can write clear, reproducible, and maintainable Seaborn code that is optimized for performance and security.