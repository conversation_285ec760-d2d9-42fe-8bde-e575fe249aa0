# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root temporarily to copy files
USER root

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/scheduler ./apps/scheduler/

# Switch back to non-root user
USER nextjs

# Set service-specific environment variables
ENV PORT=3001

# Expose port
EXPOSE 3001

# Development command with hot reload
CMD ["nodemon", "--watch", "apps/scheduler", "--watch", "libs", "--ext", "ts,js,json", "--exec", "ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/scheduler/src/main.ts"] 