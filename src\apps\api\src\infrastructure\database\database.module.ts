import { Modu<PERSON> } from "@nestjs/common";
import { getDataSourceToken } from "@nestjs/typeorm";
import {
  TypeOrmModule,
  DatabaseConfigModule,
  PipelineRepository,
  ProcessorRepository,
  CollectorRepository,
  DatabaseConfigProvider
} from "@data-pipeline/storage";
import { MySQLRepository } from "./repositories/mysql.repository";
import { PostgresRepository } from "./repositories/postgres.repository";
import { CryptoKgxEntity, CryptoPriceEntity, UsdIndexValueEntity } from "@data-pipeline/storage";
import { PostgresService } from "./services/postgres.service";
import { DataSource } from "typeorm";

/**
 * Database module for the API microservice
 * Uses the global database module for database connections
 */
@Module({
  imports: [
    DatabaseConfigModule,
    TypeOrmModule.forFeature([CryptoKgxEntity, CryptoPriceEntity, UsdIndexValueEntity], "postgres"),
    TypeOrmModule.forFeature([], 'mysql'),
  ],
  providers: [
    MySQLRepository,
    PostgresRepository,
    PipelineRepository,
    ProcessorRepository,
    CollectorRepository,
    {
      provide: MySQLService,
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return new MySQLService(configProvider.getMySQLConfig());
      },
      inject: [DatabaseConfigProvider],
    },
    {
      provide: PostgresService,
      useFactory: (postgresDataSource: DataSource) => {
        return new PostgresService(
          postgresDataSource.getRepository(CryptoKgxEntity),
          postgresDataSource.getRepository(CryptoPriceEntity),
          postgresDataSource.getRepository(UsdIndexValueEntity),
          postgresDataSource
        );
      },
      inject: [getDataSourceToken('postgres')],
    },
  ],
  exports: [
    MySQLRepository,
    PostgresRepository,
    MySQLService,
    PostgresService,
    PipelineRepository,
    ProcessorRepository,
    CollectorRepository,
    TypeOrmModule,
  ],
})
export class DatabaseModule { }
