FROM node:22-alpine

WORKDIR /app

# Install development dependencies (keep them for entrypoint rebuild)
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Install dependencies in a single layer to reduce image size
RUN yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 && \
    yarn global add nx nodemon ts-node && \
    # Clean yarn cache to reduce image size
    yarn cache clean && \
    # Clean up temporary files
    rm -rf /tmp/* /var/cache/apk/*

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/collector ./apps/collector/

# Copy entrypoint script
COPY apps/collector/entrypoint.dev.sh /entrypoint.dev.sh
RUN chmod +x /entrypoint.dev.sh

# Set environment variables
ENV NODE_ENV=development \
    PORT=3002

# Expose port
EXPOSE 3002

# Use entrypoint to rebuild native dependencies after volume mounts
ENTRYPOINT ["/entrypoint.dev.sh"]

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/collector", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/collector/src/main.ts"] 