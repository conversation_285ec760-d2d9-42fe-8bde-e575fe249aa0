# Extend from shared base image
FROM data-pipeline-base:dev

# Copy service-specific source code
COPY apps/scheduler ./apps/scheduler/

# Set service-specific environment variables
ENV PORT=3001

# Expose port
EXPOSE 3001

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/scheduler", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/scheduler/src/main.ts"] 