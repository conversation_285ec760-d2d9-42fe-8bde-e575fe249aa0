#!/bin/bash

# KGX Crypto Data Application - Monitoring Script
# Author: System Administrator
# Version: 1.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/opt/kgx-app"
ALERT_EMAIL="${ALERT_EMAIL:-<EMAIL>}"
ALERT_WEBHOOK="${ALERT_WEBHOOK:-}"
CHECK_INTERVAL="${CHECK_INTERVAL:-60}"
LOG_FILE="/var/log/kgx-monitor.log"

# Thresholds
CPU_THRESHOLD=${CPU_THRESHOLD:-80}
MEMORY_THRESHOLD=${MEMORY_THRESHOLD:-80}
DISK_THRESHOLD=${DISK_THRESHOLD:-85}
RESPONSE_TIME_THRESHOLD=${RESPONSE_TIME_THRESHOLD:-5000}

# Logging functions
log() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] $1"
    echo -e "${GREEN}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

warn() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1"
    echo -e "${YELLOW}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

error() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1"
    echo -e "${RED}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

info() {
    local message="[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1"
    echo -e "${BLUE}$message${NC}"
    echo "$message" >> "$LOG_FILE"
}

# Send alert notification
send_alert() {
    local severity="$1"
    local subject="$2"
    local message="$3"
    
    local full_message="KGX Application Alert - $severity
    
Subject: $subject
Time: $(date)
Server: $(hostname)

$message

---
KGX Monitoring System"
    
    # Send email alert if configured
    if [[ -n "$ALERT_EMAIL" ]] && command -v mail &> /dev/null; then
        echo "$full_message" | mail -s "KGX Alert: $subject" "$ALERT_EMAIL"
        log "Alert email sent to $ALERT_EMAIL"
    fi
    
    # Send webhook alert if configured
    if [[ -n "$ALERT_WEBHOOK" ]] && command -v curl &> /dev/null; then
        curl -X POST "$ALERT_WEBHOOK" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"$full_message\"}" \
            &> /dev/null || true
        log "Alert webhook sent"
    fi
    
    # Log the alert
    error "ALERT [$severity]: $subject - $message"
}

# Check if Docker is running
check_docker() {
    if ! docker info &> /dev/null; then
        send_alert "CRITICAL" "Docker Service Down" "Docker daemon is not running or not accessible"
        return 1
    fi
    return 0
}

# Check Docker Compose services
check_services() {
    local failed_services=()
    local services=("postgres" "mysql" "redis" "api" "scheduler" "collector" "processor" "writer" "dashboard")
    
    cd "$APP_DIR/src"
    
    for service in "${services[@]}"; do
        local status=$(docker compose -f docker-compose.yml -f docker-compose.prod.yml ps -q "$service" 2>/dev/null)
        
        if [[ -z "$status" ]]; then
            failed_services+=("$service (not found)")
            continue
        fi
        
        local running=$(docker inspect --format='{{.State.Running}}' "$status" 2>/dev/null)
        local health=$(docker inspect --format='{{.State.Health.Status}}' "$status" 2>/dev/null || echo "no-health-check")
        
        if [[ "$running" != "true" ]]; then
            failed_services+=("$service (not running)")
        elif [[ "$health" == "unhealthy" ]]; then
            failed_services+=("$service (unhealthy)")
        fi
    done
    
    if [[ ${#failed_services[@]} -gt 0 ]]; then
        local message="Failed services: ${failed_services[*]}"
        send_alert "CRITICAL" "Service Failure" "$message"
        return 1
    fi
    
    return 0
}

# Check API endpoints
check_api_endpoints() {
    local failed_endpoints=()
    local endpoints=(
        "http://localhost:3000/api/health"
        "http://localhost:3000/kgx-crypto-data/getValue?symbol=BTC"
        "http://localhost:3010"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local start_time=$(date +%s%3N)
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$endpoint" 2>/dev/null || echo "000")
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        
        if [[ "$response_code" != "200" ]]; then
            failed_endpoints+=("$endpoint (HTTP $response_code)")
        elif [[ $response_time -gt $RESPONSE_TIME_THRESHOLD ]]; then
            warn "Slow response from $endpoint: ${response_time}ms"
        fi
    done
    
    if [[ ${#failed_endpoints[@]} -gt 0 ]]; then
        local message="Failed endpoints: ${failed_endpoints[*]}"
        send_alert "CRITICAL" "API Endpoint Failure" "$message"
        return 1
    fi
    
    return 0
}

# Check database connectivity
check_databases() {
    local failed_dbs=()
    
    cd "$APP_DIR/src"
    
    # Check PostgreSQL
    if ! docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres &> /dev/null; then
        failed_dbs+=("PostgreSQL")
    fi
    
    # Check MySQL
    if ! docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysqladmin ping -h localhost -u root -p"${MYSQL_ROOT_PASSWORD:-password}" &> /dev/null; then
        failed_dbs+=("MySQL")
    fi
    
    # Check Redis
    if ! docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T redis redis-cli ping &> /dev/null; then
        failed_dbs+=("Redis")
    fi
    
    if [[ ${#failed_dbs[@]} -gt 0 ]]; then
        local message="Failed databases: ${failed_dbs[*]}"
        send_alert "CRITICAL" "Database Connectivity Failure" "$message"
        return 1
    fi
    
    return 0
}

# Check system resources
check_system_resources() {
    local alerts=()
    
    # Check CPU usage
    local cpu_usage=$(top -bn1 | grep "Cpu(s)" | awk '{print $2}' | sed 's/%us,//')
    cpu_usage=${cpu_usage%.*}  # Remove decimal part
    
    if [[ $cpu_usage -gt $CPU_THRESHOLD ]]; then
        alerts+=("High CPU usage: ${cpu_usage}%")
    fi
    
    # Check memory usage
    local memory_info=$(free | grep Mem)
    local total_mem=$(echo $memory_info | awk '{print $2}')
    local used_mem=$(echo $memory_info | awk '{print $3}')
    local memory_usage=$((used_mem * 100 / total_mem))
    
    if [[ $memory_usage -gt $MEMORY_THRESHOLD ]]; then
        alerts+=("High memory usage: ${memory_usage}%")
    fi
    
    # Check disk usage
    local disk_usage=$(df -h "$APP_DIR" | awk 'NR==2 {print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt $DISK_THRESHOLD ]]; then
        alerts+=("High disk usage: ${disk_usage}%")
    fi
    
    # Check load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    local cpu_cores=$(nproc)
    local load_threshold=$((cpu_cores * 2))
    
    if (( $(echo "$load_avg > $load_threshold" | bc -l) )); then
        alerts+=("High load average: $load_avg (cores: $cpu_cores)")
    fi
    
    if [[ ${#alerts[@]} -gt 0 ]]; then
        local message="${alerts[*]}"
        send_alert "WARNING" "High System Resource Usage" "$message"
        return 1
    fi
    
    return 0
}

# Check log files for errors
check_logs() {
    local error_patterns=("ERROR" "FATAL" "Exception" "failed" "timeout" "connection refused")
    local log_dirs=("$APP_DIR/data/logs" "/var/log/nginx")
    local recent_errors=()
    
    # Check logs from the last 5 minutes
    local since_time=$(date -d '5 minutes ago' '+%Y-%m-%d %H:%M:%S')
    
    for log_dir in "${log_dirs[@]}"; do
        if [[ ! -d "$log_dir" ]]; then
            continue
        fi
        
        for pattern in "${error_patterns[@]}"; do
            local errors=$(find "$log_dir" -name "*.log" -type f -exec grep -l "$pattern" {} \; 2>/dev/null | head -5)
            
            for log_file in $errors; do
                local recent=$(grep "$pattern" "$log_file" | tail -10)
                if [[ -n "$recent" ]]; then
                    recent_errors+=("$log_file: $pattern found")
                fi
            done
        done
    done
    
    if [[ ${#recent_errors[@]} -gt 0 ]]; then
        local message="Recent errors found in logs: ${recent_errors[*]}"
        warn "Log errors detected: $message"
        # Don't send critical alert for log errors, just warn
    fi
}

# Check external connectivity
check_external_connectivity() {
    local failed_connections=()
    local external_hosts=("hades.kitco.com" "kds2.kitco.com" "google.com")
    
    for host in "${external_hosts[@]}"; do
        if ! ping -c 1 -W 5 "$host" &> /dev/null; then
            failed_connections+=("$host")
        fi
    done
    
    if [[ ${#failed_connections[@]} -gt 0 ]]; then
        local message="Cannot reach external hosts: ${failed_connections[*]}"
        send_alert "WARNING" "External Connectivity Issues" "$message"
        return 1
    fi
    
    return 0
}

# Check SSL certificate expiration
check_ssl_certificate() {
    local domain="${DOMAIN:-localhost}"
    
    if [[ "$domain" == "localhost" ]]; then
        return 0
    fi
    
    local cert_info=$(echo | openssl s_client -servername "$domain" -connect "$domain:443" 2>/dev/null | openssl x509 -noout -dates 2>/dev/null)
    
    if [[ -n "$cert_info" ]]; then
        local expiry_date=$(echo "$cert_info" | grep "notAfter" | cut -d= -f2)
        local expiry_timestamp=$(date -d "$expiry_date" +%s)
        local current_timestamp=$(date +%s)
        local days_until_expiry=$(( (expiry_timestamp - current_timestamp) / 86400 ))
        
        if [[ $days_until_expiry -lt 30 ]]; then
            local message="SSL certificate expires in $days_until_expiry days"
            send_alert "WARNING" "SSL Certificate Expiring Soon" "$message"
        fi
    fi
}

# Generate status report
generate_status_report() {
    local report_file="/tmp/kgx-status-$(date +%Y%m%d_%H%M%S).txt"
    
    cat > "$report_file" <<EOF
KGX Crypto Data Application - Status Report
Generated: $(date)
Server: $(hostname)

=== System Information ===
Uptime: $(uptime)
Load Average: $(uptime | awk -F'load average:' '{print $2}')
CPU Cores: $(nproc)
Total Memory: $(free -h | grep Mem | awk '{print $2}')
Available Memory: $(free -h | grep Mem | awk '{print $7}')
Disk Usage: $(df -h $APP_DIR | awk 'NR==2 {print $3 "/" $2 " (" $5 " used)"}')

=== Docker Services ===
EOF
    
    cd "$APP_DIR/src"
    docker compose -f docker-compose.yml -f docker-compose.prod.yml ps >> "$report_file"
    
    cat >> "$report_file" <<EOF

=== API Endpoints Status ===
EOF
    
    local endpoints=(
        "http://localhost:3000/api/health"
        "http://localhost:3000/kgx-crypto-data/getValue?symbol=BTC"
        "http://localhost:3010"
    )
    
    for endpoint in "${endpoints[@]}"; do
        local start_time=$(date +%s%3N)
        local response_code=$(curl -s -o /dev/null -w "%{http_code}" --max-time 10 "$endpoint" 2>/dev/null || echo "000")
        local end_time=$(date +%s%3N)
        local response_time=$((end_time - start_time))
        
        echo "$endpoint: HTTP $response_code (${response_time}ms)" >> "$report_file"
    done
    
    cat >> "$report_file" <<EOF

=== Recent Log Entries ===
EOF
    
    # Add recent log entries
    if [[ -f "$LOG_FILE" ]]; then
        tail -20 "$LOG_FILE" >> "$report_file"
    fi
    
    echo "$report_file"
}

# Main monitoring function
run_monitoring() {
    log "Starting KGX application monitoring..."
    
    local checks_passed=0
    local checks_failed=0
    
    # Run all checks
    if check_docker; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    if check_services; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    if check_api_endpoints; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    if check_databases; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    if check_system_resources; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    check_logs  # This doesn't count towards pass/fail
    
    if check_external_connectivity; then
        ((checks_passed++))
    else
        ((checks_failed++))
    fi
    
    check_ssl_certificate  # This doesn't count towards pass/fail
    
    # Log summary
    local total_checks=$((checks_passed + checks_failed))
    info "Monitoring completed: $checks_passed/$total_checks checks passed"
    
    if [[ $checks_failed -gt 0 ]]; then
        warn "$checks_failed critical checks failed"
        return 1
    fi
    
    return 0
}

# Continuous monitoring mode
continuous_monitoring() {
    log "Starting continuous monitoring mode (interval: ${CHECK_INTERVAL}s)"
    
    while true; do
        run_monitoring
        sleep "$CHECK_INTERVAL"
    done
}

# Setup monitoring as a service
setup_monitoring_service() {
    log "Setting up monitoring service..."
    
    sudo tee /etc/systemd/system/kgx-monitor.service > /dev/null <<EOF
[Unit]
Description=KGX Application Monitoring Service
After=docker.service kgx-app.service
Requires=docker.service

[Service]
Type=simple
User=$USER
Group=$USER
WorkingDirectory=$APP_DIR
ExecStart=$APP_DIR/deploy/monitor.sh continuous
Restart=always
RestartSec=30
StandardOutput=journal
StandardError=journal

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable kgx-monitor.service
    
    log "Monitoring service created and enabled"
    info "Start with: sudo systemctl start kgx-monitor"
    info "View logs with: journalctl -u kgx-monitor -f"
}

# Main function
main() {
    # Ensure log file exists and is writable
    sudo touch "$LOG_FILE"
    sudo chown "$USER:$USER" "$LOG_FILE"
    
    case "${1:-check}" in
        "check")
            run_monitoring
            ;;
        "continuous")
            continuous_monitoring
            ;;
        "report")
            local report_file=$(generate_status_report)
            log "Status report generated: $report_file"
            cat "$report_file"
            ;;
        "setup")
            setup_monitoring_service
            ;;
        "test-alert")
            send_alert "TEST" "Test Alert" "This is a test alert to verify notification system"
            ;;
        *)
            echo "Usage: $0 {check|continuous|report|setup|test-alert}"
            echo
            echo "Commands:"
            echo "  check      - Run monitoring checks once"
            echo "  continuous - Run continuous monitoring"
            echo "  report     - Generate status report"
            echo "  setup      - Setup monitoring as systemd service"
            echo "  test-alert - Send test alert"
            echo
            echo "Environment variables:"
            echo "  ALERT_EMAIL              - Email address for alerts"
            echo "  ALERT_WEBHOOK            - Webhook URL for alerts"
            echo "  CHECK_INTERVAL           - Monitoring interval in seconds (default: 60)"
            echo "  CPU_THRESHOLD            - CPU usage alert threshold % (default: 80)"
            echo "  MEMORY_THRESHOLD         - Memory usage alert threshold % (default: 80)"
            echo "  DISK_THRESHOLD           - Disk usage alert threshold % (default: 85)"
            echo "  RESPONSE_TIME_THRESHOLD  - API response time threshold ms (default: 5000)"
            exit 1
            ;;
    esac
}

# Run main function
main "$@" 