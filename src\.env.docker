# Docker Development Environment Configuration (Services running in Docker)
NODE_ENV=development
LOG_LEVEL=debug

# API Configuration
API_KEY=61d6e1f73101fa0b13d6a5301e649fef78dea03b50b6b6c44a10a4600ceefe57
API_URL=http://localhost:3000/api

# Service URLs for HTTP clients / fallbacks (DOCKER MODE)
SCHEDULER_URL=http://scheduler:3001
COLLECTOR_URL=http://collector:3002
PROCESSOR_URL=http://processor:3003
WRITER_URL=http://writer:3004

# Service URLs for health checks (DOCKER MODE)
SCHEDULER_SERVICE_URL=http://scheduler:3001
COLLECTOR_SERVICE_URL=http://collector:3002
PROCESSOR_SERVICE_URL=http://processor:3003
WRITER_SERVICE_URL=http://writer:3004

# Service Ports
API_PORT=3000
SCHEDULER_PORT=3001
COLLECTOR_PORT=3002
PROCESSOR_PORT=3003
WRITER_PORT=3004

# MySQL (Docker)
MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=kitco_user
MYSQL_PASSWORD=password
MYSQL_DATABASE=kitco
MYSQL_SSL=false
MYSQL_ROOT_PASSWORD=password

# PostgreSQL / Timescale (Docker)
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=postgres
POSTGRES_DB=kitco

TIMESCALE_HOST=postgres
TIMESCALE_PORT=5432
TIMESCALE_USERNAME=postgres
TIMESCALE_PASSWORD=postgres
TIMESCALE_DATABASE=kitco
TIMESCALE_SSL=false

# Redis (Docker)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=

# Websocket
WS_HOSTNAME=localhost
WS_PORT=9233

# Integration configs
USD_INDEX_API_URL=https://kdb-api.prod.kitco.com/api/usd-index
CRYPTO_API_URL=https://kdb-api.prod.kitco.com/api/crypto-rates
OPEN_EXCHANGE_RATES_URL=https://openexchangerates.org/api
FOREX_FALLBACK_URL=https://open.er-api.com/v6/latest/USD

# KDS2_API_URL=https://kds2.kitco.com/api
KDS2_BASE_URL=https://kds2.kitco.com
KDS2_API_URL=https://kds2.kitco.com/getUSD
KDS2_API_KEY=9bnteWVi2kT13528d100c608fn0TlbC6

# Hades Integration (for legacy system)
HADES_HOST=************
HADES_PORT=1433
HADES_USER=FavishROUser
HADES_PASSWORD=WayrUAZcNUux!%*Ay58#w9Y8d3TJKi
HADES_DATABASE=KitcoData

# Database selection flags
USE_TIMESCALE_DB=true
SAVE_TO_MULTIPLE_DATABASES=false

# Kitco CMS Next Integration
KITCO_CMS_SSE_ENABLED=true
