import { forwardR<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { PinoLoggerModule } from "@data-pipeline/logging";
import { TypeOrmModule } from "@nestjs/typeorm";
import { DomainModule } from "./domain/domain.module";
import { ApplicationModule } from "./application/application.module";
import { PresentationModule } from "./presentation/presentation.module";
import { InfrastructureModule } from "./infrastructure/infrastructure.module";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { JwtModule } from "@nestjs/jwt";
import { SecurityModule } from "@data-pipeline/security";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.local"],
    }),
    PinoLoggerModule.register({
      serviceName: "writer-service",
      logLevel: process.env.LOG_LEVEL || "info",
      prettyPrint: process.env.NODE_ENV !== "production",
    }),
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: ".",
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || "secret",
      signOptions: { expiresIn: "1h" },
    }),
    TypeOrmModule.forRoot({
      name: "mysql",
      type: "mysql",
      host:
        process.env.NODE_ENV === "development"
          ? "localhost"
          : process.env.MYSQL_HOST || "mysql",
      port: parseInt(process.env.MYSQL_PORT || "3306", 10),
      username: process.env.MYSQL_USERNAME || "kitco_user",
      password: process.env.MYSQL_PASSWORD || "password",
      database: process.env.MYSQL_DATABASE || "kitco",
      entities: [],
      synchronize: false,
      autoLoadEntities: true,
      retryAttempts: 20,
      retryDelay: 3000,
      keepConnectionAlive: true,
      extra: {
        connectionLimit: 10,
        waitForConnections: true,
        connectTimeout: 60000, // 60 seconds connection timeout
      },
    }),
    TypeOrmModule.forRoot({
      name: "postgres",
      type: "postgres",
      host:
        process.env.NODE_ENV === "development"
          ? "localhost"
          : process.env.POSTGRES_HOST || "postgres",
      port: parseInt(process.env.POSTGRES_PORT || "5432", 10),
      username: process.env.POSTGRES_USERNAME || "postgres",
      password: process.env.POSTGRES_PASSWORD || "password",
      database: process.env.POSTGRES_DATABASE || "kitco",
      entities: [],
      synchronize: false,
      autoLoadEntities: true,
      retryAttempts: 20,
      retryDelay: 3000,
      keepConnectionAlive: true,
      ssl: process.env.POSTGRES_SSL === "true",
    }),
    TypeOrmModule.forFeature([], "mysql"),
    TypeOrmModule.forFeature([], "postgres"),
    SecurityModule.register({
      jwt: {
        secret: process.env.JWT_SECRET || "secret",
        expiresIn: process.env.JWT_EXPIRES_IN || "3600s",
        issuer:
          process.env.API_SERVICE_URL ||
          `http://${process.env.API_SERVICE_HOST}:3000`,
        audience: process.env.API_AUDIENCE || "*",
      },
      apiKey: {
        keys: {
          [process.env.API_KEY || "default_key"]: {
            name: "default",
            roles: [],
            permissions: [],
          },
        },
      },
    }),
    forwardRef(() => InfrastructureModule),
    forwardRef(() => DomainModule),
    forwardRef(() => ApplicationModule),
    forwardRef(() => PresentationModule),
  ],
  providers: [Logger],
})
export class AppModule {}
