FROM node:22-alpine

WORKDIR /app

# Copy package files first for better layer caching
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Install dependencies and clean up in a single layer to minimize image size
RUN apk add --no-cache python3 make g++ && \
    yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 --production=false && \
    yarn global add nx nodemon ts-node && \
    # Clean up build artifacts and caches
    yarn cache clean && \
    npm cache clean --force && \
    # Remove build dependencies to reduce size
    apk del python3 make g++ && \
    # Clean up temporary files and package manager caches
    rm -rf /tmp/* /var/cache/apk/* /root/.npm /root/.yarn-cache

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/writer ./apps/writer/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3004

# Expose port
EXPOSE 3004

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/writer", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/writer/src/main.ts"] 