# Extend from shared base image
FROM data-pipeline-base:dev

# Copy service-specific source code
COPY apps/writer ./apps/writer/

# Set service-specific environment variables
ENV PORT=3004

# Expose port
EXPOSE 3004

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/writer", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/writer/src/main.ts"] 