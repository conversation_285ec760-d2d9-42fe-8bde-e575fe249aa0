# PowerShell script to set Docker environment variables for better performance
# Run this before using Docker commands

Write-Host "🔧 Setting Docker environment variables for better performance..." -ForegroundColor Green

# Enable Docker Compose Bake for better build performance
$env:COMPOSE_BAKE = "true"
[Environment]::SetEnvironmentVariable("COMPOSE_BAKE", "true", "User")

# Enable Docker BuildKit
$env:DOCKER_BUILDKIT = "1"
[Environment]::SetEnvironmentVariable("DOCKER_BUILDKIT", "1", "User")

# Enable Compose Docker CLI Build
$env:COMPOSE_DOCKER_CLI_BUILD = "1"
[Environment]::SetEnvironmentVariable("COMPOSE_DOCKER_CLI_BUILD", "1", "User")

# Set timeouts to handle connection issues
$env:COMPOSE_HTTP_TIMEOUT = "120"
[Environment]::SetEnvironmentVariable("COMPOSE_HTTP_TIMEOUT", "120", "User")

$env:DOCKER_CLIENT_TIMEOUT = "120"
[Environment]::SetEnvironmentVariable("DOCKER_CLIENT_TIMEOUT", "120", "User")

Write-Host "✅ Docker environment variables set successfully!" -ForegroundColor Green
Write-Host "📝 You may need to restart your terminal for changes to take effect." -ForegroundColor Yellow
Write-Host ""
Write-Host "🚀 You can now run: yarn docker:dev" -ForegroundColor Cyan 