# PowerShell script to unlock Windows files during build process
param(
    [string]$TargetPath = "node_modules"
)

Write-Host "Unlocking Windows files in $TargetPath..." -ForegroundColor Yellow

# Stop NX daemon
try {
    Write-Host "Stopping NX daemon..." -ForegroundColor Blue
    & yarn nx reset 2>$null
    Start-Sleep -Seconds 2
} catch {
    Write-Host "NX reset failed or not available" -ForegroundColor Yellow
}

# Kill any remaining Node.js processes
try {
    Write-Host "Stopping Node.js processes..." -ForegroundColor Blue
    Get-Process -Name "node" -ErrorAction SilentlyContinue | Stop-Process -Force -ErrorAction SilentlyContinue
    Start-Sleep -Seconds 2
} catch {
    Write-Host "No Node.js processes to stop" -ForegroundColor Yellow
}

# Try to unlock specific problematic files
$problematicFiles = @(
    "node_modules\@nx\nx-win32-x64-msvc\nx.win32-x64-msvc.node",
    "apps\dashboard\node_modules\@nx\nx-win32-x64-msvc\nx.win32-x64-msvc.node"
)

foreach ($file in $problematicFiles) {
    if (Test-Path $file) {
        try {
            Write-Host "Attempting to unlock $file..." -ForegroundColor Blue
            Remove-Item $file -Force -ErrorAction SilentlyContinue
            Write-Host "Successfully removed $file" -ForegroundColor Green
        } catch {
            Write-Host "Could not remove $file - file may be in use" -ForegroundColor Yellow
        }
    }
}

# Use Handle.exe if available (from Sysinternals) to force unlock
$handlePath = Get-Command "handle.exe" -ErrorAction SilentlyContinue
if ($handlePath) {
    Write-Host "Using Handle.exe to force unlock files..." -ForegroundColor Blue
    try {
        & handle.exe -p node.exe -nobanner | ForEach-Object {
            if ($_ -match "(\w+):\s+File\s+.*nx\.win32-x64-msvc\.node") {
                $handleId = $matches[1]
                Write-Host "Closing handle $handleId" -ForegroundColor Blue
                & handle.exe -c $handleId -p node.exe -y -nobanner
            }
        }
    } catch {
        Write-Host "Handle.exe operation failed" -ForegroundColor Yellow
    }
}

Write-Host "File unlock process completed" -ForegroundColor Green 