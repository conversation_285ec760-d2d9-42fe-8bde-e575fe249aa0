import { Injectable, Logger } from "@nestjs/common";
import { JobRepository as CentralizedJobRepository, MySQLServiceBase, Job } from "@data-pipeline/storage";

/**
 * Adapter to extend the centralized JobRepository with additional methods
 * specific to the scheduler service
 */
@Injectable()
export class JobRepositoryAdapter {
  private readonly logger = new Logger(JobRepositoryAdapter.name);
  private readonly tableName = "jobs";

  constructor(
    private readonly jobRepository: CentralizedJobRepository,
    private readonly mysqlService: MySQLServiceBase
  ) {}

  /**
   * Find a job by ID
   * @param id Job ID
   * @returns Job or null if not found
   */
  async findById(id: string): Promise<Job | null> {
    return this.jobRepository.findById(id);
  }

  /**
   * Find all jobs
   * @param options Query options
   * @returns Array of jobs
   */
  async findAll(options: any = {}): Promise<Job[]> {
    return this.jobRepository.find(options);
  }

  /**
   * Create a job
   * @param job Job data
   * @returns Created job
   */
  // Accept id, but created_at and updated_at will be generated by the caller or DB defaults
  async createJob(job: Omit<Job, "created_at" | "updated_at">): Promise<Job> {
    // Ensure required fields are present
    if (!job.collector_id || !job.scheduler_id) {
      throw new Error("Job must have collector_id and scheduler_id");
    }

    return this.jobRepository.create(job);
  }

  /**
   * Update a job
   * @param id Job ID
   * @param job Job data
   * @returns Updated job
   */
  async updateJob(id: string, job: Partial<Job>): Promise<Job | null> {
    const existingJob = await this.jobRepository.findById(id);
    if (!existingJob) {
      return null;
    }

    await this.jobRepository.update(id, job);
    return this.jobRepository.findById(id);
  }

  /**
   * Delete a job
   * @param id Job ID
   * @returns True if deleted, false if not found
   */
  async deleteJob(id: string): Promise<boolean> {
    const query = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.mysqlService.query<any>(query, [id]);
    return result.affectedRows > 0;
  }

  /**
   * Find jobs by collector ID
   * @param collectorId Collector ID
   * @returns Array of jobs
   */
  async findByCollectorId(collectorId: string): Promise<Job[]> {
    const query = `SELECT * FROM ${this.tableName} WHERE collector_id = ?`;
    return this.mysqlService.query(query, [collectorId]);
  }

  /**
   * Find jobs by scheduler ID
   * @param schedulerId Scheduler ID
   * @returns Array of jobs
   */
  async findBySchedulerId(schedulerId: string): Promise<Job[]> {
    const query = `SELECT * FROM ${this.tableName} WHERE scheduler_id = ?`;
    return this.mysqlService.query(query, [schedulerId]);
  }

  /**
   * Find jobs by type
   * @param type Job type
   * @returns Array of jobs
   */
  async findByType(type: string): Promise<Job[]> {
    const query = `SELECT * FROM ${this.tableName} WHERE type = ?`;
    return this.mysqlService.query(query, [type]);
  }

  /**
   * Find jobs that are due to run
   * @param now Current time
   * @returns Array of jobs
   */
  async findDueJobs(now: Date): Promise<Job[]> {
    const query = `
      SELECT * FROM ${this.tableName}
      WHERE enabled = 1
      AND (next_run IS NULL OR next_run <= ?)
      ORDER BY next_run ASC
    `;
    return this.mysqlService.query(query, [now]);
  }
}
