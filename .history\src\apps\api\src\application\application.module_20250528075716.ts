import { forwardRef, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { DomainModule } from "../domain/domain.module";
import { DataFacade } from "./facades/data.facade";
import { InitializationService } from "./services/initialization.service";
import { KitcoCmsDataService } from "./services/kitco-cms-data.service";
import { WorkflowApiService } from "./services/workflow-api.service";
import { DataCacheService } from "./services/data-cache.service";
import { CryptoDataService } from "./services/crypto-data.service";
import { AssetDataTransformerService } from "./services/asset-data-transformer.service";
import { InfrastructureModule } from "../infrastructure/infrastructure.module";
import { RedisCacheModule } from '@data-pipeline/cache';

@Module({
  imports: [
    forwardRef(() => DomainModule),
    forwardRef(() => InfrastructureModule),
    // Distributed cache for crypto data
    RedisCacheModule.register(),
  ],
  controllers: [],
  providers: [
    DataFacade,
    InitializationService,
    KitcoCmsDataService,
    WorkflowApiService,
    DataCacheService,
    CryptoDataService,
    AssetDataTransformerService
  ],
  exports: [
    DataFacade,
    InitializationService,
    KitcoCmsDataService,
    WorkflowApiService,
    DataCacheService,
    CryptoDataService,
    AssetDataTransformerService
  ],
})
export class ApplicationModule {}
