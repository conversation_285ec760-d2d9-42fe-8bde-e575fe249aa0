{"compilerOptions": {"target": "es2015", "module": "commonjs", "moduleResolution": "node", "declaration": true, "strict": true, "noImplicitAny": true, "strictNullChecks": true, "strictFunctionTypes": true, "strictPropertyInitialization": false, "noImplicitThis": true, "alwaysStrict": true, "noUnusedLocals": true, "noUnusedParameters": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "esModuleInterop": true, "skipLibCheck": true, "skipDefaultLibCheck": true, "experimentalDecorators": true, "emitDecoratorMetadata": true, "baseUrl": ".", "paths": {"@data-pipeline/cache": ["libs/cache/src/index.ts"], "@data-pipeline/config": ["libs/config/src/index.ts"], "@data-pipeline/core": ["libs/core/src/index.ts"], "@data-pipeline/kgx": ["libs/kgx/src/index.ts"], "@data-pipeline/logging": ["libs/logging/src/index.ts"], "@data-pipeline/messaging": ["libs/messaging/src/index.ts"], "@data-pipeline/monitoring": ["libs/monitoring/src/index.ts"], "@data-pipeline/security": ["libs/security/src/index.ts"], "@data-pipeline/storage": ["libs/storage/src/index.ts"], "@data-pipeline/utils": ["libs/utils/src/index.ts"]}, "preserveSymlinks": true}, "exclude": ["node_modules", "tmp"]}