#!/bin/bash

# Shell script to unlock Unix/Linux files during build process
TARGET_PATH="${1:-node_modules}"

echo "Unlocking Unix/Linux files in $TARGET_PATH..."

# Stop NX daemon
echo "Stopping NX daemon..."
yarn nx reset 2>/dev/null || echo "NX reset failed or not available"
sleep 2

# Kill any remaining Node.js processes
echo "Stopping Node.js processes..."
pkill -f "node" 2>/dev/null || echo "No Node.js processes to stop"
sleep 2

# Try to unlock specific problematic files
PROBLEMATIC_FILES=(
    "node_modules/@nx/nx-linux-x64-gnu/nx.linux-x64-gnu.node"
    "node_modules/@nx/nx-linux-x64-musl/nx.linux-x64-musl.node"
    "node_modules/@nx/nx-darwin-x64/nx.darwin-x64.node"
    "node_modules/@nx/nx-darwin-arm64/nx.darwin-arm64.node"
    "apps/dashboard/node_modules/@nx/nx-linux-x64-gnu/nx.linux-x64-gnu.node"
    "apps/dashboard/node_modules/@nx/nx-linux-x64-musl/nx.linux-x64-musl.node"
    "apps/dashboard/node_modules/@nx/nx-darwin-x64/nx.darwin-x64.node"
    "apps/dashboard/node_modules/@nx/nx-darwin-arm64/nx.darwin-arm64.node"
)

for file in "${PROBLEMATIC_FILES[@]}"; do
    if [ -f "$file" ]; then
        echo "Attempting to unlock $file..."
        if rm -f "$file" 2>/dev/null; then
            echo "Successfully removed $file"
        else
            echo "Could not remove $file - file may be in use"
        fi
    fi
done

# Use lsof to find and kill processes holding file locks
echo "Checking for processes holding file locks..."
if command -v lsof >/dev/null 2>&1; then
    echo "Using lsof to find locked files..."
    
    # Find processes holding locks on nx native files
    LOCKED_PIDS=$(lsof +D node_modules 2>/dev/null | grep "nx.*\.node" | awk '{print $2}' | sort -u)
    
    if [ -n "$LOCKED_PIDS" ]; then
        echo "Found processes holding locks: $LOCKED_PIDS"
        for pid in $LOCKED_PIDS; do
            echo "Killing process $pid"
            kill -TERM "$pid" 2>/dev/null || echo "Could not kill process $pid"
        done
        sleep 2
        
        # Force kill if still running
        for pid in $LOCKED_PIDS; do
            if kill -0 "$pid" 2>/dev/null; then
                echo "Force killing process $pid"
                kill -KILL "$pid" 2>/dev/null || echo "Could not force kill process $pid"
            fi
        done
    else
        echo "No locked files found"
    fi
else
    echo "lsof not available, skipping file lock detection"
fi

# Use fuser if available (alternative to lsof)
if command -v fuser >/dev/null 2>&1; then
    echo "Using fuser to unlock files..."
    for file in "${PROBLEMATIC_FILES[@]}"; do
        if [ -f "$file" ]; then
            echo "Checking locks on $file..."
            fuser -k "$file" 2>/dev/null || echo "No locks found on $file"
        fi
    done
fi

# Clear any yarn/npm cache that might be holding locks
echo "Clearing package manager caches..."
yarn cache clean 2>/dev/null || echo "Yarn cache clean failed"
npm cache clean --force 2>/dev/null || echo "NPM cache clean failed"

echo "File unlock process completed" 