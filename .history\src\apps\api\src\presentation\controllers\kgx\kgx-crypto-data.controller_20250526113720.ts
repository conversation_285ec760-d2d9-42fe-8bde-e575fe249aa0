import { <PERSON>, <PERSON>, <PERSON><PERSON>, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, NotFoundException, Header, Inject, UnauthorizedException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { DataCacheService } from '../../../application/services/data-cache.service';
import { CryptoDataService } from '../../../application/services/crypto-data.service';
import { SSEService } from '@data-pipeline/messaging';
import { Public, ApiKeyService } from '@data-pipeline/security';
import { v4 as uuidv4 } from 'uuid';
import { PostgresService } from '../../../infrastructure/database/services/postgres.service';
import { Req } from '@nestjs/common';

interface CryptoRecord {
  id: string;
  time: string;
  symbol: string;
  price: number;
  high?: number;
  low?: number;
  price_change?: number;
  price_change_percent?: number;
  change?: number;
  change_due_usd_percent?: number;
  change_due_trade_percent?: number;
  change_usd?: number;
  change_due_usd?: number;
  ask?: number;
  bid?: number;
  [key: string]: any;
}

@ApiTags('kgx-crypto-data')
@Controller(['kgx-crypto-data', 'v1/kgx-crypto-data'])
export class KgxCryptoDataController {
  private readonly logger = new Logger(KgxCryptoDataController.name);

  constructor(
    private readonly dataCacheService: DataCacheService,
    private readonly cryptoDataService: CryptoDataService,
    private readonly sseService: SSEService,
    @Inject(PostgresService) private readonly postgresService: PostgresService,
    private readonly apiKeyService: ApiKeyService
  ) { }

  @Get('getValue')
  @ApiOperation({ summary: 'Get single crypto value in oil API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data for a specific symbol in oil API format' })
  @ApiResponse({ status: 400, description: 'Bad request - symbol parameter is required' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: true, description: 'Cryptocurrency symbol or comma-separated list of symbols' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getValueFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('symbol') symbol?: string,
    @Query('apikey') apikey?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    try {
      if (!symbol) {
        this.logger.warn('No symbol parameter provided');
        throw new BadRequestException('Symbol parameter is required');
      }

      this.logger.log(`Received request for crypto value for symbol(s): ${symbol}`);

      if (symbol.includes(',')) {
        this.logger.log(`Multiple symbols detected, redirecting to PM format: ${symbol}`);
        return this.getPreciousMetalsFormat(
          req,
          res,
          apikey,
          symbol,
          type,
          version,
          dateFormat,
          timeFormat,
          includeKgx
        );
      }

      const symbolToQuery = symbol.trim().toUpperCase();

      if (!/^[A-Z0-9-]+$/.test(symbolToQuery)) {
        this.logger.warn(`Invalid symbol format: ${symbolToQuery}`);
        throw new BadRequestException('Invalid symbol format. Only alphanumeric characters and hyphens are allowed.');
      }

      try {
        const latestData = await this.postgresService.query(
          `WITH latest_times AS (
            SELECT symbol, MAX(time) as latest_time
            FROM kgx_crypto_data
            WHERE symbol = $1
            GROUP BY symbol
          )
          SELECT d.*
          FROM kgx_crypto_data d
          JOIN latest_times lt ON d.symbol = lt.symbol AND d.time = lt.latest_time
          WHERE d.symbol = $1
          LIMIT 1`,
          [symbolToQuery]
        );

        if (!latestData || latestData.length === 0) {
          this.logger.warn(`No crypto data found for symbol: ${symbolToQuery}`);
          throw new NotFoundException(`No data available for symbol: ${symbolToQuery}`);
        }

        this.logger.log(`Found crypto data for symbol: ${symbolToQuery}`);

        const record = latestData[0];

        const formattedData = latestData.map((record: CryptoRecord) => ({
          High: record.high || record.price,
          ChangeTrade: record.price_change || 0,
          Symbol: record.symbol,
          Mid: record.price,
          Change: record.change || record.price_change || 0,
          ChangePercentUSD: record.change_due_usd_percent ? Math.abs(record.change_due_usd_percent) : 0.02,
          Unit: 'USD',
          Timestamp: this.formatTimestamp(record.time, dateFormat, timeFormat),
          ChangePercentage: record.price_change_percent || 0,
          ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
          Low: record.low || record.price,
          Currency: 'USD',
          Ask: record.ask || (record.price * 1.001),
          ChangeUSD: record.change_usd || record.change_due_usd || 0,
          Bid: record.bid || (record.price * 0.999)
        }));

        this.logger.log(`Returning crypto value for ${symbol} in oil API format`);
        return formattedData;
      } catch (error: any) {
        if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
          throw new InternalServerErrorException('Database connection error. Please try again later.');
        }
        throw error;
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
      this.logger.error(`Error getting crypto value: ${errorMessage}`, errorStack);

      if (errorMessage.includes('timeout') || errorMessage.includes('connection')) {
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      throw new InternalServerErrorException('Failed to retrieve crypto value. Please try again later.');
    }
  }
  
  // @Get('getPM')
  // @ApiOperation({ summary: 'Get crypto data in precious metals API format' })
  // @ApiResponse({ status: 200, description: 'Return the crypto data in format matching precious metals API' })
  // @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  // @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  // @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  // @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  // @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  // @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  // @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values (set to any value to enable)' })
  // async getPreciousMetalsFormat(
  //   @Req() req: Request,
  //   @Res({ passthrough: true }) res: Response,
  //   @Query('apikey') apikey?: string,
  //   @Query('symbol') symbolParam?: string,
  //   @Query('type') type: string = 'json',
  //   @Query('ver') version?: string,
  //   @Query('df') dateFormat?: string,
  //   @Query('tf') timeFormat?: string,
  //   @Query('kgx') includeKgx?: string
  // ) {
  //   this.logger.log(`Received request for crypto data in PM format, symbols: ${symbolParam || 'all'}`);

  //   try {
  //     // Prepare symbol filter array (uppercase, trimmed)
  //     const symbols = symbolParam
  //       ? symbolParam.split(',').map(s => s.trim().toUpperCase())
  //       : undefined;
  //     // Use service for safe, parameterized query
  //     const latestData = await this.postgresService.getCryptoPrices(symbols);
  //     if (!latestData || latestData.length === 0) {
  //       this.logger.warn('No crypto data found');
  //       throw new NotFoundException('No data available');
  //     }

  //     this.logger.log(`Found ${latestData.length} crypto records`);

  //     const formattedData = latestData.map((record: CryptoRecord) => {
  //       const baseData = {
  //         High: record.high || record.price,
  //         ChangeTrade: record.price_change || 0,
  //         Symbol: record.symbol,
  //         Mid: record.price,
  //         Change: record.change || record.price_change || 0,
  //         ChangePercentUSD: record.change_due_usd_percent ? Math.abs(record.change_due_usd_percent) : 0.02,
  //         Unit: 'USD',
  //         Timestamp: this.formatTimestamp(record.time, dateFormat, timeFormat),
  //         ChangePercentage: record.price_change_percent || 0,
  //         ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
  //         Low: record.low || record.price,
  //         Currency: 'USD',
  //         Ask: record.ask || (record.price * 1.001),
  //         ChangeUSD: record.change_usd || record.change_due_usd || 0,
  //         Bid: record.bid || (record.price * 0.999),
  //         KgxValue: record.kgx_value,
  //       };
        
  //       return baseData;
  //     });

  //     const response = {
  //       CryptoMetals: {
  //         crypto: formattedData,
  //         ...(includeKgx && { KgxValues: formattedData.map((item: any) => item.KgxValue) })
  //       }
  //     };

  //     this.logger.log(`Returning ${formattedData.length} crypto records in PM format  data: ${JSON.stringify(formattedData)}`);
  //     return response;
  //   } catch (error) {
  //     // Log and rethrow mapped exceptions
  //     const msg = error instanceof Error ? error.message : 'Unknown error';
  //     this.logger.error(`Error in getPM: ${msg}`, error instanceof Error ? error.stack : undefined);
  //     if (error instanceof NotFoundException) throw error;
  //     throw new InternalServerErrorException('Failed to retrieve crypto data');
  //   }
  // }

  /**
   * Format timestamp based on provided formats
   * @param timestamp The timestamp to format
   * @param dateFormat Optional date format (1=MM/DD/YYYY, 2=YYYY-MM-DD)
   * @param timeFormat Optional time format (1=12h, 2=24h)
   * @returns Formatted timestamp string
   */
  private formatTimestamp(timestamp: string | Date, dateFormat?: string, timeFormat?: string): string {
    const date = new Date(timestamp);

    // Default format is YYYY-MM-DD HH:MM:SS (24h)
    if (!dateFormat && !timeFormat) {
      return date.toISOString()
        .replace('T', ' ')
        .replace(/\.\d+Z$/, '');
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    let formattedDate: string;
    if (dateFormat === '1') {
      formattedDate = `${month}/${day}/${year}`;
    } else {
      formattedDate = `${year}-${month}-${day}`;
    }

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    let formattedTime: string;
    if (timeFormat === '1') {
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12;
      formattedTime = `${hours}:${minutes}:${seconds} ${ampm}`;
    } else {
      formattedTime = `${String(hours).padStart(2, '0')}:${minutes}:${seconds}`;
    }

    return `${formattedDate} ${formattedTime}`;
  }

  // @Get('getBM')
  // @ApiOperation({ summary: 'Get crypto data in base metals API format' })
  // @ApiResponse({ status: 200, description: 'Return the crypto data in format matching base metals API' })
  // @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  // @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  // @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  // @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  // @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  // @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  // @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  // async getBaseMetalsFormat(
  //   @Req() req: Request,
  //   @Res({ passthrough: true }) res: Response,
  //   @Query('apikey') apikey?: string,
  //   @Query('symbol') symbolParam?: string,
  //   @Query('type') type: string = 'json',
  //   @Query('ver') version?: string,
  //   @Query('df') dateFormat?: string,
  //   @Query('tf') timeFormat?: string,
  //   @Query('kgx') includeKgx?: string
  // ) {
  //   this.logger.log(`Received request for crypto data in BM format, symbols: ${symbolParam || 'all'}`);

  //   try {
  //     // Prepare symbol filter array
  //     const symbols = symbolParam
  //       ? symbolParam.split(',').map(s => s.trim().toUpperCase())
  //       : undefined;
  //     // Fetch latest records via service for safe queries
  //     const latestData = await this.postgresService.getCryptoPrices(symbols);
  //     if (!latestData || latestData.length === 0) {
  //       this.logger.warn('No crypto data found');
  //       throw new NotFoundException('No data available');
  //     }

  //     this.logger.log(`Found ${latestData.length} crypto records`);

  //     const formattedData = latestData.map((record: CryptoRecord) => ({
  //       Symbol: record.symbol,
  //       High: record.high || record.price,
  //       Low: record.low || record.price,
  //       ChangeTrade: record.price_change || 0,
  //       Price: record.price,
  //       ChangeUSD: record.change_usd || record.change_due_usd || 0,
  //       Change: record.change || record.price_change || 0,
  //       ChangePercentUSD: record.change_due_usd_percent ? Math.abs(record.change_due_usd_percent) : 0.05,
  //       ChangePercentage: record.price_change_percent || 0,
  //       ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
  //       Timestamp: this.formatTimestamp(record.time, dateFormat, timeFormat),
  //       Currency: 'USD'
  //     }));

  //     const response = {
  //       BaseMetals: {
  //         BM: formattedData
  //       }
  //     };

  //     this.logger.log(`Returning ${formattedData.length} crypto records in BM format`);
  //     return response;
  //   } catch (error) {
  //     // Log and map exceptions
  //     const msg = error instanceof Error ? error.message : 'Unknown error';
  //     this.logger.error(`Error in getBM: ${msg}`, error instanceof Error ? error.stack : undefined);
  //     if (error instanceof NotFoundException) throw error;
  //     throw new InternalServerErrorException('Failed to retrieve crypto data');
  //   }
  // }



  @Get('getLatestCryptoData')
  @ApiOperation({ summary: 'Get latest crypto data with USD index information' })
  @ApiResponse({ status: 200, description: 'Returns the latest crypto data with USD index details' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  async getLatestCryptoData(
    @Req() req: Request,
    @Query('apikey') apikey?: string
  ) {
    this.logger.log('Received request for latest crypto data');

    const response = await this.cryptoDataService.getLatestCryptoData();

    this.logger.log(`RESPONSE PAYLOAD: ${JSON.stringify(response)}`);
    return response;
  }

  @Get('details')
  @Public()
  @ApiOperation({ summary: 'Get detailed KGX crypto data' })
  @ApiResponse({ status: 200, description: 'Return detailed crypto data with all available information' })
  async getDetailedCryptoData(@Res({ passthrough: true }) res: Response) {
    this.logger.log('Received request for detailed crypto data');

    const response = await this.cryptoDataService.getDetailedCryptoData();

    this.logger.log(`DETAILED RESPONSE PAYLOAD: ${JSON.stringify(response)}`);
    return response;
  }

  @Get('stream')
  @Public()
  @Header('Content-Type', 'text/event-stream')
  @Header('Cache-Control', 'no-cache')
  @Header('Connection', 'keep-alive')
  @ApiOperation({ summary: 'Stream real-time crypto data updates via SSE' })
  @ApiResponse({ status: 200, description: 'Stream of SSE events with crypto data updates' })
  async streamCryptoData(@Res() res: Response) {
    const clientId = uuidv4();
    this.logger.log(`Client ${clientId} connected to crypto data stream`);

    res.setHeader('Content-Type', 'text/event-stream');
    res.setHeader('Cache-Control', 'no-cache');
    res.setHeader('Connection', 'keep-alive');
    const initialData = this.cryptoDataService.getInitialStreamData(clientId);
    if (initialData) {
      this.logger.log(`Sending initial data to client ${clientId}`);

      res.write(`event: initial-data\n`);
      res.write(`data: ${JSON.stringify(initialData)}\n\n`);
    }

    const subject = this.cryptoDataService.addStreamClient(clientId);

    const subscription = subject.subscribe((message) => {
      if (message.type === 'crypto-update') {
        res.write(`event: crypto-update\n`);
        res.write(`data: ${JSON.stringify(message.data)}\n\n`);
      }
    });

    res.on('close', () => {
      this.logger.log(`Client ${clientId} disconnected from crypto data stream`);
      subscription.unsubscribe();
      this.cryptoDataService.removeStreamClient(clientId);
    });
  }
}
