#!/usr/bin/env node

/**
 * Test script to verify asset grouping functionality
 * Run with: node test-asset-grouping.js
 */

const API_BASE_URL = 'http://localhost:3000/api';
const API_KEY = '61d6e1f73101fa0b13d6a5301e649fef78dea03b50b6b6c44a10a4600ceefe57';

// Asset type mapping function (same as in frontend)
function getAssetTypeFromCategory(category) {
  switch (category) {
    case 'base-metals':
      return 'base-metals';
    case 'precious-metals':
      return 'precious-metals';
    case 'energies':
      return 'energies';
    case 'crypto':
      return 'crypto';
    default:
      return 'crypto';
  }
}

// Determine asset category (same logic as frontend)
function determineAssetCategory(item) {
  // Check if asset_type is provided in the response
  if (item.asset_type) {
    switch (item.asset_type.toUpperCase()) {
      case 'CRYPTOCURRENCY':
        return 'crypto';
      case 'PRECIOUS_METALS':
        return 'precious-metals';
      case 'BASE_METALS':
        return 'base-metals';
      case 'FOREX':
        return 'forex';
      default:
        return 'crypto';
    }
  }

  // Fallback: determine by symbol patterns
  const symbol = (item.Symbol || item.symbol || '').toUpperCase();
  
  // Common precious metals
  if (['GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM', 'XAU', 'XAG', 'XPT', 'XPD'].includes(symbol)) {
    return 'precious-metals';
  }
  
  // Common base metals
  if (['COPPER', 'ALUMINUM', 'ZINC', 'NICKEL', 'LEAD', 'TIN'].includes(symbol)) {
    return 'base-metals';
  }
  
  // Common forex pairs
  if (symbol.length === 6 && symbol.includes('USD')) {
    return 'forex';
  }
  
  // Default to crypto for everything else
  return 'crypto';
}

async function testAssetGrouping() {
  console.log('🧪 Testing Asset Grouping Functionality\n');

  try {
    console.log('📡 Fetching all assets from API...');
    const response = await fetch(`${API_BASE_URL}/v1/kgx/getValue?symbol=all&apikey=${API_KEY}`);
    
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    console.log(`✅ Fetched ${data.length} total assets\n`);

    // Group assets by type
    const groups = {
      'all': [],
      'base-metals': [],
      'precious-metals': [],
      'energies': [],
      'crypto': []
    };

    data.forEach(item => {
      const category = determineAssetCategory(item);
      const assetType = getAssetTypeFromCategory(category);
      
      // Add category to item for consistency with frontend
      item.category = category;
      
      groups[assetType].push(item);
      groups.all.push(item);
    });

    // Display grouping results
    console.log('📊 Asset Grouping Results:');
    console.log('═'.repeat(50));
    
    Object.entries(groups).forEach(([type, assets]) => {
      if (type === 'all') return; // Skip 'all' for detailed breakdown
      
      console.log(`\n🏷️  ${type.toUpperCase().replace('-', ' ')} (${assets.length} assets)`);
      
      if (assets.length > 0) {
        // Show first few symbols as examples
        const examples = assets.slice(0, 5).map(a => a.Symbol).join(', ');
        console.log(`   Examples: ${examples}${assets.length > 5 ? '...' : ''}`);
        
        // Show price range
        const prices = assets.map(a => parseFloat(a.Price || a.Mid || 0)).filter(p => p > 0);
        if (prices.length > 0) {
          const minPrice = Math.min(...prices);
          const maxPrice = Math.max(...prices);
          console.log(`   Price range: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`);
        }
      }
    });

    console.log('\n📈 Summary:');
    console.log(`   Total Assets: ${groups.all.length}`);
    console.log(`   Base Metals: ${groups['base-metals'].length}`);
    console.log(`   Precious Metals: ${groups['precious-metals'].length}`);
    console.log(`   Energies: ${groups['energies'].length}`);
    console.log(`   Cryptocurrencies: ${groups['crypto'].length}`);

    // Test search functionality
    console.log('\n🔍 Testing Search Functionality:');
    const searchTerm = 'BTC';
    const searchResults = groups.all.filter(item => 
      item.Symbol?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.category?.toLowerCase().includes(searchTerm.toLowerCase())
    );
    console.log(`   Search for "${searchTerm}": ${searchResults.length} results`);
    if (searchResults.length > 0) {
      console.log(`   Found: ${searchResults.map(r => r.Symbol).join(', ')}`);
    }

    console.log('\n✨ Asset grouping test completed successfully!');
    console.log('\n📋 Dashboard should now show:');
    console.log('   ✓ Tabs for each asset type with counts');
    console.log('   ✓ Filtered data based on selected tab');
    console.log('   ✓ Asset type badges when viewing "All Assets"');
    console.log('   ✓ Search functionality across all fields');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

// Run the test
testAssetGrouping();
