import { Module } from "@nestjs/common";
import { DatabaseService } from "./services/database.service";
import { MySQLService } from "./services/mysql.service";
import {
  DatabaseConfigProvider,
  DatabaseConfigModule,
  CollectorRepository,
  JobRepository as CentralizedJobRepository,
  SchedulerRepository as CentralizedSchedulerRepository,
  MySQLModule,
  MySQLServiceBase,
  RepositoriesModule,
} from "@data-pipeline/storage";

/**
 * Database module for the Scheduler microservice
 * Uses only MySQL - simplified configuration to avoid PostgreSQL dependencies
 */
@Module({
  imports: [
    DatabaseConfigModule,
    MySQLModule.register({
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return configProvider.getMySQLConfig();
      },
      inject: [DatabaseConfigProvider],
    }),
    RepositoriesModule,
  ],
  providers: [
    {
      provide: DatabaseService,
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return new DatabaseService(configProvider.getMySQLConfig());
      },
      inject: [DatabaseConfigProvider],
    },
    {
      provide: MySQLService,
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return new MySQLService(configProvider.getMySQLConfig());
      },
      inject: [DatabaseConfigProvider],
    },
    CentralizedJobRepository,
    CentralizedSchedulerRepository,
    CollectorRepository,
  ],
  exports: [
    CentralizedJobRepository,
    CentralizedSchedulerRepository,
    CollectorRepository,
    DatabaseService,
    MySQLService,
    MySQLServiceBase,
    DatabaseConfigModule,
    MySQLModule,
    RepositoriesModule,
  ],
})
export class DatabaseModule {}
