#!/bin/bash

# KGX Crypto Data Application - Deployment Script
# Author: System Administrator
# Version: 1.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/opt/kgx-app"
REPO_URL="${REPO_URL:-}"
BRANCH="${BRANCH:-main}"
DOMAIN="${DOMAIN:-localhost}"
EMAIL="${EMAIL:-<EMAIL>}"

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Docker is installed and running
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed. Please run install.sh first."
    fi
    
    if ! docker info &> /dev/null; then
        error "Docker is not running or user doesn't have permission. Please check Docker installation."
    fi
    
    # Check if Docker Compose is available
    if ! docker compose version &> /dev/null; then
        error "Docker Compose is not available. Please run install.sh first."
    fi
    
    # Check if application directory exists
    if [[ ! -d "$APP_DIR" ]]; then
        error "Application directory $APP_DIR does not exist. Please run install.sh first."
    fi
    
    log "Prerequisites check passed"
}

# Clone or update repository
setup_repository() {
    log "Setting up repository..."
    
    if [[ -z "$REPO_URL" ]]; then
        warn "REPO_URL not set. Assuming code is already in place."
        if [[ ! -f "$APP_DIR/src/docker-compose.yml" ]]; then
            error "No repository URL provided and no existing code found. Please set REPO_URL environment variable."
        fi
        return
    fi
    
    cd "$APP_DIR"
    
    if [[ -d ".git" ]]; then
        log "Updating existing repository..."
        git fetch origin
        git checkout "$BRANCH"
        git pull origin "$BRANCH"
    else
        log "Cloning repository..."
        git clone -b "$BRANCH" "$REPO_URL" .
    fi
    
    log "Repository setup completed"
}

# Generate secure passwords and keys
generate_secrets() {
    log "Generating secure passwords and API keys..."
    
    # Generate random passwords
    POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    MYSQL_ROOT_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    MYSQL_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
    JWT_SECRET=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
    API_KEY=$(openssl rand -hex 32)
    
    log "Secrets generated successfully"
}

# Create environment configuration
create_environment() {
    log "Creating environment configuration..."
    
    cd "$APP_DIR/src"
    
    # Create .env file
    cat > .env <<EOF
# Database Configuration
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_USER=postgres
POSTGRES_PASSWORD=$POSTGRES_PASSWORD
POSTGRES_DB=kitco

MYSQL_HOST=mysql
MYSQL_PORT=3306
MYSQL_USER=kitco_user
MYSQL_USERNAME=kitco_user
MYSQL_PASSWORD=$MYSQL_PASSWORD
MYSQL_ROOT_PASSWORD=$MYSQL_ROOT_PASSWORD
MYSQL_DATABASE=kitco

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=$REDIS_PASSWORD

# Hades Database (External - Update with actual credentials)
HADES_SERVER=hades.kitco.com
HADES_PORT=5432
HADES_DATABASE=kitco
HADES_USER=your_hades_username
HADES_PASSWORD=your_hades_password
HADES_CONNECT_TIMEOUT=30000

# Application Configuration
NODE_ENV=production
LOG_LEVEL=info
API_KEY=$API_KEY
JWT_SECRET=$JWT_SECRET

# Service URLs (internal Docker network)
API_SERVICE_URL=http://api:3000
SCHEDULER_SERVICE_URL=http://scheduler:3001
COLLECTOR_SERVICE_URL=http://collector:3002
PROCESSOR_SERVICE_URL=http://processor:3003
WRITER_SERVICE_URL=http://writer:3004

# Service Ports
API_PORT=3000
SCHEDULER_PORT=3001
COLLECTOR_PORT=3002
PROCESSOR_PORT=3003
WRITER_PORT=3004

# External APIs (Update with actual keys)
KDS2_BASE_URL=https://kds2.kitco.com
KDS2_API_KEY=your_kds2_api_key_here
KDS2_API_URL=https://kds2.kitco.com/api
OPEN_EXCHANGE_RATES_URL=https://openexchangerates.org/api
USD_INDEX_API_URL=https://kdb-api.prod.kitco.com/api/usd-index

# Feature Flags
USE_TIMESCALE_DB=true
SAVE_TO_MULTIPLE_DATABASES=true

# Redis Configuration
REDIS_KEEPALIVE=true
REDIS_CONNECT_TIMEOUT=10000
REDIS_RECONNECT_ATTEMPTS=20
REDIS_BACKOFF_FACTOR=1.5
REDIS_MAX_BACKOFF=10000
EOF
    
    # Create .env.local for local overrides
    cp .env .env.local
    
    log "Environment configuration created"
    warn "Please update the following in .env.local:"
    warn "- HADES_USER and HADES_PASSWORD with actual credentials"
    warn "- KDS2_API_KEY with actual API key"
    warn "- Any other external API keys as needed"
}

# Update docker-compose for production
update_docker_compose() {
    log "Updating Docker Compose configuration for production..."
    
    cd "$APP_DIR/src"
    
    # Create production docker-compose override
    cat > docker-compose.prod.yml <<EOF
services:
  postgres:
    volumes:
      - $APP_DIR/data/postgres:/var/lib/postgresql/data
    restart: unless-stopped
    
  mysql:
    volumes:
      - $APP_DIR/data/mysql:/var/lib/mysql
    restart: unless-stopped
    
  redis:
    volumes:
      - $APP_DIR/data/redis:/data
    restart: unless-stopped
    
  api:
    volumes:
      - $APP_DIR/data/logs/api:/app/logs
    restart: unless-stopped
    
  scheduler:
    volumes:
      - $APP_DIR/data/logs/scheduler:/app/logs
    restart: unless-stopped
    
  collector:
    volumes:
      - $APP_DIR/data/logs/collector:/app/logs
    restart: unless-stopped
    
  processor:
    volumes:
      - $APP_DIR/data/logs/processor:/app/logs
    restart: unless-stopped
    
  writer:
    volumes:
      - $APP_DIR/data/logs/writer:/app/logs
    restart: unless-stopped
    
  dashboard:
    volumes:
      - $APP_DIR/data/logs/dashboard:/app/logs
    restart: unless-stopped
EOF
    
    log "Docker Compose configuration updated"
}

# Configure Nginx
configure_nginx() {
    log "Configuring Nginx..."
    
    sudo tee /etc/nginx/sites-available/kgx-app > /dev/null <<EOF
server {
    listen 80;
    server_name $DOMAIN;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    
    # Rate limiting
    limit_req_zone \$binary_remote_addr zone=api:10m rate=10r/s;
    limit_req_zone \$binary_remote_addr zone=dashboard:10m rate=5r/s;
    
    # API endpoints
    location /api/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:3000/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # KGX API endpoints (legacy support)
    location /kgx-crypto-data/ {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://localhost:3000/kgx-crypto-data/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # Timeouts
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
    
    # Dashboard
    location / {
        limit_req zone=dashboard burst=10 nodelay;
        
        proxy_pass http://localhost:3010/;
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Health check endpoint
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF
    
    # Enable the site
    sudo ln -sf /etc/nginx/sites-available/kgx-app /etc/nginx/sites-enabled/
    sudo rm -f /etc/nginx/sites-enabled/default
    
    # Test configuration
    sudo nginx -t
    sudo systemctl reload nginx
    
    log "Nginx configured successfully"
}

# Deploy application
deploy_application() {
    log "Deploying application..."
    
    cd "$APP_DIR/src"
    
    # Pull latest images
    docker compose -f docker-compose.yml -f docker-compose.prod.yml pull
    
    # Build application images
    log "Building application images..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml build
    
    # Start databases first
    log "Starting database services..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d postgres mysql redis
    
    # Wait for databases to be ready
    log "Waiting for databases to be ready..."
    sleep 30
    
    # Check database health
    for i in {1..30}; do
        if docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres; then
            log "PostgreSQL is ready"
            break
        fi
        if [[ $i -eq 30 ]]; then
            error "PostgreSQL failed to start"
        fi
        sleep 2
    done
    
    for i in {1..30}; do
        if docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysqladmin ping -h localhost -u root -p$MYSQL_ROOT_PASSWORD; then
            log "MySQL is ready"
            break
        fi
        if [[ $i -eq 30 ]]; then
            error "MySQL failed to start"
        fi
        sleep 2
    done
    
    # Start all services
    log "Starting all services..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
    
    log "Application deployed successfully"
}

# Create systemd service
create_systemd_service() {
    log "Creating systemd service..."
    
    sudo tee /etc/systemd/system/kgx-app.service > /dev/null <<EOF
[Unit]
Description=KGX Crypto Data Application
Requires=docker.service
After=docker.service

[Service]
Type=oneshot
RemainAfterExit=yes
WorkingDirectory=$APP_DIR/src
ExecStart=/usr/bin/docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
ExecStop=/usr/bin/docker compose -f docker-compose.yml -f docker-compose.prod.yml down
TimeoutStartSec=300
User=$USER
Group=$USER

[Install]
WantedBy=multi-user.target
EOF
    
    sudo systemctl daemon-reload
    sudo systemctl enable kgx-app.service
    
    log "Systemd service created and enabled"
}

# Verify deployment
verify_deployment() {
    log "Verifying deployment..."
    
    cd "$APP_DIR/src"
    
    # Check if all services are running
    sleep 10
    
    if ! docker compose -f docker-compose.yml -f docker-compose.prod.yml ps | grep -q "Up"; then
        error "Some services are not running. Check logs with: docker compose logs"
    fi
    
    # Test API endpoints
    log "Testing API endpoints..."
    
    for i in {1..30}; do
        if curl -s http://localhost:3000/api/health > /dev/null; then
            log "API health check passed"
            break
        fi
        if [[ $i -eq 30 ]]; then
            warn "API health check failed, but continuing..."
        fi
        sleep 2
    done
    
    # Test dashboard
    for i in {1..30}; do
        if curl -s http://localhost:3010 > /dev/null; then
            log "Dashboard is accessible"
            break
        fi
        if [[ $i -eq 30 ]]; then
            warn "Dashboard is not accessible, but continuing..."
        fi
        sleep 2
    done
    
    log "Deployment verification completed"
}

# Setup SSL certificate
setup_ssl() {
    if [[ "$DOMAIN" == "localhost" ]]; then
        warn "Skipping SSL setup for localhost"
        return
    fi
    
    log "Setting up SSL certificate for $DOMAIN..."
    
    # Get SSL certificate
    sudo certbot --nginx -d "$DOMAIN" --email "$EMAIL" --agree-tos --non-interactive
    
    log "SSL certificate configured successfully"
}

# Display deployment information
show_deployment_info() {
    log "Deployment completed successfully!"
    echo
    info "Application Information:"
    info "- Application Directory: $APP_DIR"
    info "- Domain: $DOMAIN"
    info "- API URL: http://$DOMAIN/api/"
    info "- Dashboard URL: http://$DOMAIN/"
    echo
    info "API Endpoints:"
    info "- Health: http://$DOMAIN/api/health"
    info "- Get Value: http://$DOMAIN/kgx-crypto-data/getValue?symbol=BTC"
    info "- Get PM: http://$DOMAIN/kgx-crypto-data/getPM"
    info "- Get BM: http://$DOMAIN/kgx-crypto-data/getBM"
    echo
    info "Management Commands:"
    info "- View logs: cd $APP_DIR/src && docker compose logs -f"
    info "- Restart services: sudo systemctl restart kgx-app"
    info "- Stop services: sudo systemctl stop kgx-app"
    info "- Start services: sudo systemctl start kgx-app"
    echo
    warn "Important: Please update the following in $APP_DIR/src/.env.local:"
    warn "- HADES_USER and HADES_PASSWORD with actual credentials"
    warn "- KDS2_API_KEY with actual API key"
    warn "- Any other external API keys as needed"
    echo
    info "After updating credentials, restart the application:"
    info "sudo systemctl restart kgx-app"
}

# Main deployment function
main() {
    log "Starting KGX Crypto Data Application deployment..."
    
    check_prerequisites
    setup_repository
    generate_secrets
    create_environment
    update_docker_compose
    configure_nginx
    deploy_application
    create_systemd_service
    verify_deployment
    setup_ssl
    show_deployment_info
}

# Handle command line arguments
case "${1:-deploy}" in
    "deploy")
        main
        ;;
    "update")
        log "Updating application..."
        setup_repository
        cd "$APP_DIR/src"
        docker compose -f docker-compose.yml -f docker-compose.prod.yml build
        docker compose -f docker-compose.yml -f docker-compose.prod.yml up -d
        verify_deployment
        log "Update completed"
        ;;
    "restart")
        log "Restarting application..."
        sudo systemctl restart kgx-app
        log "Application restarted"
        ;;
    "stop")
        log "Stopping application..."
        sudo systemctl stop kgx-app
        log "Application stopped"
        ;;
    "start")
        log "Starting application..."
        sudo systemctl start kgx-app
        log "Application started"
        ;;
    "logs")
        cd "$APP_DIR/src"
        docker compose -f docker-compose.yml -f docker-compose.prod.yml logs -f
        ;;
    "status")
        cd "$APP_DIR/src"
        docker compose -f docker-compose.yml -f docker-compose.prod.yml ps
        ;;
    *)
        echo "Usage: $0 {deploy|update|restart|stop|start|logs|status}"
        exit 1
        ;;
esac 