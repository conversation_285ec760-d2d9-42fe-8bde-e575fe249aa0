# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root to install SQLite build tools and copy files
USER root

# Install build tools needed for SQLite rebuild in entrypoint (minimal install)
RUN apk add --no-cache --virtual .sqlite-deps python3 make g++

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/collector ./apps/collector/

# Copy entrypoint script with proper ownership
COPY --chown=nextjs:nodejs apps/collector/entrypoint.dev.sh /entrypoint.dev.sh
RUN chmod +x /entrypoint.dev.sh

# Set service-specific environment variables
ENV PORT=3002

# Expose port
EXPOSE 3002

# Use entrypoint to rebuild native dependencies after volume mounts
ENTRYPOINT ["/entrypoint.dev.sh"]

# Development command with hot reload
CMD ["nodemon", "--watch", "apps/collector", "--watch", "libs", "--ext", "ts,js,json", "--exec", "ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/collector/src/main.ts"] 