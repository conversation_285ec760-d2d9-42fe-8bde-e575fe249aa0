---
description: This rule provides comprehensive best practices for Scrapy development, including code organization, performance, security, testing, and common pitfalls to avoid. It aims to guide developers in building robust, efficient, and maintainable web scraping applications with Scrapy.
globs: **/*.py
---
# Scrapy Best Practices

This document outlines the recommended best practices for developing Scrapy web scraping applications. Following these guidelines will help you create robust, efficient, secure, and maintainable scrapers.

## 1. Code Organization and Structure

### 1.1. Directory Structure

-   **Project Root:** Contains `scrapy.cfg`, project directory, and any `README.md`, `LICENSE`, or other project-level files.
-   **Project Directory (e.g., `my_project`):**
    -   `__init__.py`:  Marks the directory as a Python package.
    -   `items.py`: Defines the data structures (Scrapy Items) for the scraped data.
    -   `middlewares.py`:  Contains the Scrapy middleware components, used for request/response processing.
    -   `pipelines.py`:  Defines the data processing pipelines, used for cleaning, validating, and storing the scraped data.
    -   `settings.py`:  Configures the Scrapy project, including settings for pipelines, middleware, concurrency, etc.
    -   `spiders/`:
        -   `__init__.py`:  Marks the directory as a Python package.
        -   `my_spider.py`: Contains the spider definitions (Scrapy Spiders) responsible for crawling and scraping data.

Example:


my_project/
├── scrapy.cfg
├── my_project/
│   ├── __init__.py
│   ├── items.py
│   ├── middlewares.py
│   ├── pipelines.py
│   ├── settings.py
│   └── spiders/
│       ├── __init__.py
│       └── my_spider.py
└── README.md


### 1.2. File Naming Conventions

-   **Spider Files:** `spider_name.py` (e.g., `product_spider.py`, `news_spider.py`)
-   **Item Files:** `items.py` (standard naming)
-   **Middleware Files:** `middlewares.py` (standard naming)
-   **Pipeline Files:** `pipelines.py` (standard naming)
-   **Settings Files:** `settings.py` (standard naming)

### 1.3. Module Organization

-   **Small Projects:** All spiders can reside in the `spiders/` directory.
-   **Large Projects:** Consider organizing spiders into subdirectories based on the target website or data type (e.g., `spiders/news/`, `spiders/ecommerce/`).
-   **Custom Modules:** Create custom modules (e.g., `utils/`, `lib/`) for reusable code, helper functions, and custom classes.

### 1.4. Component Architecture

-   **Spiders:** Focus on crawling and extracting raw data.
-   **Items:** Define the structure of the scraped data.
-   **Pipelines:** Handle data cleaning, validation, transformation, and storage.
-   **Middleware:** Manage request/response processing, error handling, and proxy management.

### 1.5. Code Splitting

-   **Separate Concerns:**  Keep spiders lean and focused on crawling logic. Move data processing and storage to pipelines.
-   **Reusable Components:**  Extract common functionality (e.g., custom item loaders, helper functions) into separate modules or classes.
-   **Configuration:**  Use `settings.py` to manage project-wide configuration and avoid hardcoding values in spiders.
-   **Modular Middleware:** Create small, focused middleware components for specific tasks (e.g., user agent rotation, request retries).

## 2. Common Patterns and Anti-patterns

### 2.1. Design Patterns

-   **Strategy Pattern:** Use different parsing strategies within a spider based on the page structure or data type.
-   **Factory Pattern:** Create a factory function to instantiate items with default values or based on specific criteria.
-   **Singleton Pattern:** (Use sparingly) For global resources like database connections, consider using a singleton pattern, but ensure thread safety.
-   **Observer Pattern:** Use Scrapy signals to trigger actions based on specific events (e.g., item scraped, spider closed).

### 2.2. Recommended Approaches

-   **Item Loaders:** Use Item Loaders to populate items with extracted data, providing data validation and cleaning.
-   **CSS and XPath Selectors:**  Master CSS and XPath selectors for efficient data extraction.
-   **Response Objects:**  Utilize the methods provided by the `response` object (e.g., `css()`, `xpath()`, `urljoin()`, `follow()`).
-   **Asynchronous Operations:**  Understand Scrapy's asynchronous nature and use deferreds for handling asynchronous tasks (though Scrapy abstracts a lot of this away).
-   **Robots.txt:** Respect the robots.txt file and configure `ROBOTSTXT_OBEY` accordingly.
-   **Settings:** Centralize settings in `settings.py` instead of hardcoding values.

### 2.3. Anti-patterns and Code Smells

-   **Hardcoded Values:** Avoid hardcoding URLs, selectors, or other configuration values directly in the code.
-   **Overly Complex Spiders:** Keep spiders focused on crawling.  Move complex data processing to pipelines.
-   **Ignoring Errors:**  Implement proper error handling and logging to identify and address issues.
-   **Excessive Logging:**  Avoid verbose logging that can impact performance. Use appropriate log levels (DEBUG, INFO, WARNING, ERROR).
-   **Blocking Operations:** Avoid blocking operations (e.g., synchronous network requests) in spiders, as they can significantly reduce performance. Let Scrapy handle the concurrency.
-   **Unnecessary Recursion:** Overly complex recursion within `parse` methods can lead to stack overflow errors.
-   **Abusing Global State:** Avoid overly relying on global variables, since Scrapy manages concurrency it can lead to race conditions or unpredictable spider behavior.

### 2.4. State Management

-   **Spider Attributes:** Store spider-specific state in spider attributes (e.g., current page number, filters).
-   **Request.meta:** Use `Request.meta` to pass data between callbacks (e.g., passing data extracted from one page to the next).
-   **Settings:** Use Scrapy settings to manage project-level configuration.
-   **External Databases/Caches:** For persistent state or data sharing between spiders, consider using an external database or cache (e.g., Redis).

### 2.5. Error Handling

-   **`try...except` Blocks:** Use `try...except` blocks to handle potential exceptions during data extraction or processing.
-   **Scrapy Logging:** Utilize Scrapy's logging system to record errors, warnings, and informational messages.
-   **Retry Middleware:** Configure the Retry Middleware to automatically retry failed requests.
-   **Error Handling in Pipelines:** Implement error handling in pipelines to catch and log errors during data processing.
-   **Error Handling in Middlewares:** Implement error handling in middleware to deal with request or response issues

## 3. Performance Considerations

### 3.1. Optimization Techniques

-   **Concurrency:** Adjust `CONCURRENT_REQUESTS`, `CONCURRENT_REQUESTS_PER_DOMAIN`, and `DOWNLOAD_DELAY` to optimize crawling speed while avoiding server overload.
-   **Item Pipelines:** Optimize item pipelines for efficient data processing and storage.
-   **Caching:** Use HTTP caching to avoid re-downloading unchanged pages (`HTTPCACHE_ENABLED = True`).
-   **Offsite Middleware:** Enable the Offsite Spider Middleware to prevent crawling outside the allowed domains.
-   **Keep-Alive:** Ensure HTTP keep-alive is enabled for persistent connections.
-   **DNS Caching:** Optimize DNS resolution by enabling DNS caching.
-   **Efficient Selectors:** Optimize XPath and CSS selectors for faster data extraction.
-   **Asynchronous Request Handling:** Scrapy's asynchronous architecture handles concurrency, utilize it by avoid blocking operations

### 3.2. Memory Management

-   **Large Datasets:** For very large datasets, consider using Scrapy's built-in support for chunked responses or processing data in batches.
-   **Avoid Storing Everything in Memory:** Process items in pipelines and avoid storing the entire scraped data in memory at once.
-   **Limit Response Body Size:** Set `DOWNLOAD_MAXSIZE` to limit the maximum size of downloaded responses to prevent memory exhaustion.
-   **Garbage Collection:** Manually trigger garbage collection if necessary to reclaim memory (use with caution).

### 3.3. Rendering Optimization

-   **Splash/Selenium:** If JavaScript rendering is required, use Scrapy Splash or Selenium, but be aware of the performance overhead.
-   **Render Only When Necessary:**  Only render pages that require JavaScript execution. Avoid rendering static HTML pages.
-   **Minimize Browser Interactions:**  Reduce the number of interactions with the browser (e.g., clicks, form submissions) to improve rendering performance.
-   **Caching Rendered Results:** Cache rendered HTML to avoid redundant rendering.

### 3.4. Bundle Size Optimization

- Scrapy does not have bundles like web development frameworks, so this section does not apply.

### 3.5. Lazy Loading

-   **Pagination:** Implement pagination to crawl websites in smaller chunks.
-   **Lazy Item Processing:** Defer item processing in pipelines until necessary to reduce memory consumption.

## 4. Security Best Practices

### 4.1. Common Vulnerabilities and Prevention

-   **Cross-Site Scripting (XSS):** Sanitize scraped data before displaying it on a website to prevent XSS attacks. Don't output raw scraped content.
-   **SQL Injection:** If storing scraped data in a database, use parameterized queries or ORMs to prevent SQL injection vulnerabilities.
-   **Command Injection:** Avoid executing arbitrary commands based on scraped data to prevent command injection attacks.
-   **Denial of Service (DoS):** Implement rate limiting and politeness policies to avoid overwhelming target websites.
-   **Data Poisoning:** Validate scraped data to ensure its integrity and prevent data poisoning.

### 4.2. Input Validation

-   **Validate Scraped Data:**  Implement validation logic in item pipelines to ensure that scraped data conforms to expected formats and ranges.
-   **Data Type Validation:**  Check the data type of scraped values to prevent unexpected errors.
-   **Regular Expressions:**  Use regular expressions to validate string values and enforce specific patterns.

### 4.3. Authentication and Authorization

-   **HTTP Authentication:** Use Scrapy's built-in support for HTTP authentication to access password-protected websites.
-   **Cookies:**  Manage cookies properly to maintain sessions and avoid authentication issues.
-   **API Keys:**  Store API keys securely and avoid exposing them in the code.
-   **OAuth:**  Implement OAuth authentication if required to access protected resources.

### 4.4. Data Protection

-   **Encryption:**  Encrypt sensitive data during storage and transmission.
-   **Anonymization:**  Anonymize or pseudonymize personal data to protect user privacy.
-   **Access Control:**  Implement access control mechanisms to restrict access to sensitive data.

### 4.5. Secure API Communication

-   **HTTPS:**  Always use HTTPS for secure communication with APIs.
-   **SSL/TLS:**  Ensure that SSL/TLS certificates are properly configured.
-   **API Authentication:**  Use API keys or tokens for authentication.
-   **Rate Limiting:**  Implement rate limiting to prevent abuse and protect APIs.

## 5. Testing Approaches

### 5.1. Unit Testing

-   **Spider Logic:** Unit test individual components of spiders, such as selector logic, data extraction, and URL generation.
-   **Item Pipelines:** Unit test item pipelines to verify data cleaning, validation, and transformation.
-   **Middleware:** Unit test middleware components to ensure proper request/response processing.
-   **Helper Functions:** Unit test helper functions to ensure correct behavior.

### 5.2. Integration Testing

-   **Spider Integration:** Integrate spiders with item pipelines to test the entire data flow.
-   **Middleware Integration:** Integrate middleware components with spiders to test request/response handling.
-   **External Services:** Integrate with external services (e.g., databases, APIs) to test data storage and retrieval.

### 5.3. End-to-End Testing

-   **Full Crawl:** Run a full crawl of the target website and verify that all data is extracted correctly.
-   **Data Validation:** Validate the scraped data against expected values or schemas.
-   **Performance Testing:** Measure the performance of the scraper and identify potential bottlenecks.

### 5.4. Test Organization

-   **Separate Test Directory:** Create a separate `tests/` directory to store test files.
-   **Test Modules:** Organize tests into modules based on the component being tested (e.g., `tests/test_spiders.py`, `tests/test_pipelines.py`).
-   **Test Fixtures:** Use test fixtures to set up test data and dependencies.

### 5.5. Mocking and Stubbing

-   **Mock Responses:** Mock HTTP responses to test spider logic without making actual network requests.
-   **Stub External Services:** Stub external services (e.g., databases, APIs) to isolate components during testing.
-   **Mock Item Pipelines:** Mock item pipelines to prevent actual data storage during testing.

## 6. Common Pitfalls and Gotchas

### 6.1. Frequent Mistakes

-   **Incorrect Selectors:**  Using incorrect or brittle CSS/XPath selectors that break when the website structure changes.
-   **Ignoring Robots.txt:**  Ignoring the `robots.txt` file and potentially violating the website's terms of service.
-   **Overloading the Target Website:**  Making too many requests too quickly and potentially causing a denial of service.
-   **Not Handling JavaScript:**  Failing to handle JavaScript-rendered content.
-   **Not Handling Pagination:**  Failing to properly handle pagination and missing data from multiple pages.
-   **Incorrectly handling Cookies:** Failing to persist or handle cookies properly can cause unpredictable behavior.

### 6.2. Edge Cases

-   **Website Structure Changes:**  Websites frequently change their structure, breaking existing scrapers. Implement robust selectors and monitoring to detect changes.
-   **Anti-Scraping Measures:**  Websites may implement anti-scraping measures (e.g., CAPTCHAs, IP blocking) to prevent scraping. Use appropriate techniques to bypass these measures.
-   **Dynamic Content:**  Websites may use dynamic content (e.g., AJAX, WebSockets) to load data. Use appropriate techniques to handle dynamic content.
-   **Rate Limiting:**  Websites may implement rate limiting to restrict the number of requests from a single IP address. Use proxies or distributed crawling to overcome rate limits.

### 6.3. Version-Specific Issues

-   **Scrapy API Changes:**  Be aware of API changes between Scrapy versions and update your code accordingly.
-   **Dependency Conflicts:**  Manage dependencies carefully to avoid conflicts between Scrapy and other libraries.
-   **Python Version Compatibility:**  Ensure that your code is compatible with the supported Python versions.

### 6.4. Compatibility Concerns

-   **JavaScript Rendering Libraries:** Ensure that the JavaScript rendering library is compatible with the target website and Scrapy.
-   **Data Storage Libraries:**  Ensure that the data storage library is compatible with Scrapy and the chosen data format.
-   **Proxy Management Libraries:** Ensure that the proxy management library is compatible with Scrapy.

### 6.5. Debugging Strategies

-   **Scrapy Shell:** Use the Scrapy shell to test selectors and extract data interactively.
-   **Logging:**  Use Scrapy's logging system to record errors, warnings, and informational messages.
-   **Debugging Tools:** Use Python debugging tools (e.g., `pdb`, `ipdb`) to step through the code and inspect variables.
-   **Middleware Debugging:** Use middleware to inspect requests and responses and identify potential issues.
-   **Verbose Output:** Use `-v` or `-vv` when running spiders to get more verbose output.

## 7. Tooling and Environment

### 7.1. Recommended Tools

-   **IDE:**  Use a capable IDE such as VS Code, PyCharm, or Sublime Text.
-   **Virtual Environment:**  Use virtual environments (e.g., `venv`, `conda`) to manage dependencies and isolate projects.
-   **Scrapy Shell:** Use the Scrapy shell for interactive testing and debugging.
-   **Browser Developer Tools:** Use browser developer tools to inspect website structure and identify selectors.

### 7.2. Build Configuration

-   **`setup.py`:** Use a `setup.py` file to define project dependencies and metadata for larger projects and for distribution.
-   **`requirements.txt`:** Use a `requirements.txt` file to list project dependencies for easy installation.
-   **`pip freeze`:** Use `pip freeze > requirements.txt` to generate a list of installed packages and their versions.

### 7.3. Linting and Formatting

-   **PEP 8:**  Follow PEP 8 style guidelines for code readability.
-   **Linters:**  Use linters (e.g., `flake8`, `pylint`) to identify code style issues and potential errors.
-   **Formatters:**  Use code formatters (e.g., `black`, `autopep8`) to automatically format code according to PEP 8.

### 7.4. Deployment

-   **Scrapyd:**  Use Scrapyd to deploy and manage Scrapy spiders on a server.
-   **Docker:**  Use Docker to containerize Scrapy applications for easy deployment and scalability.
-   **Cloud Platforms:**  Deploy Scrapy applications on cloud platforms such as AWS, Google Cloud, or Azure.
-   **Scheduled Tasks:**  Use scheduled tasks (e.g., cron jobs) to run Scrapy spiders on a regular basis.

### 7.5. CI/CD Integration

-   **Testing:**  Integrate unit and integration tests into the CI/CD pipeline to ensure code quality.
-   **Linting and Formatting:**  Integrate linters and formatters into the CI/CD pipeline to enforce code style.
-   **Automated Deployment:**  Automate the deployment process to deploy new versions of the scraper automatically.
-   **Monitoring:**  Integrate monitoring tools to track the performance and health of the scraper in production.