import { Injectable, Logger, Inject } from "@nestjs/common";
import { HttpService } from "@nestjs/axios";
import { firstValueFrom } from "rxjs";
import { v4 as uuidv4 } from "uuid";
import {
  CollectionCollector,
  CollectorCollectionResult,
} from "@data-pipeline/storage";
import {
  CollectionStatus,
  getErrorMessage,
  getErrorStack,
  generateHash,
  generateCollectionHash,
  AuthType,
} from "@data-pipeline/core";
import { CollectorPlugin } from "../collector-plugin.interface";
import { Collector } from "../../collectors/collector.interface";
import {
  DataTypeHandler,
  DataProcessingContext,
  KgxDataType,
} from "./interfaces/data-type-handler.interface";
import {
  KgxFeatureFlags,
  DEFAULT_KGX_FEATURE_FLAGS,
  AdditionalCollectionConfig,
} from "./interfaces/kgx-feature-flags.interface";
import { CryptoDataHandler } from "./handlers/crypto-data.handler";
import { BaseMetalsDataHandler } from "./handlers/base-metals-data.handler";
import { PreciousMetalsDataHandler } from "./handlers/precious-metals-data.handler";
import { EnergyDataHandler } from "./handlers/energy-data.handler";
import { ForexDataHandler } from "./handlers/forex-data.handler";
import { MarketIndicesDataHandler } from "./handlers/market-indices-data.handler";

// First, define an interface for our pattern objects
interface DataTypePattern {
  tags: string[];
  url: string[];
  name: string[];
  exclude?: string[];  // Make exclude optional
}

/**
 * Unified KGX Plugin that acts as a complete collector
 * Handles the entire collection process from HTTP request to data enrichment
 * Supports multi-dataset collection with configurable feature flags
 */
@Injectable()
export class KgxPlugin implements CollectorPlugin, Collector {
  private readonly logger = new Logger(KgxPlugin.name);
  private readonly handlers = new Map<KgxDataType, DataTypeHandler>();
  private readonly featureFlags: KgxFeatureFlags;

  // API endpoint configurations for additional collections
  private readonly apiConfigs = {
    [KgxDataType.CRYPTO]: {
      url: "https://kdb-api.prod.kitco.com/api/crypto-rates",
      queryParams: {
        crypto: "BTC,ETH,SOL,ADA,AVAX,LINK,TRX,XRP,BNB,SUI",
        money: "USD",
        limit: "1",
        compareToTime: "24h",
      },
    },
    [KgxDataType.PRECIOUS_METALS]: {
      url: "https://kds2.kitco.com/getpM",
      queryParams: {
        apikey: "9bnteWVi2kT13528d100c608fn0TlbC6",
        symbol: "AU,AG,PT,PD",
        currency: "USD",
        unit: "ounce",
        df: "2",
        tf: "2",
        type: "json",
      },
    },
    [KgxDataType.BASE_METALS]: {
      url: "https://kdb-api.prod.kitco.com/api/base-metals",
      queryParams: {
        metals: "CU,AL,ZN,NI",
        currency: "USD",
        exchange: "LME",
      },
    },
    [KgxDataType.ENERGY]: {
      url: "https://kdb-api.prod.kitco.com/api/energy-commodities",
      queryParams: {
        commodities: "CL,BZ,NG",
        currency: "USD",
        exchange: "NYMEX",
      },
    },
    [KgxDataType.FOREX]: {
      url: "https://kds2.kitco.com/getUSD",
      queryParams: {
        apikey: "9bnteWVi2kT13528d100c608fn0TlbC6",
        ver: "2.0",
        symbol: "AUD,BRL,CAD,CNY,EUR,MXN,INR,JPY,RUB,ZAR,CHF,HKD,GBP",
        type: "json",
      },
    },
    [KgxDataType.MARKET_INDICES]: {
      url: "https://kds2.kitco.com/getValue",
      queryParams: {
        apikey: "9bnteWVi2kT13528d100c608fn0TlbC6",
        type: "json",
        df: "1",
        tf: "1",
        symbol: "USDX",
        tz: "America/New_York",
        kgx: "no",
      },
    },
  };

  /**
 * Data type patterns mapped to their corresponding enum values
 */
  private readonly dataTypePatterns: Record<KgxDataType, DataTypePattern> = {
    [KgxDataType.CRYPTO]: {
      tags: ['crypto'],
      url: ['crypto'],
      name: ['crypto']
    },
    [KgxDataType.BASE_METALS]: {
      tags: ['base_metals'],
      url: ['base-metals', 'metals'],
      name: ['metal'],
      exclude: ['precious'] // Exclude precious metals from base metals
    },
    [KgxDataType.PRECIOUS_METALS]: {
      tags: ['precious_metals'],
      url: ['precious'],
      name: ['precious']
    },
    [KgxDataType.ENERGY]: {
      tags: ['energy'],
      url: ['energy', 'oil', 'gas'],
      name: ['energy', 'oil', 'gas']
    },
    [KgxDataType.FOREX]: {
      tags: ['forex'],
      url: ['getUSD', 'forex'],
      name: ['forex', 'currency']
    },
    [KgxDataType.MARKET_INDICES]: {
      tags: ['market_indices', 'indices'],
      url: ['getValue', 'indices'],
      name: ['index', 'indices', 'market']
    }
  } as const;

  constructor(
    @Inject(HttpService) private readonly httpService: HttpService,
    private readonly cryptoHandler: CryptoDataHandler,
    private readonly baseMetalsHandler: BaseMetalsDataHandler,
    private readonly preciousMetalsHandler: PreciousMetalsDataHandler,
    private readonly energyHandler: EnergyDataHandler,
    private readonly forexHandler: ForexDataHandler,
    private readonly marketIndicesHandler: MarketIndicesDataHandler
  ) {
    this.featureFlags = { ...DEFAULT_KGX_FEATURE_FLAGS };
    this.initializeHandlers();
  }

  /**
   * Initialize all data type handlers
   */
  private initializeHandlers(): void {
    this.handlers.set(KgxDataType.BASE_METALS, this.baseMetalsHandler);
    this.handlers.set(KgxDataType.PRECIOUS_METALS, this.preciousMetalsHandler);
    this.handlers.set(KgxDataType.ENERGY, this.energyHandler);
    this.handlers.set(KgxDataType.CRYPTO, this.cryptoHandler);
    this.handlers.set(KgxDataType.FOREX, this.forexHandler);
    this.handlers.set(KgxDataType.MARKET_INDICES, this.marketIndicesHandler);

    this.logger.log(
      `Initialized ${this.handlers.size} data type handlers with feature flags support`
    );
  }

  /**
   * Determine what additional data types to collect based on feature flags and primary type
   */
  private getAdditionalCollections(
    primaryDataType: KgxDataType
  ): AdditionalCollectionConfig[] {
    const additionalCollections: AdditionalCollectionConfig[] = [];

    // Auto-collect base metals with precious metals
    if (
      primaryDataType === KgxDataType.PRECIOUS_METALS &&
      this.featureFlags.autoCollectBaseMetalsWithPrecious
    ) {
      additionalCollections.push({
        dataType: KgxDataType.BASE_METALS,
        priority: 2,
        symbols: ["CU", "AL"], // Industrial metals for broader market context
      });
    }

    // Auto-collect precious metals with crypto
    if (
      primaryDataType === KgxDataType.CRYPTO &&
      this.featureFlags.autoCollectPreciousMetalsWithCrypto
    ) {
      additionalCollections.push({
        dataType: KgxDataType.PRECIOUS_METALS,
        priority: 1,
        symbols: ["AU", "AG"], // Focus on gold and silver for crypto correlation
      });
    }

    // Auto-collect energy with any metals (inflation/commodity correlation)
    if (
      (primaryDataType === KgxDataType.PRECIOUS_METALS ||
        primaryDataType === KgxDataType.BASE_METALS) &&
      this.featureFlags.autoCollectEnergyWithMetals
    ) {
      additionalCollections.push({
        dataType: KgxDataType.ENERGY,
        priority: 3,
        symbols: ["CL"], // Crude oil as key inflation indicator
      });
    }

    // Auto-collect forex with crypto (USD strength analysis)
    if (
      primaryDataType === KgxDataType.CRYPTO &&
      this.featureFlags.autoCollectForexWithCrypto
    ) {
      additionalCollections.push({
        dataType: KgxDataType.FOREX,
        priority: 2,
        symbols: ["EUR", "JPY", "GBP", "CAD", "CHF"], // Major USD pairs for strength analysis
      });
    }

    // Auto-collect market indices with any data type (broad market context)
    if (this.featureFlags.autoCollectMarketIndicesWithAll) {
      additionalCollections.push({
        dataType: KgxDataType.MARKET_INDICES,
        priority: 4,
        symbols: ["USDX", "SPX", "DJI", "XAU"], // USD Index, major equity indices, gold index
      });
    }

    // Sort by priority and limit concurrent collections
    return additionalCollections
      .sort((a, b) => a.priority - b.priority)
      .slice(0, this.featureFlags.maxConcurrentCollections - 1); // -1 for primary collection
  }

  // /**
  //  * Determine data type from collector configuration
  //  */
  // private determineDataType(
  //   collector: CollectionCollector
  // ): KgxDataType | null {
  //   // Check tags first (most reliable)
  //   if (collector.tags) {
  //     if (collector.tags.includes("crypto")) return KgxDataType.CRYPTO;
  //     if (collector.tags.includes("base_metals")) return KgxDataType.BASE_METALS;
  //     if (collector.tags.includes("precious_metals")) return KgxDataType.PRECIOUS_METALS;
  //     if (collector.tags.includes("energy")) return KgxDataType.ENERGY;
  //     if (collector.tags.includes("forex")) return KgxDataType.FOREX;
  //     if (
  //       collector.tags.includes("market_indices") ||
  //       collector.tags.includes("indices")
  //     )
  //       return KgxDataType.MARKET_INDICES;
  //   }

  //   // Check URL patterns
  //   const config = collector.config as any;
  //   if (config.url) {
  //     if (config.url.includes("crypto")) return KgxDataType.CRYPTO;
  //     if (config.url.includes("base-metals") || config.url.includes("metals"))
  //       return KgxDataType.BASE_METALS;
  //     if (config.url.includes("precious")) return KgxDataType.PRECIOUS_METALS;
  //     if (
  //       config.url.includes("energy") ||
  //       config.url.includes("oil") ||
  //       config.url.includes("gas")
  //     )
  //       return KgxDataType.ENERGY;
  //     if (config.url.includes("getUSD") || config.url.includes("forex"))
  //       return KgxDataType.FOREX;
  //     if (config.url.includes("getValue") || config.url.includes("indices"))
  //       return KgxDataType.MARKET_INDICES;
  //   }

  //   // Check collector name patterns
  //   if (collector.name) {
  //     const nameLower = collector.name.toLowerCase();
  //     if (nameLower.includes("crypto")) return KgxDataType.CRYPTO;
  //     if (nameLower.includes("metal") && !nameLower.includes("precious"))
  //       return KgxDataType.BASE_METALS;
  //     if (nameLower.includes("precious")) return KgxDataType.PRECIOUS_METALS;
  //     if (
  //       nameLower.includes("energy") ||
  //       nameLower.includes("oil") ||
  //       nameLower.includes("gas")
  //     )
  //       return KgxDataType.ENERGY;
  //     if (nameLower.includes("forex") || nameLower.includes("currency"))
  //       return KgxDataType.FOREX;
  //     if (
  //       nameLower.includes("index") ||
  //       nameLower.includes("indices") ||
  //       nameLower.includes("market")
  //     )
  //       return KgxDataType.MARKET_INDICES;
  //   }

  //   return null;
  // }

  /**
   * Determine data type from collector configuration
   */
  private determineDataType(collector: CollectionCollector): KgxDataType | null {
    // Check tags first (most reliable)
    if (collector.tags) {
      for (const tag of collector.tags) {
        const dataType = this.matchPattern('tags', tag);
        if (dataType) return dataType;
      }
    }

    // Check URL patterns
    const config = collector.config as { url?: string };
    if (config?.url) {
      const dataType = this.matchPattern('url', config.url.toLowerCase());
      if (dataType) return dataType;
    }

    // Check collector name patterns
    if (collector.name) {
      const dataType = this.matchPattern('name', collector.name.toLowerCase());
      if (dataType) return dataType;
    }

    return null;
  }

  /**
   * Helper to match patterns against a value
   */
  private matchPattern(
    field: 'tags' | 'url' | 'name',
    value: string
  ): KgxDataType | null {
    for (const [dataType, patterns] of Object.entries(this.dataTypePatterns)) {
      const fieldPatterns = patterns[field] || [];
      const excludePatterns = patterns.exclude || [];

      // Skip if any exclude patterns match
      if (excludePatterns.some(pattern => value.includes(pattern))) {
        continue;
      }

      // Check if any include patterns match
      if (fieldPatterns.some(pattern => value.includes(pattern))) {
        return dataType as KgxDataType;
      }
    }
    return null;
  }

  /**
   * Get the appropriate handler for the data type
   */
  private getHandler(dataType: KgxDataType): DataTypeHandler | null {
    return this.handlers.get(dataType) || null;
  }

  // ===== CollectorPlugin Interface =====

  /**
   * Check if this plugin can handle the given collector
   */
  canHandle(collector: CollectionCollector): boolean {
    // Must be HTTP/HTTPS collector
    if (collector.type !== "https" && collector.type !== "http") {
      return false;
    }

    // Must have kgx tag or be identifiable as KGX data
    const hasKgxTag = collector.tags?.includes("kgx");
    const dataType = this.determineDataType(collector);

    if (!hasKgxTag && !dataType) {
      return false;
    }

    // Check if we have a handler for this data type
    if (dataType) {
      const handler = this.getHandler(dataType);
      return handler ? handler.canHandle(collector) : false;
    }

    // Fallback: check if any handler can handle this collector
    for (const handler of this.handlers.values()) {
      if (handler.canHandle(collector)) {
        return true;
      }
    }

    return false;
  }

  /**
   * Validate data - called after collection (legacy interface)
   */
  async validateData(
    result: CollectorCollectionResult,
    collector: CollectionCollector
  ): Promise<CollectorCollectionResult> {
    // This method is now deprecated since we handle everything in collect()
    this.logger.warn(
      "validateData called - this should not happen with the new architecture"
    );
    return result;
  }

  // ===== Collector Interface =====

  /**
   * Complete collection process: HTTP request + data processing + enrichment
   * Now supports multi-dataset collection based on feature flags
   */
  async collect(
    collector: CollectionCollector
  ): Promise<CollectorCollectionResult> {
    const startTime = Date.now();

    try {
      // Determine primary data type
      const primaryDataType = this.determineDataType(collector);
      if (!primaryDataType) {
        throw new Error("Unable to determine data type for KGX collector");
      }

      // Get appropriate handler
      const primaryHandler = this.getHandler(primaryDataType);
      if (!primaryHandler) {
        throw new Error(`No handler found for data type: ${primaryDataType}`);
      }

      // Determine what additional data to collect
      const additionalCollections =
        this.getAdditionalCollections(primaryDataType);

      if (additionalCollections.length > 0) {
        this.logger.log(
          `Collecting ${primaryDataType} data with ${primaryHandler.constructor.name} ` +
          `+ ${additionalCollections.length
          } additional datasets: ${additionalCollections
            .map((c) => c.dataType)
            .join(", ")}`
        );
      } else {
        this.logger.log(
          `Collecting ${primaryDataType} data with ${primaryHandler.constructor.name}`
        );
      }

      // Collect primary data and additional data concurrently
      const collectionPromises = [
        this.collectSingleDataType(collector, primaryDataType, primaryHandler),
      ];

      // Add additional collection promises if enabled
      if (
        this.featureFlags.unifiedDatasetOutput &&
        additionalCollections.length > 0
      ) {
        for (const additionalConfig of additionalCollections) {
          const additionalHandler = this.getHandler(
            additionalConfig.dataType as KgxDataType
          );
          if (additionalHandler) {
            collectionPromises.push(
              this.collectAdditionalDataType(
                additionalConfig,
                additionalHandler
              )
            );
          }
        }
      }

      // Execute all collections with timeout
      const collectionResults = await Promise.allSettled(
        collectionPromises.map((promise) =>
          Promise.race([
            promise,
            new Promise((_, reject) =>
              setTimeout(
                () => reject(new Error("Collection timeout")),
                this.featureFlags.additionalCollectionTimeout
              )
            ),
          ])
        )
      );

      // Process results
      const primaryResult = collectionResults[0];
      const additionalResults = collectionResults.slice(1);

      if (primaryResult.status === "rejected") {
        throw primaryResult.reason;
      }

      const primaryData = primaryResult.value;

      // Combine datasets if enabled and additional data is available
      if (
        this.featureFlags.unifiedDatasetOutput &&
        additionalResults.length > 0
      ) {
        const additionalDatasets = additionalResults
          .filter((result) => result.status === "fulfilled")
          .map((result) => (result as PromiseFulfilledResult<any>).value)
          .filter((dataset) => dataset !== null);

        if (additionalDatasets.length > 0) {
          return this.createUnifiedResult(
            collector,
            primaryData,
            additionalDatasets,
            primaryDataType,
            primaryHandler,
            startTime
          );
        }
      }

      // Return primary data only
      return primaryData;
    } catch (error) {
      return this.createErrorResult(collector, error, startTime);
    }
  }

  /**
   * Collect data for a single data type
   */
  private async collectSingleDataType(
    collector: CollectionCollector,
    dataType: KgxDataType,
    handler: DataTypeHandler
  ): Promise<any> {
    // Step 1: Make HTTP request
    const rawData = await this.makeHttpRequest(collector);

    // Step 2: Create processing context
    const context: DataProcessingContext = {
      collectorId: collector.id,
      dataType,
      timestamp: new Date(),
      metadata: {
        collectorName: collector.name,
        collectorType: collector.type,
      },
    };

    // Step 3: Validate raw data
    const validation = await handler.validateData(rawData, context);

    // Step 4: Transform data
    const transformedData = await handler.transformData(rawData, context);

    // Step 5: Enrich data (simplified - no longer triggers additional collections)
    const enrichmentData = await handler.enrichData(transformedData, context);

    return {
      dataType,
      rawData,
      transformedData,
      enrichmentData,
      validation,
      handler: handler.constructor.name,
      context,
    };
  }

  /**
   * Collect additional data type using API configs
   */
  private async collectAdditionalDataType(
    config: AdditionalCollectionConfig,
    handler: DataTypeHandler
  ): Promise<any> {
    try {
      const dataType = config.dataType as KgxDataType;
      const apiConfig = this.apiConfigs[dataType];

      if (!apiConfig) {
        throw new Error(`No API configuration found for ${dataType}`);
      }

      // Create temporary collector config for additional data
      const tempCollector: CollectionCollector = {
        id: `temp_${dataType}_${Date.now()}`,
        name: `Auto-triggered ${dataType} collector`,
        type: "https" as any,
        enabled: true,
        created_at: new Date(),
        updated_at: new Date(),
        config: {
          url: apiConfig.url,
          method: "GET",
          headers: { Accept: "application/json" },
          timeout: 15000,
          queryParams: {
            ...apiConfig.queryParams,
            ...(config.symbols && { symbols: config.symbols.join(",") }),
          },
        },
      };

      return await this.collectSingleDataType(tempCollector, dataType, handler);
    } catch (error) {
      this.logger.warn(
        `Failed to collect additional ${config.dataType
        } data: ${getErrorMessage(error)}`
      );

      if (!this.featureFlags.gracefulDegradation) {
        throw error;
      }

      return null;
    }
  }

  /**
   * Create unified result combining multiple datasets
   */
  private createUnifiedResult(
    collector: CollectionCollector,
    primaryData: any,
    additionalDatasets: any[],
    primaryDataType: KgxDataType,
    primaryHandler: DataTypeHandler,
    startTime: number
  ): CollectorCollectionResult {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const timestamp = new Date();

    // Combine all raw data
    const combinedRawData = {
      primary: {
        dataType: primaryDataType,
        data: primaryData.rawData,
      },
      additional: additionalDatasets
        .filter((dataset) => dataset && dataset.dataType)
        .map((dataset) => ({
          dataType: dataset.dataType,
          data: dataset.rawData,
        })),
      collection_metadata: {
        feature_flags: this.featureFlags,
        total_datasets: 1 + additionalDatasets.length,
        collection_strategy: "unified_multi_dataset",
      },
    };

    // Combine all transformations
    const combinedTransformations = {
      [primaryDataType]: primaryData.transformedData,
      ...additionalDatasets
        .filter((dataset) => dataset && dataset.dataType)
        .reduce((acc, dataset) => {
          acc[dataset.dataType] = dataset.transformedData;
          return acc;
        }, {} as Record<string, any>),
    };

    // // Combine all enrichments
    // const combinedEnrichments = {
    //   [primaryDataType]: primaryData.enrichmentData,
    //   ...additionalDatasets
    //     .filter((dataset) => dataset && dataset.dataType)
    //     .reduce((acc, dataset) => {
    //       acc[dataset.dataType] = dataset.enrichmentData;
    //       return acc;
    //     }, {} as Record<string, any>),
    //   correlation_data: {
    //     cross_dataset_correlations: this.calculateCrossDatasetCorrelations(
    //       primaryData,
    //       additionalDatasets
    //     ),
    //     market_context: this.generateMarketContext(
    //       primaryDataType,
    //       additionalDatasets
    //     ),
    //   },
    // };

    // Calculate combined hash
    const dataHash = generateHash(combinedRawData, "md5");
    const collectionHash = generateCollectionHash(
      collector.id,
      collector.config,
      combinedRawData,
      timestamp
    );

    return {
      id: uuidv4(),
      collector_id: collector.id,
      status: CollectionStatus.SUCCESS,
      data: {
        ...combinedRawData,
        metadata: {
          ...combinedRawData.collection_metadata,
          transformations: combinedTransformations,
          // enrichments: combinedEnrichments,
        },
      },
      data_hash: collectionHash,
      metadata: {
        timestamp,
        duration_ms: duration,
        size_bytes: JSON.stringify(combinedRawData).length,
        data_type: `kgx_unified_${primaryDataType}`,
        original_format: `kgx_unified_api_v1`,
        record_count: Object.values(combinedTransformations).reduce(
          (total: number, data: any) =>
            total + (Array.isArray(data) ? data.length : 1),
          0
        ),
        validation_timestamp: timestamp.toISOString(),
        processing_time_ms: duration,
        requires_processing: true,
        processor_type: "KGX_UNIFIED",
        kgx_data_type: primaryDataType,
        handler_used: primaryHandler.constructor.name,
        additional_handlers: additionalDatasets
          .filter((d) => d && d.handler)
          .map((d) => d.handler),
        feature_flags_used: this.featureFlags,
        datasets_collected: [
          primaryDataType,
          ...additionalDatasets
            .filter((d) => d && d.dataType)
            .map((d) => d.dataType),
        ],
        dataHash,
        request: {
          url: (collector.config as any).url,
          method: (collector.config as any).method,
          headers: { ...(collector.config as any).headers },
        },
      },
      created_at: timestamp,
    };
  }

  // /**
  //  * Calculate cross-dataset correlations for market analysis
  //  */
  // private calculateCrossDatasetCorrelations(
  //   primaryData: any,
  //   additionalDatasets: any[]
  // ): any {
  //   const correlations: any = {};

  //   // Add correlation logic here based on data types
  //   // For now, return placeholder structure
  //   return {
  //     crypto_precious_metals:
  //       primaryData.dataType === KgxDataType.CRYPTO
  //         ? {
  //           gold_correlation_notes:
  //             "Crypto often moves inverse to gold during market uncertainty",
  //           volatility_comparison:
  //             "Crypto volatility typically 3-5x higher than gold",
  //         }
  //         : null,
  //     metals_energy:
  //       "Industrial metals often correlate with energy prices due to production costs",
  //     timestamp: new Date().toISOString(),
  //   };
  // }

  // /**
  //  * Generate market context based on collected datasets
  //  */
  // private generateMarketContext(
  //   primaryDataType: KgxDataType,
  //   additionalDatasets: any[]
  // ): any {
  //   return {
  //     primary_market: primaryDataType,
  //     related_markets: additionalDatasets
  //       .filter((d) => d && d.dataType)
  //       .map((d) => d.dataType),
  //     market_session: this.getCurrentMarketSession(),
  //     context_notes: `Multi-dataset collection provides broader market context for ${primaryDataType} analysis`,
  //     timestamp: new Date().toISOString(),
  //   };
  // }

  // /**
  //  * Get current market session info
  //  */
  // private getCurrentMarketSession(): any {
  //   const now = new Date();
  //   const isWeekend = now.getDay() === 0 || now.getDay() === 6;

  //   return {
  //     is_weekend: isWeekend,
  //     current_time: now.toISOString(),
  //     timezone: "UTC",
  //     crypto_market_status: "OPEN", // Always open
  //     traditional_market_status: isWeekend ? "CLOSED" : "OPEN",
  //   };
  // }

  /**
   * Validate collector configuration
   */
  validateConfig(collector: CollectionCollector): boolean {
    if (!this.canHandle(collector)) {
      return false;
    }

    const config = collector.config as any;

    // Basic HTTP configuration validation
    if (!config.url) {
      this.logger.warn("Missing URL in collector configuration");
      return false;
    }

    if (!config.method) {
      this.logger.warn("Missing HTTP method in collector configuration");
      return false;
    }

    return true;
  }

  // ===== Private Helper Methods =====

  /**
   * Make HTTP request to collect raw data
   */
  private async makeHttpRequest(collector: CollectionCollector): Promise<any> {
    const config = collector.config as any;

    try {
      const headers = { ...config.headers };

      // Apply authentication if configured
      if (collector.auth) {
        this.applyAuthentication(headers, collector);
      }

      // Add cache-busting parameter
      const queryParams = {
        ...config.queryParams,
        _cb: Date.now().toString(),
      };

      this.logger.debug(
        `Making KGX HTTP request to ${config.url} with cache-busting parameter\n${JSON.stringify(queryParams)}`
      );

      const response = await firstValueFrom(
        this.httpService.request({
          url: config.url,
          method: config.method,
          headers,
          params: queryParams,
          data: config.body,
          timeout: config.timeout || 30000,
          maxRedirects: 5,
        })
      );

      this.logger.debug(`Successfully collected raw data from ${config.url} with response: \n${JSON.stringify(response.data)}`);

      return response.data;
    } catch (error) {
      this.logger.error(
        `Error making HTTP request to ${config.url}: ${getErrorMessage(error)}`,
        getErrorStack(error as Error)
      );
      throw error;
    }
  }

  /**
   * Apply authentication to headers
   */
  private applyAuthentication(
    headers: Record<string, string>,
    collector: CollectionCollector
  ): void {
    if (!collector.auth) return;

    switch (collector.auth.type) {
      case AuthType.API_KEY:
        if (collector.auth.apiKey && collector.auth.apiKey.in === "header") {
          headers[collector.auth.apiKey.key] = collector.auth.apiKey.value;
        }
        break;

      case AuthType.BASIC:
        if (collector.auth.basic) {
          const credentials = Buffer.from(
            `${collector.auth.basic.username}:${collector.auth.basic.password}`
          ).toString("base64");
          headers["Authorization"] = `Basic ${credentials}`;
        }
        break;

      case AuthType.BEARER:
        if (collector.auth.bearer) {
          headers["Authorization"] = `Bearer ${collector.auth.bearer.token}`;
        }
        break;

      default:
        this.logger.warn(`Unsupported auth type: ${collector.auth.type}`);
        break;
    }
  }

  /**
   * Create error result
   */
  private createErrorResult(
    collector: CollectionCollector,
    error: unknown,
    startTime: number
  ): CollectorCollectionResult {
    const endTime = Date.now();
    const duration = endTime - startTime;
    const timestamp = new Date();

    this.logger.error(
      `Error in KGX collection process: ${getErrorMessage(error)}`,
      getErrorStack(error as Error)
    );

    return {
      id: uuidv4(),
      collector_id: collector.id,
      status: CollectionStatus.FAILURE,
      data: null,
      data_hash: generateHash({ error: getErrorMessage(error) }, "md5"),
      metadata: {
        timestamp,
        duration_ms: duration,
        error: {
          message: getErrorMessage(error),
          stack: getErrorStack(error as Error),
        },
      },
      created_at: timestamp,
    };
  }

  // ===== Public API Methods =====

  /**
   * Get all supported data types
   */
  getSupportedDataTypes(): KgxDataType[] {
    return Array.from(this.handlers.keys());
  }

  /**
   * Get configuration for a specific data type
   */
  getDataTypeConfig(dataType: KgxDataType) {
    const handler = this.getHandler(dataType);
    return handler ? handler.getConfig() : null;
  }

  /**
   * Get current feature flags
   */
  getFeatureFlags(): KgxFeatureFlags {
    return { ...this.featureFlags };
  }

  /**
   * Update feature flags
   */
  updateFeatureFlags(newFlags: Partial<KgxFeatureFlags>): void {
    Object.assign(this.featureFlags, newFlags);
    this.logger.log(`Updated feature flags: ${JSON.stringify(newFlags)}`);
  }

  /**
   * Get handler statistics
   */
  getHandlerStats() {
    return {
      totalHandlers: this.handlers.size,
      supportedTypes: this.getSupportedDataTypes(),
      featureFlags: this.featureFlags,
      handlers: Array.from(this.handlers.entries()).map(([type, handler]) => ({
        type,
        handlerClass: handler.constructor.name,
        config: handler.getConfig(),
      })),
    };
  }
}
