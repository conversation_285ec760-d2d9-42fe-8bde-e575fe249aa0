#!/bin/sh

# Switch to nextjs user for SQLite rebuild
su nextjs -c "
# Rebuild native dependencies for Alpine Linux after volume mounts
echo 'Rebuilding native dependencies for Alpine Linux...'
yarn rebuild sqlite3 better-sqlite3 2>/dev/null || echo 'Native dependency rebuild completed (some packages may not need rebuilding)'
"

# Switch to nextjs user and start the original command
exec su nextjs -c "exec \"\$@\"" -- "$@" 