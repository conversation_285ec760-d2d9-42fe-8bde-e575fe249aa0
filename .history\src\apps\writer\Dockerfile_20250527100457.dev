FROM node:20-alpine

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json ./

# Install all dependencies (including dev dependencies)
RUN yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 && \
    yarn global add nx nodemon ts-node

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/writer ./apps/writer/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3004

# Expose port
EXPOSE 3004

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/writer", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node -r tsconfig-paths/register apps/writer/src/main.ts"] 