import { Injectable, Logger } from "@nestjs/common";
import { Job, MySQLServiceBase } from "@data-pipeline/storage";
import { getErrorMessage, getErrorStack } from "@data-pipeline/core";
import { JobService } from "../../domain/services/job.service";
import { CreateJobCommand } from "../commands/create-job.command";

@Injectable()
export class CreateJobHandler {
  private readonly logger = new Logger(CreateJobHandler.name);
  private readonly schedulersTable = "schedulers";
  private defaultSchedulerId: string | null = null;

  constructor(
    private readonly jobService: JobService,
    private readonly mySqlService: MySQLServiceBase
  ) {}

  async execute(command: CreateJobCommand): Promise<Job> {
    if (!this.defaultSchedulerId) {
      await this.ensureDefaultScheduler();
    }

    const data = command.data || {};
    if (!data.scheduler_id) {
      data.scheduler_id = this.defaultSchedulerId;
    }

    return this.jobService.create({
      name: command.name,
      type: command.type,
      schedule: command.schedule,
      data: data,
      enabled: command.enabled !== undefined ? command.enabled : true,
    });
  }

  /**
   * Ensure a default scheduler exists for job associations
   */
  private async ensureDefaultScheduler(): Promise<void> {
    const defaultId = process.env.DEFAULT_SCHEDULER_ID || "scheduler_default";
    try {
      const query = `SELECT id FROM ${this.schedulersTable} WHERE id = ?`;
      const existing = await this.mySqlService.query(query, [defaultId]);

      if (!existing || existing.length === 0) {
        // Insert default scheduler
        const connectionJson = JSON.stringify({ url: "", authType: "none" });
        const insertSql = `
          INSERT INTO ${this.schedulersTable}
            (id, name, description, type, connection, enabled, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        await this.mySqlService.query(insertSql, [
          defaultId,
          "Default Scheduler",
          "Auto-generated default scheduler",
          "api",
          connectionJson,
          1,
        ]);
        this.logger.log(`Created default scheduler with id ${defaultId}`);
      }
      this.defaultSchedulerId = defaultId;
      this.logger.log(`Using default scheduler with id ${defaultId}`);
    } catch (err) {
      this.logger.error(
        `Failed to ensure default scheduler: ${getErrorMessage(err as Error)}`,
        getErrorStack(err as Error)
      );
      throw err;
    }
  }
}
