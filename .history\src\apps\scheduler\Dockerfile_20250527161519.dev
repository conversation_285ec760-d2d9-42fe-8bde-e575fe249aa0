FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files first for better layer caching
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Install all dependencies including dev dependencies
RUN apk add --no-cache python3 make g++ && \
    yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 --production=false && \
    yarn global add nx nodemon ts-node

# Runtime stage - much smaller
FROM node:22-alpine AS runtime

WORKDIR /app

# Copy only the installed node_modules and necessary files from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /usr/local/lib/node_modules ./global_modules
COPY --from=builder /usr/local/bin/nx /usr/local/bin/nodemon /usr/local/bin/ts-node /usr/local/bin/

# Copy configuration files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/scheduler ./apps/scheduler/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3001

# Expose port
EXPOSE 3001

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/scheduler", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/scheduler/src/main.ts"] 