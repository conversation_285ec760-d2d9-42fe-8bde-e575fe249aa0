import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { <PERSON>ron, CronExpression } from "@nestjs/schedule";
import {
  getErrorMessage,
  getErrorStack,
  JobType,
  AuthType,
  SchedulerType,
} from "@data-pipeline/core";
import { JobService } from "../../domain/services/job.service";
import { MySQLService } from "../../infrastructure/database/services/mysql.service";

@Injectable()
export class CollectorJobSyncService implements OnModuleInit {
  private readonly logger = new Logger(CollectorJobSyncService.name);
  private readonly collectorsTable = "collectors";
  private readonly schedulersTable = "schedulers";
  private isRunning = false;
  private defaultSchedulerId: string | null = null;

  constructor(
    private readonly jobService: JobService,
    private readonly mySqlService: MySQLService
  ) {}

  async onModuleInit() {
    this.logger.log("Initializing CollectorJobSyncService...");
    try {
      await this.ensureDefaultScheduler();
      await this.syncCollectorsWithJobs();
      this.logger.log("Initial sync completed successfully");
    } catch (error) {
      this.logger.error(
        `Initial sync failed: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  @Cron(CronExpression.EVERY_5_MINUTES)
  async syncCollectorsWithJobs() {
    if (this.isRunning) {
      this.logger.warn("Sync already in progress, skipping...");
      return;
    }
    // Ensure a default scheduler exists for associating jobs
    if (!this.defaultSchedulerId) {
      await this.ensureDefaultScheduler();
    }
    this.isRunning = true;
    const startTime = Date.now();

    try {
      this.logger.log({
        message: "Starting collector job sync...",
        timestamp: new Date().toISOString(),
      });

      // Get all collectors with schedules
      const collectors = await this.getCollectorsWithSchedules();

      this.logger.debug({
        message: "Collectors retrieved",
        count: collectors.length,
        collectors: collectors.map((c: any) => ({
          id: c.id,
          name: c.name,
          schedule: c.schedule,
        })),
      });

      // Get all existing jobs
      const jobs = await this.jobService.findAll();

      this.logger.debug({
        message: "Existing jobs retrieved",
        count: jobs.length,
        jobs: jobs.map((j) => ({
          id: j.id,
          name: j.name,
          type: j.type,
          collector_id: j.data?.collector_id,
        })),
      });

      // Process collectors
      for (const collector of collectors) {
        try {
          const existingJob = jobs.find(
            (job) =>
              job.type === JobType.COLLECTION &&
              job.data?.collector_id === collector.id
          );

          if (existingJob) {
            this.logger.debug({
              message: "Processing existing job",
              collector_id: collector.id,
              job_id: existingJob.id,
              current_schedule: existingJob.schedule,
              new_schedule: collector.schedule,
            });

            if (
              existingJob.schedule !== collector.schedule ||
              existingJob.enabled !== (collector.enabled === 1)
            ) {
              await this.jobService.update(String(existingJob.id), {
                schedule: collector.schedule,
                enabled: collector.enabled === 1,
              });
              this.logger.log(
                `Updated job ${existingJob.id} for collector ${collector.id}`
              );
            }
          } else {
            const newJob = await this.jobService.create({
              name: `Collector Job: ${collector.name}`,
              type: JobType.COLLECTION,
              schedule: collector.schedule,
              data: {
                collector_id: collector.id,
                scheduler_id: this.defaultSchedulerId,
              },
              enabled: collector.enabled === 1,
            });
            this.logger.log(
              `Created new job ${newJob.id} for collector ${collector.id}`
            );
          }
        } catch (error) {
          this.logger.error({
            message: `Error processing collector`,
            collector_id: collector.id,
            error: getErrorMessage(error as Error),
            stack: getErrorStack(error as Error),
          });
        }
      }

      const duration = Date.now() - startTime;
      this.logger.log({
        message: "Collector job sync completed",
        duration_ms: duration,
        collectors_processed: collectors.length,
      });
    } catch (error) {
      this.logger.error({
        message: "Sync failed",
        error: getErrorMessage(error as Error),
        stack: getErrorStack(error as Error),
      });
    } finally {
      this.isRunning = false;
    }
  }

  private async getCollectorsWithSchedules() {
    const query = `
      SELECT
        id,
        name,
        schedule,
        enabled,
        created_at,
        updated_at
      FROM ${this.collectorsTable}
      WHERE schedule IS NOT NULL
      AND schedule != ''
      AND enabled = 1
    `;

    this.logger.debug({
      message: "Fetching collectors",
      query: query.replace(/\s+/g, " ").trim(),
    });

    const results = await this.mySqlService.query(query);

    this.logger.debug({
      message: "Collectors query results",
      count: results.length,
      first_result: results[0],
    });

    return results;
  }

  /**
   * Ensure a default scheduler exists for job associations
   */
  private async ensureDefaultScheduler(): Promise<void> {
    const defaultId = process.env.DEFAULT_SCHEDULER_ID || "scheduler_default";
    try {
      // Check if the default scheduler exists
      const query = `SELECT id FROM ${this.schedulersTable} WHERE id = ?`;
      const existing = await this.mySqlService.query(query, [defaultId]);

      if (!existing || existing.length === 0) {
        // Insert default scheduler
        const connectionJson = JSON.stringify({
          url: "",
          authType: AuthType.NONE,
        });
        const insertSql = `
          INSERT INTO ${this.schedulersTable}
            (id, name, description, type, connection, enabled, created_at, updated_at)
          VALUES (?, ?, ?, ?, ?, ?, NOW(), NOW())
        `;
        await this.mySqlService.query(insertSql, [
          defaultId,
          "Default Scheduler",
          "Auto-generated default scheduler",
          SchedulerType.API,
          connectionJson,
          1,
        ]);
        this.logger.log(`Created default scheduler with id ${defaultId}`);
      }
      this.defaultSchedulerId = defaultId;
      this.logger.log(`Using default scheduler with id ${defaultId}`);
    } catch (err) {
      this.logger.error(
        `Failed to ensure default scheduler: ${getErrorMessage(err as Error)}`,
        getErrorStack(err as Error)
      );
      throw err;
    }
  }
}
