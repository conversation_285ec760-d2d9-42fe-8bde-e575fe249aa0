{"name": "data-pipeline", "version": "0.1.0", "private": true, "description": "Data Pipeline for processing and serving time-series data", "license": "MIT", "scripts": {"build": "nx run-many --target=build --all", "build:custom": "node -e \"const { spawnSync } = require('child_process'); const isWin = process.platform === 'win32'; const cmd = isWin ? 'build-custom.bat' : 'bash'; const args = isWin ? [] : ['build-custom.sh']; spawnSync(cmd, args, {stdio: 'inherit'});\"", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down -v", "setup:db": "yarn docker:up && node -e \"const { spawnSync } = require('child_process'); const isWin = process.platform === 'win32'; const cmd = isWin ? 'powershell.exe' : 'bash'; const args = isWin ? ['-ExecutionPolicy', 'Bypass', '-File', './scripts/setup.ps1'] : ['./scripts/setup.sh']; spawnSync(cmd, args, {stdio: 'inherit'});\"", "start:dev": "yarn setup:db && yarn start:all-services", "start:dev:no-sync": "node -e \"const { spawnSync } = require('child_process'); const isWin = process.platform === 'win32'; const cmd = isWin ? 'powershell.exe' : 'bash'; const args = isWin ? ['-ExecutionPolicy', 'Bypass', '-File', './scripts/setup-no-sync.ps1'] : ['./scripts/setup-no-sync.sh']; spawnSync(cmd, args, {stdio: 'inherit'});\" && cross-env TYPEORM_SYNCHRONIZE=false yarn start:all-services", "start:dev:no-sync:win": "scripts\\start-no-sync-windows.bat", "start:all-services": "concurrently \"nx run scheduler:serve\" \"nx run collector:serve\" \"nx run processor:serve\" \"nx run writer:serve\" \"nx run api:serve\"", "start:scheduler": "nx run scheduler:serve", "start:collector": "nx run collector:serve", "start:processor": "nx run processor:serve", "start:writer": "nx run writer:serve", "start:api": "nx run api:serve", "test": "nx run-many --target=test --all", "test:kgx-data-sources": "ts-node apps/processor/src/test-utils/data-source-tester.ts", "test:kgx-calculation": "ts-node apps/processor/src/test-utils/test-kgx-calculation.ts", "test:usd-index": "node apps/processor/src/test-utils/test-usd-index-calculation.js", "test:real-forex": "node apps/processor/src/test-utils/test-real-forex-rates.js", "test:kgx-all": "ts-node apps/processor/src/test-utils/run-all-tests.ts", "lint": "nx run-many --target=lint --all", "format": "nx format:write", "clean": "nx run-many --target=clean --all", "start:scheduler:debug": "NODE_OPTIONS='--inspect-brk=9234' nx run scheduler:serve", "debug:scheduler": "node --inspect-brk ./dist/apps/scheduler/main.js"}, "dependencies": {"@aws-sdk/client-s3": "^3.782.0", "@elastic/elasticsearch": "^8.17.1", "@nestjs/axios": "^4.0.0", "@nestjs/bull": "^11.0.2", "@nestjs/cache-manager": "^3.0.1", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.0.0", "@nestjs/core": "^10.0.0", "@nestjs/event-emitter": "^3.0.1", "@nestjs/jwt": "^10.1.0", "@nestjs/microservices": "^10.0.0", "@nestjs/passport": "^10.0.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/schedule": "^5.0.1", "@nestjs/swagger": "^7.0.0", "@nestjs/terminus": "^11.0.0", "@nestjs/typeorm": "^10.0.0", "@nestjs/websockets": "^10.0.0", "@opentelemetry/auto-instrumentations-node": "^0.40.0", "@opentelemetry/exporter-trace-otlp-http": "^0.45.0", "@opentelemetry/resources": "^1.18.1", "@opentelemetry/sdk-node": "^0.45.0", "@opentelemetry/semantic-conventions": "^1.18.1", "@types/node-fetch": "^2.6.12", "@types/node-schedule": "^2.1.7", "ajv": "^8.17.1", "amqplib": "^0.10.7", "axios": "^1.7.7", "better-sqlite3": "^11.9.1", "bull": "^4.10.0", "bullmq": "^3.15.0", "cache-manager": "^6.4.2", "class-transformer": "^0.5.1", "class-validator": "^0.14.0", "csv-parser": "^3.2.0", "csv-stringify": "^6.5.2", "date-fns": "^4.1.0", "dotenv": "^16.0.0", "eventsource": "^3.0.6", "fast-csv": "^5.0.2", "ioredis": "^5.6.0", "joi": "^17.9.0", "jq": "^1.7.2", "kafkajs": "^2.2.0", "keyv": "^5.1.0", "moment-timezone": "^0.5.48", "mongodb": "^6.15.0", "mqtt": "^5.10.4", "mssql": "^11.0.1", "mysql2": "^3.2.0", "node-schedule": "^2.1.1", "oracledb": "^6.8.0", "passport": "^0.6.0", "passport-jwt": "^4.0.1", "pg": "^8.15.6", "pino": "^8.11.0", "pino-http": "^8.3.1", "prom-client": "^14.2.0", "redis": "^4.6.0", "reflect-metadata": "^0.1.13", "rxjs": "^7.8.0", "socket.io": "^4.8.1", "sqlite": "^5.1.1", "ssh2-sftp-client": "^12.0.0", "tslib": "^2.8.1", "typeorm": "^0.3.0", "uuid": "^11.1.0", "ws": "^8.18.1", "xml2js": "^0.6.2", "yahoo-finance2": "^2.13.3", "yaml": "^2.2.0", "react": "^18.3.1", "react-dom": "^18.3.1"}, "devDependencies": {"@faker-js/faker": "^9.6.0", "@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@nrwl/eslint-plugin-nx": "16.0.0", "@nrwl/jest": "16.0.0", "@nrwl/js": "16.0.0", "@nrwl/linter": "16.0.0", "@nrwl/nest": "16.0.0", "@nrwl/node": "16.0.0", "@nrwl/nx-cloud": "16.0.0", "@nrwl/webpack": "^19.8.4", "@nrwl/workspace": "16.0.0", "@opentelemetry/api": "~1.7.0", "@types/amqplib": "^0.10.7", "@types/bull": "^4.10.0", "@types/express": "^4.17.0", "@types/jest": "^29.5.14", "@types/js-yaml": "^4.0.9", "@types/mssql": "^9.1.7", "@types/node": "^18.0.0", "@types/oracledb": "^6.5.4", "@types/passport-jwt": "^4.0.1", "@types/pg": "^8.11.14", "@types/sqlite3": "^5.1.0", "@types/ssh2-sftp-client": "^9.0.4", "@types/uuid": "^10.0.0", "@types/ws": "^8.18.1", "@types/xml2js": "^0.4.14", "@typescript-eslint/eslint-plugin": "^5.0.0", "@typescript-eslint/parser": "^5.0.0", "concurrently": "^9.1.2", "cross-env": "^7.0.3", "eslint": "^8.0.0", "eslint-config-prettier": "^8.0.0", "eslint-plugin-prettier": "^4.0.0", "jest": "^29.5.0", "nx": "16.0.0", "pino-pretty": "^13.0.0", "prettier": "^2.0.0", "ts-jest": "^29.1.0", "ts-loader": "^9.0.0", "ts-node": "^10.0.0", "tsconfig-paths": "^4.0.0", "typescript": "^5.0.0", "webpack": "^5.0.0"}, "resolutions": {"glob": "^10.3.0", "uuid": "^11.0.0", "acorn-import-assertions": "npm:acorn-import-attributes@^1.9.5", "@types/hapi__shot": "npm:@hapi/shot@^6.0.1"}, "optionalDependencies": {"@rollup/rollup-linux-x64-gnu": "^4.41.1", "@rollup/rollup-linux-x64-musl": "^4.41.1", "@rollup/rollup-darwin-x64": "^4.41.1", "@rollup/rollup-darwin-arm64": "^4.41.1", "@rollup/rollup-win32-x64-msvc": "^4.41.1"}, "workspaces": ["apps/*", "libs/*"], "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}