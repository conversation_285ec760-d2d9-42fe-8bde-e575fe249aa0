import { forwardR<PERSON>, <PERSON><PERSON><PERSON> } from "@nestjs/common";
import { PinoLoggerModule } from "@data-pipeline/logging";
import { JwtModule } from "@nestjs/jwt";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { CryptoKgxEntity, CryptoPriceEntity, UsdIndexValueEntity } from "@data-pipeline/kgx";
import { SecurityModule } from "@data-pipeline/security";
import { APP_GUARD } from "@nestjs/core";
import { ConfigModule } from "@nestjs/config";
import { CombinedAuthGuard } from "@data-pipeline/security";
import { GlobalDatabaseModule } from "@data-pipeline/storage";
import { ConfigEntity } from "@data-pipeline/storage";
import { InfrastructureModule } from "./infrastructure/infrastructure.module";
import { DomainModule } from "./domain/domain.module";
import { ApplicationModule } from "./application/application.module";
import { PresentationModule } from "./presentation/presentation.module";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.local"],
    }),
    PinoLoggerModule.register({
      serviceName: "api-service",
      logLevel: process.env.LOG_LEVEL || "info",
      prettyPrint: process.env.NODE_ENV !== "production",
    }),
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: ".",
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || "secret",
      signOptions: { expiresIn: "1h" },
    }),
    GlobalDatabaseModule.forRoot({
      mysqlEntities: [ConfigEntity],
      postgresEntities: [CryptoKgxEntity, CryptoPriceEntity, UsdIndexValueEntity],
      serviceName: "API",
    }),
    SecurityModule.register({
      jwt: {
        secret: process.env.JWT_SECRET || "secret",
        expiresIn: process.env.JWT_EXPIRES_IN || "3600s",
        issuer:
          process.env.API_SERVICE_URL ||
          `http://${process.env.API_SERVICE_HOST}:3000`,
        audience: process.env.API_AUDIENCE || "*",
      },
      apiKey: {
        keys: {
          [process.env.API_KEY || "default_key"]: {
            name: "default",
            roles: [],
            permissions: [],
          },
        },
      },
    }),
    forwardRef(() => InfrastructureModule),
    forwardRef(() => DomainModule),
    forwardRef(() => ApplicationModule),
    forwardRef(() => PresentationModule),
  ],
  providers: [{ provide: APP_GUARD, useClass: CombinedAuthGuard }],
})
export class AppModule { }
