# Extend from shared base image
FROM data-pipeline-base:dev

# Copy service-specific source code
COPY apps/dashboard ./apps/dashboard/

# Set service-specific environment variables
ENV PORT=3010 \
    CHOKIDAR_USEPOLLING=true \
    WATCHPACK_POLLING=true

# Expose port
EXPOSE 3010

# Development command with hot reload for React
CMD ["npx", "nx", "serve", "dashboard", "--host", "0.0.0.0", "--port", "3010"] 