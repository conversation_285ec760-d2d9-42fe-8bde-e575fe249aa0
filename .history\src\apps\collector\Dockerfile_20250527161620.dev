FROM node:22-alpine AS builder

WORKDIR /app

# Copy package files first for better layer caching
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Install all dependencies including dev dependencies
RUN apk add --no-cache python3 make g++ && \
    yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 --production=false && \
    yarn global add nx nodemon ts-node

# Runtime stage - keep build tools for SQLite rebuild
FROM node:22-alpine AS runtime

WORKDIR /app

# Install build tools needed for SQLite rebuild in entrypoint
RUN apk add --no-cache python3 make g++

# Copy only the installed node_modules and necessary files from builder
COPY --from=builder /app/node_modules ./node_modules
COPY --from=builder /usr/local/lib/node_modules ./global_modules
COPY --from=builder /usr/local/bin/nx /usr/local/bin/nodemon /usr/local/bin/ts-node /usr/local/bin/

# Copy configuration files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/collector ./apps/collector/

# Copy entrypoint script
COPY apps/collector/entrypoint.dev.sh /entrypoint.dev.sh
RUN chmod +x /entrypoint.dev.sh

# Set environment variables
ENV NODE_ENV=development \
    PORT=3002

# Expose port
EXPOSE 3002

# Use entrypoint to rebuild native dependencies after volume mounts
ENTRYPOINT ["/entrypoint.dev.sh"]

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/collector", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/collector/src/main.ts"] 