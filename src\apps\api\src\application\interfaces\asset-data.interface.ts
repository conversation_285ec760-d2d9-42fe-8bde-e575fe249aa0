/**
 * Standardized Asset Data Interfaces
 * These interfaces define the shape of data returned by all KGX endpoints
 */

// Raw database record interface
export interface KgxRecord {
  id: string;
  time: string;
  symbol: string;
  price: number;
  high?: number;
  low?: number;
  price_change?: number;
  price_change_percent?: number;
  change?: number;
  change_due_usd_percent?: number;
  change_due_trade_percent?: number;
  change_usd?: number;
  change_due_usd?: number;
  ask?: number;
  bid?: number;
  asset_type?: string;
  name?: string;
  unit?: string;
  timestamp?: string | number | Date;
  kgx_value?: number;
  volume?: number;
  market_cap?: number;
  metadata?: any;
  extra?: any;
  [key: string]: any;
}

// Standardized output interface for all endpoints
export interface StandardAssetData {
  Symbol: string;
  Name: string;
  Price: number | null;
  High: number | null;
  Low: number | null;
  Mid: number | null;
  Ask: number | null;
  Bid: number | null;
  Change: number;
  ChangeTrade: number;
  ChangeUSD: number;
  ChangePercentage: number;
  ChangePercentTrade: number;
  ChangePercentUSD: number | null;
  Currency: string;
  Unit: string;
  Timestamp: string;
  KgxValue: number | null;
  AssetType?: string;
  Volume?: number | null;
  MarketCap?: number | null;
  metadata?: any;
  extra?: any;
}

// Response format interfaces for different endpoint types
export interface ValueFormatResponse extends StandardAssetData {}

export interface PreciousMetalsFormatResponse {
  CryptoMetals: {
    crypto: StandardAssetData[];
    KgxValues?: (number | null)[];
  };
}

export interface BaseMetalsFormatResponse {
  BaseMetals: {
    BM: Omit<StandardAssetData, 'Ask' | 'Bid' | 'Mid' | 'KgxValue' | 'AssetType' | 'Volume' | 'MarketCap' | 'metadata' | 'extra'>[];
  };
}

export interface CryptoFormatResponse {
  Cryptocurrencies: {
    CR: StandardAssetData[];
    KgxValues?: (number | null)[];
  };
}

// Transformation options
export interface TransformationOptions {
  dateFormat?: string;
  timeFormat?: string;
  includeKgx?: boolean;
  format?: 'value' | 'precious-metals' | 'base-metals' | 'crypto';
}

// Asset type enumeration
export enum AssetType {
  CRYPTOCURRENCY = 'CRYPTOCURRENCY',
  PRECIOUS_METALS = 'PRECIOUS_METALS',
  BASE_METALS = 'BASE_METALS',
  ENERGY = 'ENERGY',
  FOREX = 'FOREX'
}

// Unit mapping by asset type
export const ASSET_TYPE_UNITS: Record<AssetType, string> = {
  [AssetType.CRYPTOCURRENCY]: 'SHARES',
  [AssetType.PRECIOUS_METALS]: 'GRAM',
  [AssetType.BASE_METALS]: 'TONNE',
  [AssetType.ENERGY]: 'BARREL',
  [AssetType.FOREX]: 'UNIT'
};
