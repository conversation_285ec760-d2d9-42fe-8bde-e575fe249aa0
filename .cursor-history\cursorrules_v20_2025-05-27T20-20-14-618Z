# Instructions

During your interaction with the user, if you find anything reusable in this project (e.g. version of a library, model name), especially about a fix to a mistake you made or a correction you received, you should take note in the `Lessons` section in the `.cursorrules` file so you will not make the same mistake again. 

You should also use the `.cursorrules` file as a Scratchpad to organize your thoughts. Especially when you receive a new task, you should first review the content of the Scratchpad, clear old different task if necessary, first explain the task, and plan the steps you need to take to complete the task. You can use todo markers to indicate the progress, e.g.
[X] Task 1
[ ] Task 2

Also update the progress of the task in the Scratchpad when you finish a subtask.
Especially when you finished a milestone, it will help to improve your depth of task accomplishment to use the Scratchpad to reflect and plan.
The goal is to help you maintain a big picture as well as the progress of the task. Always refer to the Scratchpad when you plan the next step.

# Tools

Note all the tools are in python3. So in the case you need to do batch processing, you can always consult the python files and write your own script.

## Screenshot Verification

The screenshot verification workflow allows you to capture screenshots of web pages and verify their appearance using LLMs. The following tools are available:

1. Screenshot Capture:
```bash
venv/bin/python3 tools/screenshot_utils.py URL [--output OUTPUT] [--width WIDTH] [--height HEIGHT]
```

2. LLM Verification with Images:
```bash
venv/bin/python3 tools/llm_api.py --prompt "Your verification question" --provider {openai|anthropic} --image path/to/screenshot.png
```

Example workflow:
```python
from screenshot_utils import take_screenshot_sync
from llm_api import query_llm

# Take a screenshot

screenshot_path = take_screenshot_sync('https://example.com', 'screenshot.png')

# Verify with LLM

response = query_llm(
    "What is the background color and title of this webpage?",
    provider="openai",  # or "anthropic"
    image_path=screenshot_path
)
print(response)
```

## LLM

You always have an LLM at your side to help you with the task. For simple tasks, you could invoke the LLM by running the following command:
```
venv/bin/python3 ./tools/llm_api.py --prompt "What is the capital of France?" --provider "anthropic"
```

The LLM API supports multiple providers:
- OpenAI (default, model: gpt-4o)
- Azure OpenAI (model: configured via AZURE_OPENAI_MODEL_DEPLOYMENT in .env file, defaults to gpt-4o-ms)
- DeepSeek (model: deepseek-chat)
- Anthropic (model: claude-3-sonnet-20240229)
- Gemini (model: gemini-pro)
- Local LLM (model: Qwen/Qwen2.5-32B-Instruct-AWQ)

But usually it's a better idea to check the content of the file and use the APIs in the `tools/llm_api.py` file to invoke the LLM if needed.

## Web browser

You could use the `tools/web_scraper.py` file to scrape the web.
```bash
venv/bin/python3 ./tools/web_scraper.py --max-concurrent 3 URL1 URL2 URL3
```
This will output the content of the web pages.

## Search engine

You could use the `tools/search_engine.py` file to search the web.
```bash
venv/bin/python3 ./tools/search_engine.py "your search keywords"
```
This will output the search results in the following format:
```
URL: https://example.com
Title: This is the title of the search result
Snippet: This is a snippet of the search result
```
If needed, you can further use the `web_scraper.py` file to scrape the web page content.

# Lessons

## User Specified Lessons

- You have a python venv in ./venv. Always use (activate) it when doing python development. First, to check whether 'uv' is available, use `which uv`. If that's the case, first activate the venv, and then use `uv pip install` to install packages. Otherwise, fall back to `pip`.
- Due to Cursor's limit, when you use `git` and `gh` and need to submit a multiline commit message, first write the message in a file, and then use `git commit -F <filename>` or similar command to commit. And then remove the file. Include "[Cursor] " in the commit message and PR title.

## Cursor learned

- For search results, ensure proper handling of different character encodings (UTF-8) for international queries
- When using seaborn styles in matplotlib, use 'seaborn-v0_8' instead of 'seaborn' as the style name due to recent seaborn version changes
- Use 'gpt-4o' as the model name for OpenAI's GPT-4 with vision capabilities
- When searching for recent news, use the current year (2025) instead of previous years, or simply use the "recent" keyword to get the latest information
- **Scheduler Hanging Fix**: The scheduler service was hanging due to multiple database configuration issues:
  1. `includeDynamicDatabase: true` - Creates infinite transaction loops with crypto adapters
  2. `includePostgres: true` - Tries to connect to unavailable PostgreSQL 
  3. `includeDatabaseInitializer: true` - Attempts PostgreSQL table creation
  4. `includeEnhancedServices: true` - Provides PostgresService causing dependency injection errors
  5. Mixed MySQLService types - Storage library's MySQLService vs local MySQLService vs MySQLServiceBase
  
  **Solution**: For MySQL-only services like scheduler:
  - Set `includeDynamicDatabase: false`, `includePostgres: false`, `includeDatabaseInitializer: false`, `includeEnhancedServices: false`
  - Use `MySQLServiceBase` instead of `MySQLService` in dependency injection to avoid type conflicts
  - Replace complex `StorageDatabaseModule.register()` with simple `MySQLModule.register()` + `RepositoriesModule`

- **ts-node Module Resolution Fix**: Fixed "Cannot find module" errors in Docker development environment:
  **Problem**: ts-node was compiling TypeScript with ES module syntax instead of CommonJS, causing module resolution failures
  **Root Cause**: TypeScript path mapping pointed to source files, and ts-node wasn't respecting CommonJS configuration
  **Solution**: 
  1. Created `tsconfig.node.json` with explicit CommonJS configuration and ts-node settings
  2. Updated all `Dockerfile.dev` files to use `--project tsconfig.node.json` flag with ts-node
  3. Added `"ts-node": { "esm": false, "transpileOnly": true }` configuration
  4. Ensured all development containers copy the tsconfig.node.json file
  **Files Modified**: All `apps/*/Dockerfile.dev` files and created `src/tsconfig.node.json`

- **SQLite3 Native Bindings Fix**: Fixed SQLite3 native binding errors in Docker development environment:
  **Problem**: SQLite3 native bindings were not compiled for Alpine Linux architecture, causing "Could not locate the bindings file" errors
  **Root Cause**: Native dependencies installed on host system (Windows) don't work in Alpine Linux containers
  **Solution**: 
  1. Added `yarn rebuild sqlite3 better-sqlite3 || true` step to all Dockerfile.dev files
  2. Added `!tsconfig.node.json` to .dockerignore to ensure the new tsconfig is included in Docker builds
  3. Rebuild happens after dependency installation to compile native bindings for Alpine Linux
  **Files Modified**: All `apps/*/Dockerfile.dev` files and `src/.dockerignore`

- **Docker Image Size Optimization**: Aggressively reduced Docker development image size to under 1GB per service:
  **Problem**: Adding native dependency rebuilds and multiple RUN commands increased image size significantly
  **Root Cause**: Multiple RUN layers, duplicate builds, and lack of cleanup created unnecessary image bloat
  **Solution**:
  1. **Multi-Stage Builds**: Implemented builder stage for dependencies, runtime stage for execution
  2. **Shared Base Image**: Created `Dockerfile.base.dev` that all services extend from for layer sharing
  3. Combined ALL RUN commands into single layers to minimize Docker layer count
  4. Added aggressive cleanup: `yarn cache clean`, `npm cache clean --force`
  5. Removed build dependencies after installation: `apk del python3 make g++` (except collector)
  6. Extensive cleanup: `rm -rf /tmp/* /var/cache/apk/* /root/.npm /root/.yarn-cache`
  7. Used `--production=false` flag for yarn install to be explicit about dev dependencies
  8. Removed unnecessary SQLite rebuilds from services that don't use SQLite
  9. Used entrypoint script only for collector service that actually needs SQLite
  10. Optimized layer caching by copying package files first
  11. **Multi-stage separation**: Build tools and caches stay in builder stage, only runtime files in final image
  12. **Layer sharing**: Common dependencies shared across all services via base image
  **Target**: Each microservice image under 500MB with multi-stage builds + shared layers
  **Files Modified**: All `apps/*/Dockerfile.dev` files, `Dockerfile.base.dev`, `docker-compose.override.yml`

# Scratchpad

## Current Task: Optimize Docker Image Size and Fix SQLite3 Native Bindings

**Issue**: Docker image size increased from 2.35GB to 4.15GB due to native dependency rebuilds and inefficient Dockerfile layers

**Root Cause Analysis**:
- Multiple RUN commands creating separate Docker layers
- Duplicate native dependency builds (during install + explicit rebuild)
- No cleanup of build artifacts and caches
- Unnecessary SQLite rebuilds in services that don't use SQLite

**Plan Status**:
[X] Analyze the module resolution error
[X] Identify ts-node compilation issue
[X] Create tsconfig.node.json with explicit CommonJS configuration
[X] Update all Dockerfile.dev files to use the new tsconfig
[X] Add ts-node specific configuration with esm: false
[X] Ensure all development containers copy the new tsconfig file
[X] Fix .dockerignore to include tsconfig.node.json
[X] Add native dependency rebuild for SQLite3 bindings
[X] Identify Docker image size increase issue
[X] Optimize Dockerfile.dev files to reduce image size
[X] Combine RUN commands into single layers
[X] Add cleanup steps for caches and temporary files
[X] Remove unnecessary SQLite rebuilds from non-SQLite services
[X] Create entrypoint script approach for collector service
[X] Apply aggressive size optimization to ALL microservices
[X] Target under 1GB per microservice image
[X] Implement multi-stage builds for maximum size reduction
[X] Target under 500MB per microservice with multi-stage builds
[X] Document optimizations in .cursorrules
[ ] Test the optimized images and verify SQLite3 functionality