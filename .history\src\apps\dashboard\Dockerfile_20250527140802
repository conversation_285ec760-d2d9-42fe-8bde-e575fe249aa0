# Build stage
FROM node:22-alpine AS build
RUN apk add --no-cache python3 make g++

# Enable corepack and install specific Yarn version
RUN corepack enable && corepack prepare yarn@stable --activate

WORKDIR /app

# Copy package files
COPY apps/dashboard/package.json ./

# # First install without lockfile to generate it if needed
# RUN yarn set version stable && \
#     yarn config set nodeLinker node-modules && \
#     yarn install --no-immutable

# # Now copy the lockfile and install with it
# COPY apps/dashboard/yarn.lock ./
RUN yarn install

# Copy the rest of the application code
COPY apps/dashboard/ ./

# Build the application
RUN yarn build

# Production stage
FROM nginx:alpine

# Copy the build output from the build stage
COPY --from=build /app/dist /usr/share/nginx/html

# Copy custom nginx configuration if needed
# COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port 3010
EXPOSE 3010

# Create a custom nginx.conf that listens on port 3010
RUN echo 'server { \
    listen 3010; \
    location / { \
    root /usr/share/nginx/html; \
    index index.html index.htm; \
    try_files $uri $uri/ /index.html; \
    } \
    location /api/health { \
    access_log off; \
    add_header Content-Type application/json; \
    return 200 "{\"status\":\"up\"}"; \
    } \
    }' > /etc/nginx/conf.d/default.conf

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
