#!/bin/bash

# Script to switch between different environment configurations
# Usage: ./switch-env.sh [local|docker|dev|production]

set -e

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# Function to display usage
usage() {
    echo "Usage: $0 [local|docker|dev|production]"
    echo ""
    echo "Environment modes:"
    echo "  local      - All services running locally (localhost URLs)"
    echo "  docker     - Services running in Docker containers"
    echo "  dev        - Development environment (same as local)"
    echo "  production - Production environment"
    echo ""
    echo "This script copies the appropriate .env file to .env"
    exit 1
}

# Function to backup current .env
backup_env() {
    if [ -f "$PROJECT_ROOT/.env" ]; then
        cp "$PROJECT_ROOT/.env" "$PROJECT_ROOT/.env.backup.$(date +%Y%m%d_%H%M%S)"
        echo "✓ Backed up current .env file"
    fi
}

# Function to copy environment file
copy_env() {
    local env_file="$1"
    local env_name="$2"
    
    if [ ! -f "$env_file" ]; then
        echo "❌ Environment file $env_file not found!"
        exit 1
    fi
    
    backup_env
    cp "$env_file" "$PROJECT_ROOT/.env"
    echo "✓ Switched to $env_name environment"
    echo "✓ Active environment file: $env_file"
}

# Function to show current environment
show_current() {
    echo "Current environment configuration:"
    if [ -f "$PROJECT_ROOT/.env" ]; then
        echo "NODE_ENV: $(grep '^NODE_ENV=' "$PROJECT_ROOT/.env" | cut -d'=' -f2 || echo 'not set')"
        echo "Service URLs:"
        grep -E '^(SCHEDULER|COLLECTOR|PROCESSOR|WRITER)_.*URL=' "$PROJECT_ROOT/.env" | head -4 || echo "  No service URLs found"
        echo "Database hosts:"
        grep -E '^(MYSQL|POSTGRES)_HOST=' "$PROJECT_ROOT/.env" || echo "  No database hosts found"
    else
        echo "❌ No .env file found"
    fi
}

# Main logic
case "${1:-}" in
    "local")
        copy_env "$PROJECT_ROOT/.env.local" "LOCAL"
        echo ""
        echo "🏠 LOCAL MODE ACTIVATED"
        echo "   - All services running on localhost"
        echo "   - Databases on localhost"
        echo "   - Perfect for running services with npm/yarn"
        ;;
    "docker")
        copy_env "$PROJECT_ROOT/.env.docker" "DOCKER"
        echo ""
        echo "🐳 DOCKER MODE ACTIVATED"
        echo "   - Services using container names"
        echo "   - Databases in Docker containers"
        echo "   - Perfect for docker-compose development"
        ;;
    "dev")
        copy_env "$PROJECT_ROOT/.env.dev" "DEVELOPMENT"
        echo ""
        echo "🔧 DEVELOPMENT MODE ACTIVATED"
        echo "   - Standard development configuration"
        echo "   - Services on localhost"
        ;;
    "production")
        copy_env "$PROJECT_ROOT/.env.production" "PRODUCTION"
        echo ""
        echo "🚀 PRODUCTION MODE ACTIVATED"
        echo "   - Production configuration"
        echo "   - Container-based service URLs"
        ;;
    "status"|"current"|"show")
        show_current
        ;;
    "help"|"-h"|"--help")
        usage
        ;;
    "")
        echo "❌ No environment specified"
        echo ""
        show_current
        echo ""
        usage
        ;;
    *)
        echo "❌ Unknown environment: $1"
        echo ""
        usage
        ;;
esac

echo ""
echo "📋 Quick commands:"
echo "   npm run start:api     - Start API service"
echo "   npm run start:all     - Start all services"
echo "   docker-compose up     - Start with Docker"
echo ""
echo "🔍 Check health: curl http://localhost:3000/api/health"
