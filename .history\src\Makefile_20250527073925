clean:
	rm -rf logs/*
	rm -rf .nx
	rm -rf dist
	rm -rf node_modules
	rm -rf package-lock.json
	rm -rf node_modules yarn.lock package-lock.json
	rm -rf apps/dashboard/node_modules apps/dashboard/yarn.lock
	rm -rf apps/*/dist
	rm -rf apps/*/node_modules
	rm -rf libs/*/node_modules
	rm -rf libs/*/dist
	rm -rf packages/*/node_modules
	rm -rf packages/*/dist
	rm -rf packages/*/coverage
	rm -rf packages/*/build
	rm -rf packages/*/dist
	yarn cache clean
	# Only run nx reset if node_modules exists
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	docker compose down -v

build-dev:
	cp .env.dev .env.local
	cp .env.dev .env
	cp .env.dev apps/dashboard/.env.local
	yarn install
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=development DEBUG=* yarn build
	@echo "Development build completed successfully! Dashboard built by NX as vite_react_shadcn_ts."

build-prod:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	@echo "Build completed successfully! Dashboard built by NX as vite_react_shadcn_ts."

build-prod-windows:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-win32-x64-msvc --optional -W || npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Stop NX daemon to release file locks before dashboard install
	-yarn nx reset 2>/dev/null || echo "NX reset failed, continuing..."
	# Force kill any remaining node processes that might hold file locks
	-taskkill /f /im node.exe 2>/dev/null || echo "No node processes to kill"
	# Wait a moment for file locks to be released
	sleep 2 2>/dev/null || timeout /t 2 /nobreak 2>/dev/null || echo "Sleep command not available"
	# Try dashboard install with retry mechanism
	cd apps/dashboard && (yarn install --ignore-optional --ignore-scripts || (echo "First attempt failed, retrying..." && sleep 3 && yarn install --ignore-optional --ignore-scripts))

build-prod-safe-windows:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-win32-x64-msvc --optional -W || npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Dashboard dependencies are already satisfied from main install, skip separate install
	@echo "Build completed successfully. Dashboard dependencies installed with main project."

build-prod-safe:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	make clean
	yarn install --ignore-optional --ignore-scripts --production=false
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	cd apps/dashboard && yarn install --ignore-optional --ignore-scripts --production=false

build-custom:
	yarn install --ignore-scripts --ignore-optional
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=development DEBUG=* yarn build:custom

start-dev:
	./scripts/start-dev-services.sh

workflow:
	./scripts/run-workflow.sh

start-no-sync:
	@echo "Starting with TypeORM synchronization disabled..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		./scripts/start-no-sync-windows.bat; \
	else \
		KAFKAJS_NO_PARTITIONER_WARNING=1 DEBUG=* TYPEORM_SYNCHRONIZE=false yarn start:dev:no-sync; \
	fi

restart:
	@echo "Stopping all services..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-services.ps1; \
	else \
		bash ./scripts/stop-services.sh; \
	fi
	@echo "Restarting Docker services..."
	@docker-compose restart
	@echo "Starting all services..."
	KAFKAJS_NO_PARTITIONER_WARNING=1 yarn start:dev

restart-scheduler:
	@echo "Stopping scheduler service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-service.ps1 -ServiceName scheduler; \
	else \
		bash ./scripts/stop-service.sh scheduler; \
	fi
	@echo "Starting scheduler service..."
	DEBUG=* yarn nx run scheduler:serve --verbose

restart-collector:
	@echo "Stopping collector service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-service.ps1 -ServiceName collector; \
	else \
		bash ./scripts/stop-service.sh collector; \
	fi
	@echo "Starting collector service..."
	yarn nx run collector:serve

restart-processor:
	@echo "Stopping processor service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-service.ps1 -ServiceName processor; \
	else \
		bash ./scripts/stop-service.sh processor; \
	fi
	@echo "Starting processor service..."
	yarn nx run processor:serve

restart-writer:
	@echo "Stopping writer service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-service.ps1 -ServiceName writer; \
	else \
		bash ./scripts/stop-service.sh writer; \
	fi
	@echo "Starting writer service..."
	yarn nx run writer:serve

restart-api:
	@echo "Stopping api service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		powershell -ExecutionPolicy Bypass -File ./scripts/stop-service.ps1 -ServiceName api; \
	else \
		bash ./scripts/stop-service.sh api; \
	fi
	@echo "Starting api service..."
	yarn nx run api:serve

# Log tailing commands
# -------------------

# List of services to monitor
SERVICES := collector processor api writer scheduler

# Open logs for all services in separate terminals
.PHONY: logs
logs:
	@echo "Opening logs for all services..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		cmd.exe /c "start /b cmd.exe /c make logs-collector"; \
		cmd.exe /c "start /b cmd.exe /c make logs-processor"; \
		cmd.exe /c "start /b cmd.exe /c make logs-api"; \
		cmd.exe /c "start /b cmd.exe /c make logs-writer"; \
		cmd.exe /c "start /b cmd.exe /c make logs-scheduler"; \
	else \
		make logs-collector & \
		make logs-processor & \
		make logs-api & \
		make logs-writer & \
		make logs-scheduler & \
	fi

# Individual service log targets
.PHONY: logs-collector
logs-collector:
	@echo "Opening logs for collector service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=collector -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo Collector service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="Collector Logs" -- bash -c "docker logs -f $$(docker ps --filter name=collector -q) || echo 'Collector service not running'; read -p 'Press Enter to close...'"; \
	fi

.PHONY: logs-processor
logs-processor:
	@echo "Opening logs for processor service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=processor -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo Processor service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="Processor Logs" -- bash -c "docker logs -f $$(docker ps --filter name=processor -q) || echo 'Processor service not running'; read -p 'Press Enter to close...'"; \
	fi

.PHONY: logs-api
logs-api:
	@echo "Opening logs for api service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=api -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo API service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="API Logs" -- bash -c "docker logs -f $$(docker ps --filter name=api -q) || echo 'API service not running'; read -p 'Press Enter to close...'"; \
	fi

.PHONY: logs-writer
logs-writer:
	@echo "Opening logs for writer service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=writer -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo Writer service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="Writer Logs" -- bash -c "docker logs -f $$(docker ps --filter name=writer -q) || echo 'Writer service not running'; read -p 'Press Enter to close...'"; \
	fi

.PHONY: logs-scheduler
logs-scheduler:
	@echo "Opening logs for scheduler service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=scheduler -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo Scheduler service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="Scheduler Logs" -- bash -c "docker logs -f $$(docker ps --filter name=scheduler -q) || echo 'Scheduler service not running'; read -p 'Press Enter to close...'"; \
	fi

# Target to view a specific service's logs
.PHONY: logs-service
logs-service:
	@if [ -z "$(SERVICE)" ]; then \
		echo "Usage: make logs-service SERVICE=<service-name>"; \
		exit 1; \
	fi
	@echo "Opening logs for $(SERVICE) service..."
	@if [ "$(OS)" = "Windows_NT" ]; then \
		CONTAINER_ID=$$(docker ps --filter name=$(SERVICE) -q 2>/dev/null || echo ""); \
		if [ -n "$$CONTAINER_ID" ]; then \
			cmd.exe /c "start cmd.exe /k docker logs -f $$CONTAINER_ID"; \
		else \
			cmd.exe /c "start cmd.exe /k echo $(SERVICE) service not running && pause"; \
		fi \
	else \
		gnome-terminal --tab --title="$(SERVICE) Logs" -- bash -c "docker logs -f $$(docker ps --filter name=$(SERVICE) -q) || echo '$(SERVICE) service not running'; read -p 'Press Enter to close...'"; \
	fi

build-prod-robust:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	# Clean install to ensure fresh dependencies
	rm -rf node_modules yarn.lock
	rm -rf apps/dashboard/node_modules apps/dashboard/yarn.lock
	# Install with proper flags
	yarn install --ignore-optional --ignore-scripts --production=false
	# Try to install platform-specific rollup binary
	-yarn add @rollup/rollup-linux-x64-gnu --optional -W 2>/dev/null || echo "Rollup native binary installation skipped"
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	# Build with fallback
	NODE_ENV=production DEBUG=* yarn build || (echo "Build failed, trying dashboard build separately..." && cd apps/dashboard && yarn install --ignore-optional --ignore-scripts && yarn build)
	cd apps/dashboard && yarn install --ignore-optional --ignore-scripts

build-prod-clean:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	# Clean everything as suggested by the npm bug workaround
	rm -rf node_modules package-lock.json yarn.lock
	rm -rf apps/dashboard/node_modules apps/dashboard/package-lock.json apps/dashboard/yarn.lock
	# Install dependencies without optional packages first
	yarn install --ignore-optional --ignore-scripts
	# Try to install the specific Rollup binary
	-yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup binary installation failed"
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	cd apps/dashboard && yarn install --ignore-optional --ignore-scripts

build-prod-backend-only:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	# Build only backend services, skip dashboard
	NODE_ENV=production DEBUG=* yarn nx run-many --target=build --exclude=vite_react_shadcn_ts
	echo "Backend services built successfully. Dashboard build skipped due to Rollup issues."

build-docker:
	@echo "Building with Docker - no native dependency issues!"
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	# Simple install for Docker environment
	yarn install --frozen-lockfile --production=false
	-yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production yarn build
	@echo "Docker build completed successfully!"

build-prod-robust-windows:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-win32-x64-msvc --optional -W || npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Use PowerShell script to unlock files on Windows
	@echo "Unlocking Windows files before dashboard install..."
	-powershell -ExecutionPolicy Bypass -File scripts/unlock-windows-files.ps1 2>/dev/null || echo "PowerShell unlock script failed, continuing..."
	# Try dashboard install with multiple fallback strategies
	@echo "Installing dashboard dependencies..."
	cd apps/dashboard && (yarn install --ignore-optional --ignore-scripts || \
		(echo "First attempt failed, trying with cache clean..." && yarn cache clean && yarn install --ignore-optional --ignore-scripts) || \
		(echo "Second attempt failed, trying without lockfile..." && rm -f yarn.lock && yarn install --ignore-optional --ignore-scripts) || \
		echo "Dashboard dependency installation failed, but main build succeeded")
	@echo "Build process completed!"

# Alternative: Skip dashboard separate install since dependencies are in workspace
build-prod-skip-dashboard-install:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-win32-x64-msvc --optional -W || npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	@echo "Build completed successfully! Dashboard dependencies are managed by workspace."

# Unix/Linux specific build targets
build-prod-unix:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies for Unix/Linux
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Stop NX daemon and unlock files before dashboard install
	-yarn nx reset 2>/dev/null || echo "NX reset failed, continuing..."
	# Kill any remaining node processes that might hold file locks
	-pkill -f "node" 2>/dev/null || echo "No node processes to kill"
	# Wait a moment for file locks to be released
	sleep 2
	# Try dashboard install with retry mechanism
	cd apps/dashboard && (yarn install --ignore-optional --ignore-scripts || (echo "First attempt failed, retrying..." && sleep 3 && yarn install --ignore-optional --ignore-scripts))

build-prod-robust-unix:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies for Unix/Linux
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Use shell script to unlock files on Unix/Linux
	@echo "Unlocking Unix/Linux files before dashboard install..."
	-chmod +x scripts/unlock-unix-files.sh && ./scripts/unlock-unix-files.sh 2>/dev/null || echo "Unix unlock script failed, continuing..."
	# Try dashboard install with multiple fallback strategies
	@echo "Installing dashboard dependencies..."
	cd apps/dashboard && (yarn install --ignore-optional --ignore-scripts || \
		(echo "First attempt failed, trying with cache clean..." && yarn cache clean && yarn install --ignore-optional --ignore-scripts) || \
		(echo "Second attempt failed, trying without lockfile..." && rm -f yarn.lock && yarn install --ignore-optional --ignore-scripts) || \
		echo "Dashboard dependency installation failed, but main build succeeded")
	@echo "Build process completed!"

build-prod-debian:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	# Install system dependencies that might be needed on Debian
	@echo "Checking for required system dependencies..."
	-which lsof >/dev/null || echo "Warning: lsof not found. Install with: sudo apt-get install lsof"
	-which fuser >/dev/null || echo "Warning: fuser not found. Install with: sudo apt-get install psmisc"
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies for Debian/Linux
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Use comprehensive file unlocking for Debian
	@echo "Unlocking files using Debian-specific tools..."
	-chmod +x scripts/unlock-unix-files.sh && ./scripts/unlock-unix-files.sh 2>/dev/null || echo "Unix unlock script failed, continuing..."
	# Dashboard install with Debian-specific handling
	@echo "Installing dashboard dependencies..."
	cd apps/dashboard && (yarn install --ignore-optional --ignore-scripts || \
		(echo "First attempt failed, clearing caches..." && yarn cache clean && npm cache clean --force && yarn install --ignore-optional --ignore-scripts) || \
		(echo "Second attempt failed, removing lockfiles..." && rm -f yarn.lock package-lock.json && yarn install --ignore-optional --ignore-scripts) || \
		echo "Dashboard dependency installation failed, but main build succeeded")
	@echo "Debian build process completed!"

# Safe build that skips dashboard install for Unix/Linux
build-prod-safe-unix:
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies for Unix/Linux
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-linux-x64-gnu --optional -W || npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	# Dashboard dependencies are already satisfied from main install, skip separate install
	@echo "Build completed successfully. Dashboard dependencies installed with main project."

# Auto-detect platform and use appropriate build target
build-prod-auto:
	@echo "Auto-detecting platform..."
	@if [ "$(shell uname -s)" = "Linux" ]; then \
		if [ -f /etc/debian_version ]; then \
			echo "Detected Debian/Ubuntu system, using build-prod-debian"; \
			$(MAKE) build-prod-debian; \
		else \
			echo "Detected Linux system, using build-prod-unix"; \
			$(MAKE) build-prod-unix; \
		fi; \
	elif [ "$(shell uname -s)" = "Darwin" ]; then \
		echo "Detected macOS system, using build-prod-unix"; \
		$(MAKE) build-prod-unix; \
	elif [ "$(shell uname -o 2>/dev/null)" = "Msys" ] || [ "$(shell uname -o 2>/dev/null)" = "Cygwin" ]; then \
		echo "Detected Windows system, using build-prod-windows"; \
		$(MAKE) build-prod-windows; \
	else \
		echo "Unknown platform, using standard build-prod"; \
		$(MAKE) build-prod; \
	fi

# Test build without dashboard install to verify it works
test-build-prod:
	@echo "Testing production build without separate dashboard install..."
	cp .env.production .env.local
	cp .env.production .env
	cp .env.production apps/dashboard/.env.local
	yarn install --ignore-optional --ignore-scripts
	# Fix Rollup native dependencies directly
	chmod +x scripts/fix-rollup.sh && ./scripts/fix-rollup.sh || yarn add @rollup/rollup-win32-x64-msvc --optional -W || npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save || echo "Rollup fix failed, continuing..."
	yarn nx reset 2>/dev/null || echo "Skipping nx reset as nx is not installed"
	NODE_ENV=production DEBUG=* yarn build
	@echo "✅ Build completed successfully!"
	@echo "✅ Dashboard built by NX as 'vite_react_shadcn_ts'"
	@echo "✅ No separate dashboard install needed!"
	@if [ -d "apps/dashboard/dist" ]; then \
		echo "✅ Dashboard dist directory exists"; \
		ls -la apps/dashboard/dist/ | head -5; \
	else \
		echo "❌ Dashboard dist directory not found"; \
	fi

# Help target to show available build options
help:
	@echo "Available build targets:"
	@echo ""
	@echo "🚀 Main Build Targets:"
	@echo "  build-prod                    - Standard production build (fixed for all platforms)"
	@echo "  build-dev                     - Development build"
	@echo "  build-prod-auto               - Auto-detect platform and use appropriate build"
	@echo ""
	@echo "🪟 Windows-Specific Targets:"
	@echo "  build-prod-windows            - Windows build with file unlock handling"
	@echo "  build-prod-robust-windows     - Windows build with PowerShell unlock script"
	@echo "  build-prod-safe-windows       - Windows build without dashboard install"
	@echo ""
	@echo "🐧 Unix/Linux-Specific Targets:"
	@echo "  build-prod-unix               - Unix/Linux build with file unlock handling"
	@echo "  build-prod-robust-unix        - Unix/Linux build with shell unlock script"
	@echo "  build-prod-debian             - Debian-specific build with system dependency checks"
	@echo "  build-prod-safe-unix          - Unix/Linux build without dashboard install"
	@echo ""
	@echo "🧪 Testing & Alternative Targets:"
	@echo "  test-build-prod               - Test build to verify dashboard is built by NX"
	@echo "  build-prod-skip-dashboard-install - Skip dashboard install (legacy)"
	@echo "  build-prod-backend-only       - Build only backend services"
	@echo "  build-docker                  - Docker-optimized build"
	@echo ""
	@echo "🛠️ Utility Targets:"
	@echo "  clean                         - Clean all build artifacts and dependencies"
	@echo "  help                          - Show this help message"
	@echo ""
	@echo "💡 Recommended Usage:"
	@echo "  For most cases: make build-prod"
	@echo "  For auto-detection: make build-prod-auto"
	@echo "  For testing: make test-build-prod"
	@echo ""
	@echo "📝 Note: Dashboard is built by NX as 'vite_react_shadcn_ts' - no separate install needed!"

.PHONY: help
