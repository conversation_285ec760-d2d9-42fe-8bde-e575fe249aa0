# PowerShell script to switch between different environment configurations
# Usage: .\switch-env.ps1 [local|docker|dev|production]

param(
    [Parameter(Position=0)]
    [ValidateSet("local", "docker", "dev", "production", "status", "current", "show", "help")]
    [string]$Environment
)

$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectRoot = Split-Path -Parent $ScriptDir

# Function to display usage
function Show-Usage {
    Write-Host "Usage: .\switch-env.ps1 [local|docker|dev|production]" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "Environment modes:" -ForegroundColor Cyan
    Write-Host "  local      - All services running locally (localhost URLs)" -ForegroundColor White
    Write-Host "  docker     - Services running in Docker containers" -ForegroundColor White
    Write-Host "  dev        - Development environment (same as local)" -ForegroundColor White
    Write-Host "  production - Production environment" -ForegroundColor White
    Write-Host ""
    Write-Host "This script copies the appropriate .env file to .env" -ForegroundColor Gray
    exit 1
}

# Function to backup current .env
function Backup-Env {
    $envPath = Join-Path $ProjectRoot ".env"
    if (Test-Path $envPath) {
        $timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
        $backupPath = Join-Path $ProjectRoot ".env.backup.$timestamp"
        Copy-Item $envPath $backupPath
        Write-Host "✓ Backed up current .env file" -ForegroundColor Green
    }
}

# Function to copy environment file
function Copy-Env {
    param(
        [string]$EnvFile,
        [string]$EnvName
    )
    
    if (-not (Test-Path $EnvFile)) {
        Write-Host "❌ Environment file $EnvFile not found!" -ForegroundColor Red
        exit 1
    }
    
    Backup-Env
    $targetPath = Join-Path $ProjectRoot ".env"
    Copy-Item $EnvFile $targetPath
    Write-Host "✓ Switched to $EnvName environment" -ForegroundColor Green
    Write-Host "✓ Active environment file: $EnvFile" -ForegroundColor Green
}

# Function to show current environment
function Show-Current {
    Write-Host "Current environment configuration:" -ForegroundColor Cyan
    $envPath = Join-Path $ProjectRoot ".env"
    
    if (Test-Path $envPath) {
        $content = Get-Content $envPath
        
        $nodeEnv = ($content | Where-Object { $_ -match '^NODE_ENV=' }) -replace '^NODE_ENV=', ''
        Write-Host "NODE_ENV: $nodeEnv" -ForegroundColor White
        
        Write-Host "Service URLs:" -ForegroundColor White
        $serviceUrls = $content | Where-Object { $_ -match '^(SCHEDULER|COLLECTOR|PROCESSOR|WRITER)_.*URL=' } | Select-Object -First 4
        if ($serviceUrls) {
            $serviceUrls | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        } else {
            Write-Host "  No service URLs found" -ForegroundColor Gray
        }
        
        Write-Host "Database hosts:" -ForegroundColor White
        $dbHosts = $content | Where-Object { $_ -match '^(MYSQL|POSTGRES)_HOST=' }
        if ($dbHosts) {
            $dbHosts | ForEach-Object { Write-Host "  $_" -ForegroundColor Gray }
        } else {
            Write-Host "  No database hosts found" -ForegroundColor Gray
        }
    } else {
        Write-Host "❌ No .env file found" -ForegroundColor Red
    }
}

# Main logic
switch ($Environment) {
    "local" {
        $envFile = Join-Path $ProjectRoot ".env.local"
        Copy-Env $envFile "LOCAL"
        Write-Host ""
        Write-Host "🏠 LOCAL MODE ACTIVATED" -ForegroundColor Green
        Write-Host "   - All services running on localhost" -ForegroundColor White
        Write-Host "   - Databases on localhost" -ForegroundColor White
        Write-Host "   - Perfect for running services with npm/yarn" -ForegroundColor White
    }
    "docker" {
        $envFile = Join-Path $ProjectRoot ".env.docker"
        Copy-Env $envFile "DOCKER"
        Write-Host ""
        Write-Host "🐳 DOCKER MODE ACTIVATED" -ForegroundColor Blue
        Write-Host "   - Services using container names" -ForegroundColor White
        Write-Host "   - Databases in Docker containers" -ForegroundColor White
        Write-Host "   - Perfect for docker-compose development" -ForegroundColor White
    }
    "dev" {
        $envFile = Join-Path $ProjectRoot ".env.dev"
        Copy-Env $envFile "DEVELOPMENT"
        Write-Host ""
        Write-Host "🔧 DEVELOPMENT MODE ACTIVATED" -ForegroundColor Yellow
        Write-Host "   - Standard development configuration" -ForegroundColor White
        Write-Host "   - Services on localhost" -ForegroundColor White
    }
    "production" {
        $envFile = Join-Path $ProjectRoot ".env.production"
        Copy-Env $envFile "PRODUCTION"
        Write-Host ""
        Write-Host "🚀 PRODUCTION MODE ACTIVATED" -ForegroundColor Magenta
        Write-Host "   - Production configuration" -ForegroundColor White
        Write-Host "   - Container-based service URLs" -ForegroundColor White
    }
    { $_ -in @("status", "current", "show") } {
        Show-Current
    }
    "help" {
        Show-Usage
    }
    default {
        if ([string]::IsNullOrEmpty($Environment)) {
            Write-Host "❌ No environment specified" -ForegroundColor Red
            Write-Host ""
            Show-Current
            Write-Host ""
            Show-Usage
        } else {
            Write-Host "❌ Unknown environment: $Environment" -ForegroundColor Red
            Write-Host ""
            Show-Usage
        }
    }
}

Write-Host ""
Write-Host "📋 Quick commands:" -ForegroundColor Cyan
Write-Host "   npm run start:api     - Start API service" -ForegroundColor White
Write-Host "   npm run start:all     - Start all services" -ForegroundColor White
Write-Host "   docker-compose up     - Start with Docker" -ForegroundColor White
Write-Host ""
Write-Host "🔍 Check health: " -NoNewline -ForegroundColor Cyan
Write-Host "curl http://localhost:3000/api/health" -ForegroundColor Yellow
