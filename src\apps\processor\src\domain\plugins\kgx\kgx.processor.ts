import { Injectable, Logger } from "@nestjs/common";
import { v4 as uuidv4 } from "uuid";
import {
  getErrorMessage,
  getErrorStack,
  ProcessorType,
} from "@data-pipeline/core";
import { ProcessingStatus } from "@data-pipeline/storage";
import { KGX_CRYPTO_CONFIG } from "@data-pipeline/kgx";
import {
  Processor,
  ProcessorContext,
  ProcessorResult,
} from "../../processors/processor.interface";
import { ProcessorStep } from "../../entities/processing-pipeline.entity";
import { CryptoSymbolsConfig } from "./config/crypto-symbols-config";
import { DEFAULT_CURRENCY_WEIGHTS } from "./config/kgx-config";
import { MarketHoursService } from "./services/market-hours.service";
import { CryptoDataTransformer } from "./transformers/crypto-data-transformer";
import { DataTransformer } from "./transformers/data-transformer";
import { UsdIndexService } from "./services/usd-index.service";
import { KgxCalculatorService } from "./services/kgx-calculator.service";
import { KdxyCalculatorService } from "./services/kdxy-calculator.service";
import { HistoricalDataService } from "./services/historical-data.service";
import { KgxConfig } from "./config/kgx-config";

/**
 * Unified Processor for KGX calculations on multiple asset types
 * This processor calculates KGX values for both cryptocurrencies and precious metals
 * using already-collected data from the unified collection system
 */
@Injectable()
export class KgxProcessor implements Processor {
  private readonly logger = new Logger(KgxProcessor.name);
  private readonly cryptoConfig = KGX_CRYPTO_CONFIG.crypto;
  private readonly forexConfig = KGX_CRYPTO_CONFIG.forex;

  // Cache for KGX formula configuration
  private formulaConfigCache: any = null;
  private formulaCacheTimestamp: number = 0;
  private readonly formulaCacheTtlMs: number = 30 * 60 * 1000; // 30 minutes TTL

  // ✅ NEW: USD Index caching to prevent duplicate API calls
  private usdIndexCache: {
    current: number | null;
    previous: number | null;
    timestamp: number | null;
    processingId: string | null;
  } = {
      current: null,
      previous: null,
      timestamp: null,
      processingId: null,
    };
  private readonly usdIndexCacheTtlMs: number = 5 * 60 * 1000; // 5 minutes TTL

  // ✅ NEW: Market Hours caching to prevent duplicate calls during same processing run
  private marketHoursCache: {
    isMarketOpen: boolean | null;
    marketStatus: any | null;
    timestamp: number | null;
    processingId: string | null;
  } = {
      isMarketOpen: null,
      marketStatus: null,
      timestamp: null,
      processingId: null,
    };
  private readonly marketHoursCacheTtlMs: number = 1 * 60 * 1000; // 1 minute TTL (market status changes slowly)

  constructor(
    private readonly marketHoursService: MarketHoursService,
    private readonly cryptoSymbolsConfig: CryptoSymbolsConfig,
    private readonly cryptoDataTransformer: CryptoDataTransformer,
    private readonly DataTransformer: DataTransformer,
    private readonly usdIndexService: UsdIndexService,
    private readonly kgxCalculator: KgxCalculatorService,
    private readonly kdxyCalculator: KdxyCalculatorService,
    private readonly kgxConfig: KgxConfig,
    private readonly historicalDataService: HistoricalDataService
  ) {
    this.logger.log(
      "Unified KGX Processor initialized with multi-asset support"
    );
  }

  /**
   * ✅ CACHE MANAGEMENT: Clear USD index cache
   * Useful for testing or when processing different data sets
   */
  private clearUsdIndexCache(): void {
    this.usdIndexCache = {
      current: null,
      previous: null,
      timestamp: null,
      processingId: null,
    };
    this.logger.debug("USD index cache cleared");
  }

  /**
   * ✅ CACHE MANAGEMENT: Clear market hours cache
   * Useful for testing or when processing different data sets
   */
  private clearMarketHoursCache(): void {
    this.marketHoursCache = {
      isMarketOpen: null,
      marketStatus: null,
      timestamp: null,
      processingId: null,
    };
    this.logger.debug("Market hours cache cleared");
  }

  /**
   * ✅ CACHE MANAGEMENT: Clear all caches
   * Useful for testing or when processing different data sets
   */
  private clearAllCaches(): void {
    this.clearUsdIndexCache();
    this.clearMarketHoursCache();
    this.logger.debug("All caches cleared");
  }

  /**
   * ✅ OPTIMIZED: Get market hours with caching to prevent duplicate API calls
   * Uses cache to avoid multiple calls to market hours service during same processing run
   */
  private getMarketHoursWithCache(processingId?: string): [boolean, any] {
    const now = Date.now();

    // ✅ CHECK CACHE FIRST: Avoid duplicate market hours checks for same processing run
    const isCacheValid =
      this.marketHoursCache.isMarketOpen !== null &&
      this.marketHoursCache.marketStatus !== null &&
      this.marketHoursCache.timestamp !== null &&
      now - this.marketHoursCache.timestamp < this.marketHoursCacheTtlMs &&
      (processingId
        ? this.marketHoursCache.processingId === processingId
        : true);

    if (isCacheValid) {
      this.logger.debug(
        `✅ MARKET HOURS CACHE HIT: Using cached market status - ` +
        `${this.marketHoursCache.isMarketOpen ? "OPEN" : "CLOSED"} ` +
        `(age: ${Math.round(
          (now - this.marketHoursCache.timestamp!) / 1000
        )}s)`
      );
      return [
        this.marketHoursCache.isMarketOpen!,
        this.marketHoursCache.marketStatus!,
      ];
    }

    // ✅ CACHE MISS: Fetch fresh market hours data
    this.logger.debug("Market hours cache miss - fetching fresh market status");
    const [isMarketOpen, marketStatus] = this.marketHoursService.isMarketOpen();

    // ✅ STORE IN CACHE: Cache the fetched values to prevent duplicate calls
    this.marketHoursCache = {
      isMarketOpen,
      marketStatus,
      timestamp: now,
      processingId: processingId || null,
    };

    this.logger.debug(
      `✅ MARKET HOURS CACHE STORED: Market status cached - ` +
      `${isMarketOpen ? "OPEN" : "CLOSED"
      } for future use in same processing run`
    );

    return [isMarketOpen, marketStatus];
  }

  /**
   * Get the KGX formula configuration from cache or load from database if needed
   * @returns KGX formula configuration
   */
  private async getFormulaConfiguration(): Promise<any> {
    const now = Date.now();

    if (
      this.formulaConfigCache &&
      now - this.formulaCacheTimestamp < this.formulaCacheTtlMs
    ) {
      this.logger.debug("Using cached KGX formula configuration");
      return this.formulaConfigCache;
    }

    try {
      this.logger.log("Loading KGX formula configuration from cache");
      const config = await this.kgxConfig.getConfig();

      this.formulaConfigCache = config;
      this.formulaCacheTimestamp = now;

      this.logger.log(
        `Loaded and cached KGX formula configuration: ${JSON.stringify(config)}`
      );
      return config;
    } catch (error) {
      this.logger.error(
        `Error loading KGX formula configuration: ${getErrorMessage(error)}`,
        getErrorStack(error as Error)
      );
      throw error;
    }
  }

  /**
   * Check if this processor can handle the given step
   * @param step Processor step
   * @returns True if this processor can handle the step
   */
  canHandle(step: ProcessorStep): boolean {
    if (typeof step.type === "string") {
      return step.type.toUpperCase() === ProcessorType.KGX.toUpperCase();
    }
    return step.type === ProcessorType.KGX;
  }

  /**
   * Validate the step configuration
   * @param step Processor step
   * @returns True if the configuration is valid
   */
  validateConfig(step: ProcessorStep): boolean {
    if (!step.config) {
      return false;
    }

    const { usdIndexSource } = step.config;
    if (!usdIndexSource) {
      this.logger.warn("Missing USD index source configuration");
      return false;
    }

    if (usdIndexSource.type === "forex") {
      const { forexSource } = step.config;
      if (!forexSource) {
        this.logger.warn(
          "Missing forex source configuration for USD index calculation"
        );
        return false;
      }
    }

    if (step.config.formulaType === "custom" && step.config.customFormula) {
      try {
        new Function(
          "price",
          "currentUsdIndex",
          "previousUsdIndex",
          "tradingWeight",
          `return ${step.config.customFormula};`
        );
      } catch (error) {
        this.logger.warn(
          `Invalid custom formula: ${step.config.customFormula
          }, error: ${getErrorMessage(error)}`
        );
        return false;
      }
    }

    return true;
  }

  /**
   * Process the data using the Unified KGX processor for multiple asset types
   * @param data Input data
   * @param step Processing step configuration
   * @param context Processing context
   * @returns Processing result
   */
  async process(
    data: any,
    step: ProcessorStep,
    context: ProcessorContext
  ): Promise<ProcessorResult> {
    const startTime = Date.now();
    const processingId = context.processingId || uuidv4();

    // ✅ CACHE MANAGEMENT: Clear cache for different processing sessions
    if (
      (this.usdIndexCache.processingId &&
        this.usdIndexCache.processingId !== processingId) ||
      (this.marketHoursCache.processingId &&
        this.marketHoursCache.processingId !== processingId)
    ) {
      this.logger.debug(
        `New processing session detected (${processingId}), clearing all caches`
      );
      this.clearAllCaches();
    }

    try {
      this.logger.debug(`Processing with context: ${JSON.stringify(context)}`);

      // 1. Check market hours status FIRST (before any processing)
      const [isMarketOpen, marketStatus] = this.getMarketHoursWithCache(processingId);

      this.logger.log(
        `Market status: ${isMarketOpen ? "OPEN" : "CLOSED"} - ${JSON.stringify(
          marketStatus
        )}`
      );

      // const isUnifiedFormat = data.primary || data.additional;

      // if (!isUnifiedFormat) {
      //   // Fall back to legacy crypto-only processing
      //   this.logger.debug("Processing legacy format data - crypto only");
      //   return await this.processLegacyCryptoOnly(
      //     data,
      //     step,
      //     context,
      //     startTime,
      //     processingId,
      //     isMarketOpen,
      //     marketStatus
      //   );
      // }

      this.logger.debug("Processing KGX format data");

      const allExtractedData = await this.DataTransformer.extractAllData(data);

      this.logger.log(
        `Extracted ${allExtractedData.summary.totalRecords} total records across ` +
        `${allExtractedData.summary.dataTypesFound.length
        } data types: ${allExtractedData.summary.dataTypesFound.join(", ")}`
      );

      const preciousMetalsData = allExtractedData.preciousMetals;
      const cryptoData = allExtractedData.crypto;
      const forexData = allExtractedData.forex;
      const marketIndicesData = allExtractedData.marketIndices;

      // 2. Get USD index values from already-collected data (informed by market status)
      const { usdIndex, prevUsdIndex, changePercent } =
        await this.getUsdIndexFromCollectedData(
          forexData,
          marketIndicesData,
          step.config,
          isMarketOpen,
          marketStatus,
          processingId
        );

      this.logger.log(
        `Using USD index from collected data - current: ${usdIndex}, previous: ${prevUsdIndex}, changePercent: ${changePercent} ` +
        `(market ${isMarketOpen ? "OPEN" : "CLOSED"})`
      );

      // Get KGX formula configuration from cache or database
      const kgxFormulaConfig = await this.getFormulaConfiguration();
      this.logger.debug(
        `Using KGX formula configuration: ${JSON.stringify(kgxFormulaConfig)}`
      );

      // Merge configuration with step config (step config takes precedence)
      if (kgxFormulaConfig) {
        step.config = {
          ...(step.config || {}),
          formulaType:
            kgxFormulaConfig.formulaType ||
            step.config?.formulaType ||
            "standard",
          customFormula:
            kgxFormulaConfig.customFormula || step.config?.customFormula || "",
          tradingWeight:
            kgxFormulaConfig.tradingWeight ||
            step.config?.tradingWeight ||
            0.001,
          currencyWeights:
            kgxFormulaConfig.currencyWeights || step.config?.currencyWeights,
        };
      }

      // 3. Process all asset types
      const processedAssets: {
        crypto: any[];
        preciousMetals: any[];
        cryptoMetadata?: any;
        preciousMetalsMetadata?: any;
      } = {
        crypto: [],
        preciousMetals: [],
      };

      // Process crypto assets
      if (cryptoData && cryptoData.length > 0) {
        this.logger.log(`Processing ${cryptoData.length} crypto assets`);

        const cryptoResult = await this.kgxCalculator.calculateKgxValues(
          cryptoData,
          usdIndex,
          prevUsdIndex,
          isMarketOpen,
          marketStatus?.isWeekend || false,
          step.config
        );

        processedAssets.crypto = cryptoResult.results;
        // Store crypto metadata for later use
        processedAssets.cryptoMetadata = cryptoResult.metadata;
      }

      // Process precious metals
      if (preciousMetalsData && preciousMetalsData.length > 0) {
        this.logger.log(
          `Processing ${preciousMetalsData.length} precious metals assets`
        );

        // Transform precious metals data to crypto-compatible format for KGX calculation
        const transformedPreciousMetals =
          this.transformPreciousMetalsForKgx(preciousMetalsData);

        const preciousMetalsResult =
          await this.kgxCalculator.calculateKgxValues(
            transformedPreciousMetals,
            usdIndex,
            prevUsdIndex,
            isMarketOpen,
            marketStatus?.isWeekend || false,
            step.config
          );

        processedAssets.preciousMetals = preciousMetalsResult.results;
        // Store precious metals metadata for later use
        processedAssets.preciousMetalsMetadata = preciousMetalsResult.metadata;
      }

      // Combine all processed data
      const processedData = [
        ...processedAssets.crypto,
        ...processedAssets.preciousMetals,
      ];

      const inputCount =
        (cryptoData?.length || 0) + (preciousMetalsData?.length || 0);
      const outputCount = processedData.length;
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.debug(
        `Unified KGX processing completed in ${duration}ms: ${inputCount} input records, ${outputCount} output records`
      );

      // // 5. Store USD index values
      // await this.usdIndexService.storeUsdIndexValues(
      //   usdIndex,
      //   prevUsdIndex,
      //   context.collectorId
      // );

      // Get formula information for metadata
      const formulaType = step.config?.formulaType || "standard";
      const customFormula = step.config?.customFormula || "";
      const tradingWeight = step.config?.tradingWeight || 0;
      const currencyWeights =
        step.config?.currencyWeights || DEFAULT_CURRENCY_WEIGHTS;

      // Create formula information object for the metadata
      const formulaInfo = {
        type: formulaType,
        expression:
          formulaType === "custom"
            ? customFormula
            : this.getFormulaExpressionForType(formulaType),
        tradingWeight: tradingWeight,
        parameters: {
          currentUsdIndex: usdIndex,
          previousUsdIndex: prevUsdIndex,
          referenceDate: new Date().toISOString(),
          currencyWeights: currencyWeights,
        },
      };

      // 6. Create the result object with ALL properties preserved
      // Use property names that match the transformation mapping in pipeline.json
      // Get the weekend status for the current time
      const isWeekend = this.marketHoursService.isWeekend();

      const enhancedProcessedData = processedData.map((item) => ({
        ...item,
        price_usd: item.price,
        kgx_value: item.kgx_value,
        usd_index: usdIndex,
        prev_usd_index: prevUsdIndex,
        usd_ratio: parseFloat((usdIndex / prevUsdIndex).toFixed(8)),
        change_due_usd: item.change_due_usd,
        change_due_usd_percent: item.change_due_usd_percent,
        change_due_trade: item.change_due_trade,
        change_due_trade_percent: item.change_due_trade_percent,
        total_change: item.total_change,
        total_change_percent: item.total_change_percent,
        market_status: item.market_status,
        is_weekend: item.is_weekend || isWeekend,
        is_market_closed: item.is_market_closed || !isMarketOpen,
        timestamp: item.timestamp || new Date().toISOString(),
      }));

      const result = {
        status: ProcessingStatus.SUCCESS,
        data: enhancedProcessedData,
        output_data: enhancedProcessedData,
        metadata: {
          timestamp: new Date(),
          duration_ms: duration,
          input_record_count: inputCount,
          output_record_count: outputCount,
          usd_index: usdIndex,
          prev_usd_index: prevUsdIndex,
          usd_ratio: parseFloat((usdIndex / prevUsdIndex).toFixed(8)),
          crypto_symbols: this.cryptoSymbolsConfig.getSymbols(),
          precious_metals_symbols: processedAssets.preciousMetals.map(
            (pm) => pm.symbol
          ),
          kgx_config: step.config,
          processing_id: processingId,
          processor_type: "kgx",
          // data_format: isUnifiedFormat ? "unified_kgx" : "legacy",
          // ✅ ENHANCED: Include rich KGX calculation metadata
          kgx_calculation_metadata: {
            crypto: processedAssets.cryptoMetadata || {},
            precious_metals: processedAssets.preciousMetalsMetadata || {},
            combined_quality_score: this.calculateCombinedQualityScore(
              processedAssets.cryptoMetadata,
              processedAssets.preciousMetalsMetadata
            ),
          },
          // Include unified data extraction summary if available
          ...(allExtractedData && {
            unified_data_summary: allExtractedData.summary,
            extracted_data_types: {
              crypto_count: allExtractedData.crypto.length,
              precious_metals_count: allExtractedData.preciousMetals.length,
              forex_count: allExtractedData.forex.length,
              market_indices_count: allExtractedData.marketIndices.length,
            },
          }),
          calculation: {
            formula: formulaInfo.expression,
            formula_type: formulaType,
            usd_index_formula: "Current USD Index / Previous USD Index",
            usd_ratio: parseFloat((usdIndex / prevUsdIndex).toFixed(8)),
            usd_change_percent: parseFloat(
              ((usdIndex / prevUsdIndex - 1) * 100).toFixed(4)
            ),
            trading_weight: tradingWeight,
            market_hours: {
              status: isMarketOpen ? "OPEN" : "CLOSED",
              is_weekend: isWeekend,
              forex_config: this.forexConfig,
              crypto_config: this.cryptoConfig,
            },
            sample_calculation:
              processedData.length > 0
                ? {
                  symbol: processedData[0].symbol,
                  price: processedData[0].price,
                  kgx_value: processedData[0].kgx_value,
                  calculation: this.getCalculationFormula(
                    processedData[0],
                    usdIndex,
                    prevUsdIndex,
                    formulaType
                  ),
                }
                : null,
          },
          formula: formulaInfo,
        },
        processed_crypto_data: processedData.map((item) => ({
          symbol: item.symbol,
          price: item.price,
          calculated_value: item.kgx_value,
          metadata: {
            usd_index: usdIndex,
            prev_usd_index: prevUsdIndex,
            usd_effect: item.usd_effect,
            trading_effect: item.trading_effect,
            total_change: item.total_change,
          },
        })),
        // Include all extracted data for reference if using unified format
        ...(allExtractedData && {
          all_extracted_data: {
            crypto: allExtractedData.crypto,
            precious_metals: allExtractedData.preciousMetals,
            forex: allExtractedData.forex,
            market_indices: allExtractedData.marketIndices,
          },
        }),
      };

      return result;
    } catch (error) {
      const endTime = Date.now();
      const duration = endTime - startTime;

      this.logger.error(
        `Error processing data with KGX Crypto processor: ${getErrorMessage(
          error
        )}`,
        getErrorStack(error as Error)
      );

      // Create the error result
      return {
        status: ProcessingStatus.FAILURE,
        data: null,
        metadata: {
          timestamp: new Date(),
          duration_ms: duration,
          error: {
            message: getErrorMessage(error),
            stack: getErrorStack(error as Error),
          },
          processing_id: processingId,
        },
      };
    }
  }

  /**
   * Get the formula expression based on formula type
   * @param formulaType The type of formula
   * @returns Formula expression
   */
  private getFormulaExpressionForType(formulaType: string): string {
    switch (formulaType) {
      case "standard":
      default:
        return "price / (currentUsdIndex / previousUsdIndex)";
    }
  }

  /**
   * Generate a sample calculation string based on the formula type
   * @param cryptoData Crypto data
   * @param usdIndex Current USD index
   * @param prevUsdIndex Previous USD index
   * @param formulaType Formula type
   * @returns Sample calculation string
   */
  private getCalculationFormula(
    cryptoData: any,
    usdIndex: number,
    prevUsdIndex: number,
    formulaType: string
  ): string {
    const price = cryptoData.price;
    const kgxValue = cryptoData.kgx_value;

    switch (formulaType) {
      case "custom":
        return `Custom formula calculation result = ${kgxValue.toFixed(6)}`;
      case "standard":
      default:
        return `${price.toFixed(6)} / (${usdIndex.toFixed(
          6
        )} / ${prevUsdIndex.toFixed(6)}) = ${kgxValue.toFixed(6)}`;
    }
  }

  // /**
  //  * Process legacy crypto-only data format
  //  */
  // private async processLegacyCryptoOnly(
  //   data: any,
  //   step: ProcessorStep,
  //   context: ProcessorContext,
  //   startTime: number,
  //   processingId: string,
  //   isMarketOpen: boolean,
  //   marketStatus: any
  // ): Promise<ProcessorResult> {
  //   this.logger.log("Processing legacy crypto-only data format");

  //   const cryptoData = await this.cryptoDataTransformer.extractCryptoData(data);
  //   const inputCount = cryptoData.length;

  //   const kgxFormulaConfig = await this.getFormulaConfiguration();
  //   if (kgxFormulaConfig) {
  //     step.config = { ...(step.config || {}), ...kgxFormulaConfig };
  //   }

  //   const usdIndexSource = step.config?.usdIndexSource || { type: "forex" };
  //   const usdIndex = await this.usdIndexService.getCurrentUsdIndex(
  //     usdIndexSource
  //   );
  //   const prevUsdIndex = await this.usdIndexService.getPreviousUsdIndex(
  //     step.config
  //   );

  //   const cryptoResult = await this.kgxCalculator.calculateKgxValues(
  //     cryptoData,
  //     usdIndex,
  //     prevUsdIndex,
  //     isMarketOpen,
  //     marketStatus?.isWeekend || false,
  //     step.config
  //   );

  //   const processedData = cryptoResult.results;

  //   await this.usdIndexService.storeUsdIndexValues(
  //     usdIndex,
  //     prevUsdIndex,
  //     context.collectorId
  //   );

  //   return {
  //     status: ProcessingStatus.SUCCESS,
  //     data: processedData,
  //     metadata: {
  //       timestamp: new Date(),
  //       duration_ms: Date.now() - startTime,
  //       input_record_count: inputCount,
  //       output_record_count: processedData.length,
  //       processing_mode: "legacy_crypto_only",
  //       // ✅ ENHANCED: Include rich KGX metadata from calculator
  //       kgx_calculation_metadata: cryptoResult.metadata,
  //     },
  //   };
  // }

  /**
   * Extract USD index from collected data with ENHANCED market indices support
   * Now uses intelligent method selection to determine the best USD index source
   * ✅ OPTIMIZED: Uses caching to prevent duplicate API calls during same processing run
   */
  private async getUsdIndexFromCollectedData(
    forexData: any[],
    marketIndicesData: any[],
    config: any,
    isMarketOpen: boolean,
    marketStatus: any,
    processingId?: string
  ): Promise<{
    usdIndex: number;
    prevUsdIndex: number;
    changePercent: string;
  }> {
    const marketStatusInfo = isMarketOpen ? "OPEN" : "CLOSED";

    // ✅ CHECK CACHE FIRST: Avoid duplicate API calls for same processing run
    const now = Date.now();
    const isCacheValid =
      this.usdIndexCache.current !== null &&
      this.usdIndexCache.previous !== null &&
      this.usdIndexCache.timestamp !== null &&
      now - this.usdIndexCache.timestamp < this.usdIndexCacheTtlMs &&
      (processingId ? this.usdIndexCache.processingId === processingId : true);

    if (isCacheValid) {
      this.logger.log(
        `✅ CACHE HIT: Using cached USD index - current: ${this.usdIndexCache.current}, ` +
        `previous: ${this.usdIndexCache.previous} (age: ${Math.round(
          (now - this.usdIndexCache.timestamp!) / 1000
        )}s, ` +
        `market ${marketStatusInfo})`
      );
      return {
        usdIndex: this.usdIndexCache.current!,
        prevUsdIndex: this.usdIndexCache.previous!,
        changePercent: (
          (this.usdIndexCache.current! / this.usdIndexCache.previous! - 1) *
          100
        ).toFixed(4),
      };
    }

    this.logger.debug(
      `Getting USD index with market status: ${marketStatusInfo} (cache miss)`
    );

    // ✅ NEW: Use intelligent method selection to determine the best USD index source
    const methodSelection = await this.kgxCalculator.getUsdIndexMethod(forexData);
    this.logger.log(
      `🔍 USD INDEX METHOD SELECTION: ${methodSelection.source} (${methodSelection.reason})`
    );

    let usdIndex: number;
    let prevUsdIndex: number;

    // ✅ INTELLIGENT ROUTING: Route to appropriate USD index source based on method selection
    switch (methodSelection.source) {
      case "KDXY_CALCULATED":
        this.logger.log(
          `📊 USING KDXY CALCULATION: Method ${methodSelection.method}, ID ${methodSelection.usdIndexId}`
        );
        ({ usdIndex, prevUsdIndex } = await this.getUsdIndexViaKdxyCalculation(
          forexData,
          marketStatusInfo,
          methodSelection,
          now,
          processingId
        ));
        break;

      case "FEED_BASED":
        this.logger.log(
          `📡 USING FEED-BASED: Method ${methodSelection.method}, ID ${methodSelection.usdIndexId}`
        );
        ({ usdIndex, prevUsdIndex } = await this.getUsdIndexViaFeedBased(
          marketIndicesData,
          marketStatusInfo,
          methodSelection,
          now,
          processingId
        ));
        break;

      case "HISTORICAL_FALLBACK":
        this.logger.warn(
          `📚 USING HISTORICAL FALLBACK: Method ${methodSelection.method}, ID ${methodSelection.usdIndexId}`
        );
        ({ usdIndex, prevUsdIndex } =
          await this.getUsdIndexViaHistoricalFallback(
            config,
            marketStatusInfo,
            methodSelection,
            now,
            processingId
          ));
        break;

      default:
        throw new Error(`Unknown USD index source: ${methodSelection.source}`);
    }

    // ✅ VALIDATION: Ensure we got valid USD index values
    if (!usdIndex || usdIndex <= 0) {
      throw new Error(
        `Invalid current USD index from ${methodSelection.source}: ${usdIndex} (market ${marketStatusInfo})`
      );
    }
    if (!prevUsdIndex || prevUsdIndex <= 0) {
      throw new Error(
        `Invalid previous USD index from ${methodSelection.source}: ${prevUsdIndex} (market ${marketStatusInfo})`
      );
    }

    // Log the final result
    const changePercent = ((usdIndex / prevUsdIndex - 1) * 100).toFixed(4);
    this.logger.log(
      `🎯 FINAL USD INDEX VALUES (${methodSelection.source
      }) - Current: ${usdIndex.toFixed(4)}, Previous: ${prevUsdIndex.toFixed(
        4
      )}, Change: ${changePercent}% (market ${marketStatusInfo})`
    );

    // ✅ STORE IN CACHE: Cache the fetched values to prevent duplicate API calls
    this.usdIndexCache = {
      current: usdIndex,
      previous: prevUsdIndex,
      timestamp: now,
      processingId: processingId || null,
    };

    this.logger.log(
      `✅ CACHE STORED: USD index cached for future use in same processing run - ` +
      `current: ${usdIndex}, previous: ${prevUsdIndex} (market ${marketStatusInfo})`
    );

    return { usdIndex, prevUsdIndex, changePercent };
  }

  /**
   * Calculate combined quality score from crypto and precious metals metadata
   */
  private calculateCombinedQualityScore(
    cryptoMetadata?: any,
    preciousMetalsMetadata?: any
  ): number {
    const scores: number[] = [];

    if (cryptoMetadata?.quality_metrics) {
      scores.push(
        cryptoMetadata.quality_metrics.data_freshness_score || 0,
        cryptoMetadata.quality_metrics.calculation_accuracy_score || 0,
        cryptoMetadata.quality_metrics.market_context_score || 0
      );
    }

    if (preciousMetalsMetadata?.quality_metrics) {
      scores.push(
        preciousMetalsMetadata.quality_metrics.data_freshness_score || 0,
        preciousMetalsMetadata.quality_metrics.calculation_accuracy_score || 0,
        preciousMetalsMetadata.quality_metrics.market_context_score || 0
      );
    }

    if (scores.length === 0) return 0;

    // Calculate weighted average (all scores equally weighted)
    const averageScore =
      scores.reduce((sum, score) => sum + score, 0) / scores.length;
    return Math.round(averageScore * 100) / 100; // Round to 2 decimal places
  }

  /**
   * Transform precious metals for KGX calculation
   */
  private transformPreciousMetalsForKgx(preciousMetalsData: any[]): any[] {
    return preciousMetalsData.map((metal) => ({
      symbol: metal.symbol,
      price: metal.price || metal.mid || metal.bid,
      timestamp: metal.timestamp,
      high: metal.high,
      low: metal.low,
      open: metal.open || metal.price,
      close: metal.price || metal.mid,
      change: metal.change,
      change_percent: metal.change_percent,
      asset_type: "precious_metals",
      original_data: metal,
    }));
  }

  /**
   * Transform forex data for KDXY calculation
   *
   * CRITICAL FIX: Based on actual data analysis, the field meanings are:
   * - For EUR/GBP: usd_to_c contains the correct X/USD rates (EUR/USD, GBP/USD)
   * - For JPY/CAD/CHF: cto_usd contains the correct USD/X rates (USD/JPY, USD/CAD, USD/CHF)
   *
   * KDXY Formula requires these specific rates:
   * - EUR/USD: ~1.08-1.09 (use usd_to_c field)
   * - USD/JPY: ~142.565 (use cto_usd field directly)
   * - GBP/USD: ~1.27-1.35 (use usd_to_c field)
   * - USD/CAD: ~1.374 (use cto_usd field directly)
   * - USD/SEK: ~10.5 (use cto_usd field directly if available)
   * - USD/CHF: ~0.8212 (use cto_usd field directly)
   */
  private transformForexDataForKdxy(forexData: any[]): Record<string, number> {
    const rates: Record<string, number> = {};

    // Define required currencies for KDXY calculation
    const REQUIRED_CURRENCIES = ["EUR", "JPY", "GBP", "CAD", "SEK", "CHF"];
    const SUPPORTED_CURRENCIES = new Set(REQUIRED_CURRENCIES);

    this.logger.debug("Transforming forex data for KDXY calculation:");

    for (const forex of forexData) {
      const symbol = forex.symbol;
      let rate: number;

      // Log the raw forex data for debugging
      this.logger.debug(
        `${symbol}: cto_usd=${forex.cto_usd}, usd_to_c=${forex.usd_to_c}, bid=${forex.bid}`
      );

      if (symbol === "EUR" || symbol === "GBP") {
        // ✅ FIXED: For EUR/USD and GBP/USD, use usd_to_c field (contains X/USD rates)
        rate = forex.usd_to_c || forex.bid || 1;
        this.logger.debug(`${symbol}/USD rate: ${rate} (using usd_to_c field)`);
      } else if (
        symbol === "JPY" ||
        symbol === "CAD" ||
        symbol === "SEK" ||
        symbol === "CHF"
      ) {
        // ✅ FIXED: For USD/JPY, USD/CAD, USD/SEK, USD/CHF, use cto_usd field directly
        rate = forex.cto_usd || forex.bid || 1;
        this.logger.debug(
          `USD/${symbol} rate: ${rate} (using cto_usd field directly)`
        );
      } else {
        // ❌ STRICT: Reject unsupported currencies instead of using fallback
        if (!SUPPORTED_CURRENCIES.has(symbol)) {
          const errorMessage =
            `Unsupported currency '${symbol}' for KDXY calculation. ` +
            `Required currencies: ${REQUIRED_CURRENCIES.join(", ")}. ` +
            `ALL ${REQUIRED_CURRENCIES.length} currencies are required for KDXY calculation.`;

          this.logger.error(errorMessage);
          throw new Error(errorMessage);
        }

        // This should never be reached due to the exhaustive if/else above
        throw new Error(
          `Logical error: Currency '${symbol}' is supported but not handled in transformation logic`
        );
      }

      // Validate rate is a valid number
      if (!rate || isNaN(rate) || rate <= 0) {
        throw new Error(
          `Invalid forex rate for ${symbol}: ${rate}. All rates must be positive numbers.`
        );
      }

      rates[symbol] = rate;
    }

    // Log the final rates for KDXY calculation
    this.logger.log("KDXY Forex Rates (CORRECTED):");
    Object.entries(rates).forEach(([currency, rate]) => {
      this.logger.log(`  ${currency}: ${rate.toFixed(6)}`);
    });

    // ✅ VALIDATION: Ensure all required currencies are present
    const missingCurrencies = REQUIRED_CURRENCIES.filter(
      (currency) => !rates[currency]
    );
    if (missingCurrencies.length > 0) {
      const errorMessage =
        `CRITICAL: Missing required currencies for KDXY calculation: ${missingCurrencies.join(
          ", "
        )}. ` +
        `Required currencies: ${REQUIRED_CURRENCIES.join(", ")}. ` +
        `Available currencies: ${Object.keys(rates).join(", ")}.`;

      this.logger.error(errorMessage);
      throw new Error(errorMessage);
    }

    // ✅ VALIDATION: Check if rates are in realistic ranges
    if (rates.EUR && (rates.EUR < 0.8 || rates.EUR > 1.5)) {
      this.logger.warn(
        `EUR/USD rate ${rates.EUR} seems unrealistic (expected ~1.08-1.09)`
      );
    }
    if (rates.JPY && (rates.JPY < 100 || rates.JPY > 200)) {
      this.logger.warn(
        `USD/JPY rate ${rates.JPY} seems unrealistic (expected ~140-150)`
      );
    }
    if (rates.GBP && (rates.GBP < 1.0 || rates.GBP > 2.0)) {
      this.logger.warn(
        `GBP/USD rate ${rates.GBP} seems unrealistic (expected ~1.25-1.35)`
      );
    }
    if (rates.CAD && (rates.CAD < 1.0 || rates.CAD > 2.0)) {
      this.logger.warn(
        `USD/CAD rate ${rates.CAD} seems unrealistic (expected ~1.35-1.40)`
      );
    }
    if (rates.CHF && (rates.CHF < 0.7 || rates.CHF > 1.2)) {
      this.logger.warn(
        `USD/CHF rate ${rates.CHF} seems unrealistic (expected ~0.80-0.90)`
      );
    }

    return rates;
  }

  /**
   * Get USD index via KDXY calculation from forex data
   */
  private async getUsdIndexViaKdxyCalculation(
    forexData: any[],
    marketStatusInfo: string,
    methodSelection: any,
    now: number,
    processingId?: string
  ): Promise<{ usdIndex: number; prevUsdIndex: number }> {
    if (!forexData || forexData.length === 0) {
      throw new Error(
        `KDXY calculation requires forex data, but none available (market ${marketStatusInfo})`
      );
    }

    this.logger.log(
      `Calculating KDXY from ${forexData.length} forex rates (market ${marketStatusInfo})`
    );

    // Transform and calculate KDXY
    const forexRates = this.transformForexDataForKdxy(forexData);
    const usdIndex = await this.kdxyCalculator.calculateKdxy(forexRates);
    this.logger.log(
      `Calculated live KDXY: ${usdIndex} (market ${marketStatusInfo})`
    );

    // Get historical KDXY from database
    const prevUsdIndex = await this.historicalDataService.getCloseKdxy(
      methodSelection.method
    );
    this.logger.log(
      `Fetched previous close KDXY: ${prevUsdIndex} using method ${methodSelection.method} (market ${marketStatusInfo})`
    );

    if (!prevUsdIndex || prevUsdIndex <= 0) {
      throw new Error(
        `CRITICAL: Cannot proceed with KGX calculation - previous USD index is missing or invalid. ` +
        `Previous USD index: ${prevUsdIndex}. Check historical data sources.`
      );
    }

    return { usdIndex, prevUsdIndex };
  }

  /**
   * Get USD index via feed-based source from market indices data
   */
  private async getUsdIndexViaFeedBased(
    marketIndicesData: any[],
    marketStatusInfo: string,
    methodSelection: any,
    now: number,
    processingId?: string
  ): Promise<{ usdIndex: number; prevUsdIndex: number }> {
    if (!marketIndicesData || marketIndicesData.length === 0) {
      throw new Error(
        `Feed-based USD index requires market indices data, but none available (market ${marketStatusInfo})`
      );
    }

    this.logger.log(
      `Processing market indices data with ${marketIndicesData.length} records for feed-based USD index (market ${marketStatusInfo})`
    );

    // Look for current and previous USDX data
    const currentUsdx = marketIndicesData.find(
      (index) => index.symbol === "USDX" && index.is_current === true
    );
    const previousUsdx = marketIndicesData.find(
      (index) => index.symbol === "USDX" && index.is_previous === true
    );

    let usdIndex: number;
    let prevUsdIndex: number;

    if (currentUsdx && previousUsdx) {
      // ✅ BEST CASE: Both current and previous USDX available from enhanced collection
      usdIndex = parseFloat(currentUsdx.price);
      prevUsdIndex = parseFloat(previousUsdx.price);

      this.logger.log(
        `✅ ENHANCED USDX: Found both current (${usdIndex}) and previous (${prevUsdIndex}) ` +
        `from market indices enhanced collection (market ${marketStatusInfo})`
      );
    } else if (currentUsdx) {
      // ✅ PARTIAL: Only current USDX available from enhanced collection
      usdIndex = parseFloat(currentUsdx.price);
      this.logger.log(
        `✅ PARTIAL ENHANCED: Found current USDX (${usdIndex}) from market indices (market ${marketStatusInfo})`
      );

      // Get previous from historical service
      prevUsdIndex = await this.historicalDataService.getCloseKdxy(
        methodSelection.method
      );
      this.logger.log(
        `✅ FALLBACK: Got previous USDX (${prevUsdIndex}) from historical service using method ${methodSelection.method} (market ${marketStatusInfo})`
      );

      if (!prevUsdIndex || prevUsdIndex <= 0) {
        throw new Error(
          `CRITICAL: Cannot proceed with KGX calculation - previous USD index is missing or invalid. ` +
          `Previous USD index: ${prevUsdIndex}. Check historical data sources.`
        );
      }
    } else {
      // ✅ FALLBACK: Check for standard USDX (without timeframe identification)
      const standardUsdx = marketIndicesData.find(
        (index) => index.symbol === "USDX" || index.symbol === "DXY"
      );

      if (standardUsdx) {
        usdIndex = parseFloat(standardUsdx.price);
        this.logger.log(
          `✅ STANDARD: Found USDX (${usdIndex}) from standard market indices (market ${marketStatusInfo})`
        );

        // Get previous from historical service
        prevUsdIndex = await this.historicalDataService.getCloseKdxy(
          methodSelection.method
        );
        this.logger.log(
          `✅ HISTORICAL: Got previous USDX (${prevUsdIndex}) from historical service using method ${methodSelection.method} (market ${marketStatusInfo})`
        );

        if (!prevUsdIndex || prevUsdIndex <= 0) {
          throw new Error(
            `CRITICAL: Cannot proceed with KGX calculation - previous USD index is missing or invalid. ` +
            `Previous USD index: ${prevUsdIndex}. Check historical data sources.`
          );
        }
      } else {
        throw new Error(
          `Feed-based USD index selected but no USDX data found in market indices (market ${marketStatusInfo})`
        );
      }
    }

    return { usdIndex, prevUsdIndex };
  }

  /**
   * Get USD index via historical fallback (last resort)
   */
  private async getUsdIndexViaHistoricalFallback(
    config: any,
    marketStatusInfo: string,
    methodSelection: any,
    now: number,
    processingId?: string
  ): Promise<{ usdIndex: number; prevUsdIndex: number }> {
    this.logger.warn(
      `Using historical fallback for USD index - this indicates data source issues (market ${marketStatusInfo})`
    );

    // ✅ FIXED: Use actual historical data instead of trying to calculate new values
    // When we're in HISTORICAL_FALLBACK mode, we should use existing historical data
    try {
      // Get current USD index from historical data
      const currentUsdIndex = await this.historicalDataService.getCloseKdxy(
        methodSelection.method
      );

      if (!currentUsdIndex || currentUsdIndex <= 0) {
        throw new Error(
          `CRITICAL: Historical fallback failed - no current USD index found in historical data using method ${methodSelection.method}`
        );
      }

      // Try to get a previous historical value (e.g., from 24 hours ago)
      // ✅ NO FALLBACK: If we can't get real previous data, we fail
      let prevUsdIndex: number;
      try {
        // In a real implementation, this would query for data from 24 hours ago
        // For now, we'll try the same method but expect it to fail
        prevUsdIndex = await this.historicalDataService.getCloseKdxy(
          methodSelection.method
        );

        if (!prevUsdIndex || prevUsdIndex <= 0) {
          throw new Error('No previous historical USD index data available');
        }
      } catch (prevError) {
        // ✅ NO FALLBACK VALUES: Fail clearly when no previous data available
        throw new Error(
          `CRITICAL: Historical fallback failed - no previous USD index data available. ` +
          `Current historical data exists (${currentUsdIndex}) but previous data is missing. ` +
          `Error: ${getErrorMessage(prevError)}. ` +
          `Cannot proceed with KGX calculation without both current and previous USD index values.`
        );
      }

      this.logger.log(
        `Historical fallback - current: ${currentUsdIndex}, previous: ${prevUsdIndex} (market ${marketStatusInfo})`
      );

      return { usdIndex: currentUsdIndex, prevUsdIndex };

    } catch (historicalError) {
      this.logger.error(
        `Historical data lookup failed: ${getErrorMessage(historicalError)}`
      );

      // ✅ LAST RESORT: If historical data is completely unavailable, 
      // we have no choice but to fail with a clear error message
      throw new Error(
        `CRITICAL: All USD index sources failed including historical fallback. ` +
        `Historical data error: ${getErrorMessage(historicalError)}. ` +
        `Cannot proceed with KGX calculation without any valid USD index data. ` +
        `Please check data sources and ensure at least historical USD index data is available.`
      );
    }
  }
}
