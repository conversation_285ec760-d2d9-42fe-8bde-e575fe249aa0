import { Injectable, Logger, Inject } from "@nestjs/common";
import { InjectRepository } from "@nestjs/typeorm";
import { Repository, DataSource } from "typeorm";
import {
  CryptoKgxEntity,
  CryptoPriceEntity,
  UsdIndexValueEntity,
} from "@data-pipeline/kgx";
import { MySQLService } from "./mysql.service";

/**
 * PostgreSQL service for the API microservice
 * Provides methods for accessing crypto data in PostgreSQL
 * with fallback to MySQL when PostgreSQL data is unavailable
 */
@Injectable()
export class PostgresService {
  private readonly logger = new Logger(PostgresService.name);

  constructor(
    @InjectRepository(CryptoKgxEntity)
    private readonly cryptoKgxRepository: Repository<CryptoKgxEntity>,
    @InjectRepository(CryptoPriceEntity)
    private readonly cryptoPriceRepository: Repository<CryptoPriceEntity>,
    @InjectRepository(UsdIndexValueEntity)
    private readonly usdIndexRepository: Repository<UsdIndexValueEntity>,
    private readonly dataSource: DataSource,
    @Inject(MySQLService) private readonly mysqlService: MySQLService
  ) { }

  async getLatestKgx(symbol?: string) {
    try {
      const query = this.cryptoKgxRepository
        .createQueryBuilder("kgx")
        .orderBy("kgx.time", "DESC");

      if (symbol) {
        query.where("kgx.symbol = :symbol", { symbol });
      }

      // Get the latest record for each symbol if no specific symbol is provided
      if (!symbol) {
        // Using a window function to get the latest record per symbol
        return await this.cryptoKgxRepository.query(`
          SELECT DISTINCT ON (symbol) *
          FROM crypto_kgx
          ORDER BY symbol, time DESC
        `);
      }

      return await query.limit(1).getOne();
    } catch (error) {
      this.logger.error(`Error fetching latest KGX data: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async getHistoricalKgx(symbol: string, startDate: Date, endDate: Date) {
    try {
      return await this.cryptoKgxRepository
        .createQueryBuilder("kgx")
        .where("kgx.symbol = :symbol", { symbol })
        .andWhere("kgx.time >= :startDate", { startDate })
        .andWhere("kgx.time <= :endDate", { endDate })
        .orderBy("kgx.time", "ASC")
        .getMany();
    } catch (error) {
      this.logger.error(`Error fetching historical KGX data: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async getLatestKdxy() {
    try {
      return await this.usdIndexRepository
        .createQueryBuilder("kdxy")
        .orderBy("kdxy.time", "DESC")
        .limit(1)
        .getOne();
    } catch (error) {
      this.logger.error(`Error fetching latest KDXY data: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  async getHistoricalKdxy(startDate: Date, endDate: Date) {
    try {
      return await this.usdIndexRepository
        .createQueryBuilder("kdxy")
        .where("kdxy.time >= :startDate", { startDate })
        .andWhere("kdxy.time <= :endDate", { endDate })
        .orderBy("kdxy.time", "ASC")
        .getMany();
    } catch (error) {
      this.logger.error(`Error fetching historical KDXY data: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Check database connection status
   * @returns Connection status object
   */
  async checkConnection() {
    try {
      // Try a simple query to check if the database is accessible
      const result = await this.dataSource.query('SELECT 1 as connected');
      return {
        connected: result && result.length > 0 && result[0].connected === 1,
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      this.logger.error(`Database connection check failed: ${error instanceof Error ? error.message : String(error)}`);
      return {
        connected: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      };
    }
  }

  /**
   * Get asset data filtered by asset type using symbol-based filtering
   * @param assetType Asset type to filter by ('CRYPTOCURRENCY', 'PRECIOUS_METALS', 'BASE_METALS', etc.)
   * @param symbols Optional array of specific symbols to fetch
   * @returns Array of asset records
   */
  async getAssetsByType(assetType?: string, symbols?: string[]) {
    try {
      this.logger.log(`Fetching assets for type: ${assetType || 'all'}, symbols: ${symbols?.join(', ') || 'all'}`);

      // Build WHERE conditions
      const conditions = [];
      const params: any[] = [];

      // Add asset type filtering based on symbol patterns
      if (assetType) {
        const symbolPatterns = this.getSymbolPatternsForAssetType(assetType);
        if (symbolPatterns.length > 0) {
          const symbolConditions = symbolPatterns.map((pattern, index) => {
            params.push(pattern);
            return `symbol = $${params.length}`;
          }).join(' OR ');
          conditions.push(`(${symbolConditions})`);
        } else {
          // If no known symbols for this asset type, return empty result
          this.logger.warn(`No known symbols for asset type: ${assetType}`);
          return [];
        }
      }

      // Add specific symbol filtering
      if (symbols && symbols.length > 0) {
        const symbolConditions = symbols.map((symbol, index) => {
          params.push(symbol);
          return `symbol = $${params.length}`;
        }).join(' OR ');

        if (conditions.length > 0) {
          // If we have asset type filtering, intersect with symbol filtering
          conditions.push(`(${symbolConditions})`);
        } else {
          // If no asset type filtering, just use symbol filtering
          conditions.push(`(${symbolConditions})`);
        }
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      // Get the latest record for each symbol
      const query = `
        SELECT DISTINCT ON (symbol) *
        FROM kgx
        ${whereClause}
        ORDER BY symbol, timestamp DESC
      `;

      this.logger.log(`Executing query: ${query} with params: ${JSON.stringify(params)}`);
      let result = await this.cryptoPriceRepository.query(query, params);

      // If PostgreSQL returns no data, try MySQL as a fallback
      if (!result || result.length === 0) {
        this.logger.warn('No data found in PostgreSQL, trying MySQL fallback');
        result = await this.getMySQLFallback(assetType, symbols);
      } else {
        this.logger.log(`Found ${result.length} asset records in PostgreSQL`);
      }

      return result;
    } catch (error) {
      this.logger.error(`Error fetching assets by type: ${error instanceof Error ? error.message : String(error)}`);

      // Try MySQL as last resort
      try {
        this.logger.warn('PostgreSQL query failed, attempting MySQL as last resort');
        return await this.getMySQLFallback(assetType, symbols);
      } catch (mysqlError) {
        this.logger.error(`MySQL last resort also failed: ${mysqlError instanceof Error ? mysqlError.message : String(mysqlError)}`);
        throw error;
      }
    }
  }

  /**
   * Get symbol patterns for a specific asset type
   */
  private getSymbolPatternsForAssetType(assetType: string): string[] {
    switch (assetType) {
      case 'CRYPTOCURRENCY':
        return ['BTC', 'ETH', 'ADA', 'AVAX', 'LINK', 'SOL', 'TRX', 'XRP']; // Known crypto symbols
      case 'PRECIOUS_METALS':
        return ['AU', 'AG', 'PT', 'PD', 'GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM'];
      case 'BASE_METALS':
        return ['CU', 'AL', 'ZN', 'NI', 'PB', 'SN', 'COPPER', 'ALUMINUM', 'ZINC', 'NICKEL', 'LEAD', 'TIN'];
      case 'ENERGY':
        return ['OIL', 'GAS', 'WTI', 'BRENT', 'CRUDE', 'GASOLINE', 'HEATING', 'NATURAL'];
      case 'FOREX':
        return []; // Forex pairs would be 6-character codes like EURUSD, GBPUSD, etc.
      default:
        return [];
    }
  }

  /**
   * Get cryptocurrency data only
   */
  async getCryptocurrencies(symbols?: string[]) {
    return this.getAssetsByType('CRYPTOCURRENCY', symbols);
  }

  /**
   * Get precious metals data only
   */
  async getPreciousMetals(symbols?: string[]) {
    return this.getAssetsByType('PRECIOUS_METALS', symbols);
  }

  /**
   * Get base metals data only
   */
  async getBaseMetals(symbols?: string[]) {
    return this.getAssetsByType('BASE_METALS', symbols);
  }

  /**
   * Get energy commodities data only
   */
  async getEnergyCommodities(symbols?: string[]) {
    return this.getAssetsByType('ENERGY', symbols);
  }

  /**
   * Legacy method - now uses the new asset-type-aware method
   */
  async getCryptoPrices(symbols?: string[]) {
    // For backward compatibility, return all asset types
    return this.getAssetsByType(undefined, symbols);
  }

  /**
   * MySQL fallback helper method
   */
  private async getMySQLFallback(assetType?: string, symbols?: string[]) {
    try {
      // Build MySQL query conditions using symbol-based filtering
      const conditions = [];

      // Add asset type filtering based on symbol patterns
      if (assetType) {
        const symbolPatterns = this.getSymbolPatternsForAssetType(assetType);
        if (symbolPatterns.length > 0) {
          const symbolConditions = symbolPatterns.map(s => `'${s}'`).join(',');
          conditions.push(`symbol IN (${symbolConditions})`);
        } else {
          // If no known symbols for this asset type, return empty result
          this.logger.warn(`No known symbols for asset type: ${assetType} in MySQL fallback`);
          return [];
        }
      }

      // Add specific symbol filtering
      if (symbols && symbols.length > 0) {
        const symbolConditions = symbols.map(s => `'${s}'`).join(',');
        if (conditions.length > 0) {
          // Intersect with asset type filtering
          conditions.push(`symbol IN (${symbolConditions})`);
        } else {
          // Just use symbol filtering
          conditions.push(`symbol IN (${symbolConditions})`);
        }
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '';

      const mysqlQuery = `
        SELECT * FROM kgx_crypto_data
        ${whereClause}
        ORDER BY symbol, time DESC
      `;

      const mysqlResult = await this.mysqlService.query(mysqlQuery);

      if (mysqlResult && mysqlResult.length > 0) {
        this.logger.log(`Found ${mysqlResult.length} asset records in MySQL`);

        // Process to deduplicate and get latest record per symbol
        const latestBySymbol = new Map();
        for (const item of mysqlResult) {
          if (!latestBySymbol.has(item.symbol) ||
            new Date(item.time) > new Date(latestBySymbol.get(item.symbol).time)) {
            latestBySymbol.set(item.symbol, item);
          }
        }

        const result = Array.from(latestBySymbol.values());
        this.logger.log(`Returning ${result.length} unique symbols from MySQL`);
        return result;
      } else {
        this.logger.warn('No data found in MySQL either');
        return [];
      }
    } catch (error) {
      this.logger.error(`Error in MySQL fallback: ${error instanceof Error ? error.message : String(error)}`);
      return [];
    }
  }

  /**
   * Legacy method implementation - keeping for backward compatibility
   */
  private async getLegacyCryptoPrices(symbols?: string[]) {
    try {
      this.logger.log(`Fetching crypto prices for symbols: ${symbols?.join(', ') || 'all'}`);

      // First attempt to get data from PostgreSQL
      const query = this.cryptoPriceRepository
        .createQueryBuilder("price")
        .orderBy("price.time", "DESC");

      if (symbols && symbols.length > 0) {
        query.where("price.symbol IN (:...symbols)", { symbols });
      }

      // Get the latest price for each symbol from PostgreSQL
      this.logger.log('Querying PostgreSQL for crypto data...');
      let result = await this.cryptoPriceRepository.query(`
        SELECT DISTINCT ON (symbol) *
        FROM kgx
        ${symbols && symbols.length > 0
          ? `WHERE symbol IN (${symbols.map((s) => `'${s}'`).join(",")})`
          : ""
        }
        ORDER BY symbol, timestamp DESC
      `);

      return result;
    } catch (error) {
      this.logger.error(`Error in legacy crypto prices method: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }

  /**
   * Execute a raw SQL query
   * @param sql SQL query
   * @param params Query parameters
   * @returns Query result
   */
  async query(sql: string, params: any[] = []): Promise<any> {
    try {
      return await this.dataSource.query(sql, params);
    } catch (error) {
      this.logger.error(`Error executing query: ${error instanceof Error ? error.message : String(error)}`);
      throw error;
    }
  }
}
