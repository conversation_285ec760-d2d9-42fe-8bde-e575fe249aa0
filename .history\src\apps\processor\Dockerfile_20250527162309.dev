# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root temporarily to copy files
USER root

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/processor ./apps/processor/

# Switch back to non-root user
USER nextjs

# Set service-specific environment variables
ENV PORT=3003

# Expose port
EXPOSE 3003

# Development command with hot reload
CMD ["nodemon", "--watch", "apps/processor", "--watch", "libs", "--ext", "ts,js,json", "--exec", "ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/processor/src/main.ts"] 