---
description: This rule provides comprehensive guidelines for ESLint, covering code organization, common patterns, performance, security, testing, and tooling, ensuring high-quality, maintainable JavaScript/TypeScript code.
globs: **/*.{js,jsx,ts,tsx,vue}
---
By following these best practices, you can ensure that your JavaScript/TypeScript code is clean, consistent, and maintainable, reducing the risk of bugs and improving overall code quality.