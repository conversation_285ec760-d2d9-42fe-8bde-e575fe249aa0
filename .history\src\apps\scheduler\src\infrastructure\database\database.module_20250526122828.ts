import { Modu<PERSON> } from "@nestjs/common";
import { DatabaseService } from "./services/database.service";
import { MySQLService } from "./services/mysql.service";
import {
  DatabaseConfigProvider,
  CollectorRepository,
  JobRepository as CentralizedJobRepository,
  SchedulerRepository as CentralizedSchedulerRepository,
  CoreDatabaseModule,
  DatabaseModule as StorageDatabaseModule,
} from "@data-pipeline/storage";

/**
 * Database module for the Scheduler microservice
 * Uses the database module to centralize database configuration
 */
@Module({
  imports: [
    StorageDatabaseModule.register({
      serviceGroups: ["shared", "scheduler"],
      includeMysql: true,
      includePostgres: true,
      includeMigration: true,
      includeDatabaseInitializer: true,
      includeRepositories: true,
      includeDynamicDatabase: false,
      includeConnectionRetry: true,
      includeEnhancedServices: true,
      entities: [],
      serviceName: "Scheduler",
    }),
  ],
  providers: [
    {
      provide: DatabaseService,
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return new DatabaseService(configProvider.getMySQLConfig());
      },
      inject: [DatabaseConfigProvider],
    },
    {
      provide: MySQLService,
      useFactory: (configProvider: DatabaseConfigProvider) => {
        return new MySQLService(configProvider.getMySQLConfig());
      },
      inject: [DatabaseConfigProvider],
    },
    CentralizedJobRepository,
    CentralizedSchedulerRepository,
    CollectorRepository,
  ],
  exports: [
    CentralizedJobRepository,
    CentralizedSchedulerRepository,
    CollectorRepository,
    DatabaseService,
    MySQLService,
    StorageDatabaseModule,
  ],
})
export class DatabaseModule {}
