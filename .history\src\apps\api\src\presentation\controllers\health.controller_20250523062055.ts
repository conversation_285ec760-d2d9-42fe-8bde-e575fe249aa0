import { Controller, Get } from "@nestjs/common";
import { Public } from '@data-pipeline/security';
import { ApiTags, ApiOperation, ApiResponse } from "@nestjs/swagger";
import { DatabaseHealthService } from "../../infrastructure/health/database-health.service";
import { CollectorClient } from "../../infrastructure/clients/collector.client";
import { ProcessorClient } from "../../infrastructure/clients/processor.client";
import { WriterClient } from "../../infrastructure/clients/writer.client";
import { SchedulerClient } from "../../infrastructure/clients/scheduler.client";
import { HttpService } from "@nestjs/axios";
import { ConfigService } from "../../infrastructure/config/config.service";
import { toErrorWithMessage } from "@data-pipeline/utils";
import { firstValueFrom } from "rxjs";

@Public()
@ApiTags("health")
@Controller("health")
export class HealthController {
  constructor(
    private readonly databaseHealthService: DatabaseHealthService,
    private readonly collectorClient: CollectorClient,
    private readonly processorClient: ProcessorClient,
    private readonly writerClient: WriterClient,
    private readonly schedulerClient: SchedulerClient,
    private readonly httpService: HttpService,
    private readonly configService: ConfigService
  ) {}

  @Get()
  @ApiOperation({ summary: "Check health of the API service" })
  @ApiResponse({ status: 200, description: "Service is healthy" })
  async checkHealth() {
    const databaseStatus = await this.checkDatabaseHealth();
    const servicesStatus = await this.checkServicesHealth();

    const isHealthy =
      databaseStatus.mysql.status === "up" &&
      databaseStatus.postgres.status === "up" &&
      databaseStatus.mysql.status === "up" &&
      databaseStatus.postgres.status === "up" &&
      servicesStatus.collector.status === "up" &&
      servicesStatus.processor.status === "up" &&
      servicesStatus.writer.status === "up" &&
      servicesStatus.scheduler.status === "up";

    return {
      status: isHealthy ? "up" : "down",
      timestamp: new Date(),
      infrastructure: {
        databases: databaseStatus,
      },
      services: servicesStatus,
      info: {
        service: "api-service",
        version: process.env.npm_package_version || "1.0.0",
        environment: process.env.NODE_ENV || "development",
      },
    };
  }

  @Get("readiness")
  @ApiOperation({ summary: "Check if the API service is ready" })
  @ApiResponse({ status: 200, description: "Service is ready" })
  async checkReadiness() {
    return {
      status: "ready",
      timestamp: new Date(),
    };
  }

  @Get("liveness")
  @ApiOperation({ summary: "Check if the API service is alive" })
  @ApiResponse({ status: 200, description: "Service is alive" })
  async checkLiveness() {
    return {
      status: "alive",
      timestamp: new Date(),
    };
  }

  private async checkDatabaseHealth() {
    const dbHealth = await this.databaseHealthService.checkAllDatabases();

    return {
      mysql: {
        status: dbHealth.mysql ? "up" : "down",
        details: {
          host: process.env.NODE_ENV === "development" ? "localhost" : process.env.MYSQL_HOST || "mysql",
          port: process.env.NODE_ENV === "development" ? 3306 : process.env.MYSQL_PORT || 3306,
        },
      },
      postgres: {
        status: dbHealth.postgres ? "up" : "down",
        details: {
          host: process.env.NODE_ENV === "development" ? "localhost" : process.env.POSTGRES_HOST || "postgres",
          port: process.env.NODE_ENV === "development" ? 5432 : process.env.POSTGRES_PORT || 5432,
        },
      },
    };
  }

  private async checkServicesHealth() {
    const services = {
      collector: { status: "down", error: null as string | null },
      processor: { status: "down", error: null as string | null },
      writer: { status: "down", error: null as string | null },
      scheduler: { status: "down", error: null as string | null },
    };

    try {
      await firstValueFrom(
        this.httpService.get("http://scheduler:3001/api/health")
      );
      services.scheduler.status = "up";
    } catch (error) {
      const formattedError = toErrorWithMessage(error);
      services.scheduler.error = formattedError.message;
    }

    try {
      await firstValueFrom(
        this.httpService.get("http://collector:3002/api/health")
      );
      services.collector.status = "up";
    } catch (error) {
      const formattedError = toErrorWithMessage(error);
      services.collector.error = formattedError.message;
    }

    try {
      await firstValueFrom(
        this.httpService.get("http://processor:3003/api/health")
      );
      services.processor.status = "up";
    } catch (error) {
      const formattedError = toErrorWithMessage(error);
      services.processor.error = formattedError.message;
    }

    try {
      await firstValueFrom(
        this.httpService.get("http://writer:3004/api/health")
      );
      services.writer.status = "up";
    } catch (error) {
      const formattedError = toErrorWithMessage(error);
      services.writer.error = formattedError.message;
    }

    return services;
  }

  @Get("services")
  @ApiOperation({ summary: "Check the status of all services" })
  @ApiResponse({ status: 200, description: "Return services status" })
  async getServicesStatus(): Promise<Record<string, any>> {
    return this.checkServicesHealth();
  }

  @Get("database")
  @ApiOperation({ summary: "Check the status of all databases" })
  @ApiResponse({ status: 200, description: "Return database status" })
  async getDatabaseStatus(): Promise<Record<string, any>> {
    return this.checkDatabaseHealth();
  }
}
