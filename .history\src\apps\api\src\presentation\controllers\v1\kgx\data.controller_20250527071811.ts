import { <PERSON>, <PERSON>, <PERSON>m, Query, <PERSON><PERSON>, <PERSON><PERSON>, HttpStatus, NotFoundException, Header, Inject, UnauthorizedException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Response, Request } from 'express';
import { Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { SSEService } from '@data-pipeline/messaging';
import { Public, ApiKeyService } from '@data-pipeline/security';
import { DataCacheService } from '../../../../application/services/data-cache.service';
import { CryptoDataService } from '../../../../application/services/crypto-data.service';
import { PostgresService } from '../../../../infrastructure/database/services/postgres.service';

interface CryptoRecord {
  id: string;
  time: string;
  symbol: string;
  price: number;
  high?: number;
  low?: number;
  price_change?: number;
  price_change_percent?: number;
  change?: number;
  change_due_usd_percent?: number;
  change_due_trade_percent?: number;
  change_usd?: number;
  change_due_usd?: number;
  ask?: number;
  bid?: number;
  asset_type?: string;
  name?: string;
  unit?: string;
  [key: string]: any;
}

@ApiTags('kgx')
@Controller('api/v1/kgx')
export class KgxDataController {
  private readonly logger = new Logger(KgxDataController.name);

  constructor(
    private readonly dataCacheService: DataCacheService,
    private readonly cryptoDataService: CryptoDataService,
    private readonly sseService: SSEService,
    @Inject(PostgresService) private readonly postgresService: PostgresService,
    private readonly apiKeyService: ApiKeyService
  ) { }

  @Get('getValue')
  @ApiOperation({ summary: 'Get single crypto value in oil API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data for a specific symbol in oil API format' })
  @ApiResponse({ status: 400, description: 'Bad request - symbol parameter is required' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: true, description: 'Cryptocurrency symbol or comma-separated list of symbols' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getValueFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('symbol') symbol?: string,
    @Query('apikey') apikey?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    try {
      if (!symbol) {
        this.logger.warn('No symbol parameter provided');
        throw new BadRequestException('Symbol parameter is required');
      }

      this.logger.log(`Received request for crypto value for symbol(s): ${symbol}`);

      // Handle multiple symbols (comma-separated)
      const symbolsToQuery = symbol.split(',').map(s => s.trim().toUpperCase());
      if (symbolsToQuery.length > 1) {
        // Fetch all requested symbols (crypto, metals, or both)
        const latestData = await this.postgresService.getCryptoPrices(symbolsToQuery);
        if (!latestData || latestData.length === 0) {
          this.logger.warn('No data found for requested symbols');
          throw new NotFoundException('No data available');
        }
        this.logger.log(`Found data for symbols: ${symbolsToQuery.join(', ')}`);
        const formattedData = latestData.map((record: CryptoRecord) => ({
          High: record.high ?? record.price ?? null,
          ChangeTrade: record.price_change ?? 0,
          Symbol: record.symbol ?? '',
          Mid: record.price ?? null,
          Change: record.change ?? record.price_change ?? 0,
          ChangePercentUSD: record.change_due_usd_percent,
          Unit: record.unit ?? (record.asset_type === 'PRECIOUS_METALS' ? 'GRAM' : 'SHARES'),
          Timestamp: this.formatTimestamp(record.timestamp, dateFormat, timeFormat),
          ChangePercentage: record.price_change_percent ?? 0,
          ChangePercentTrade: record.change_due_trade_percent ?? record.price_change_percent ?? 0,
          Low: record.low ?? record.price ?? null,
          Currency: 'USD',
          Ask: record.ask ?? (record.price ? record.price * 1.001 : null),
          ChangeUSD: record.change_usd ?? record.change_due_usd ?? 0,
          Bid: record.bid ?? (record.price ? record.price * 0.999 : null),
          Name: record.name ?? record.symbol ?? '',
          Price: record.price ?? null,
          KgxValue: record.kgx_value ?? null,
          asset_type: record.asset_type ?? null,
          metadata: record.metadata ?? null,
          extra: record.extra ?? null
        }));
        this.logger.log(`Returning values for symbols: ${symbolsToQuery.join(', ')}`);
        return formattedData;
      }

      // Single symbol logic (as before)
      const symbolToQuery = symbol.trim().toUpperCase();
      if (!/^[A-Z0-9-]+$/.test(symbolToQuery)) {
        this.logger.warn(`Invalid symbol format: ${symbolToQuery}`);
        throw new BadRequestException('Invalid symbol format. Only alphanumeric characters and hyphens are allowed.');
      }
      try {
        // Fetch the latest record for the symbol, regardless of asset_type
        const latestData = await this.postgresService.query(
          `WITH latest_times AS (
            SELECT symbol, MAX(timestamp) as latest_time
            FROM kgx
            WHERE symbol = $1
            GROUP BY symbol
          )
          SELECT d.*
          FROM kgx d
          JOIN latest_times lt ON d.symbol = lt.symbol AND d.timestamp = lt.latest_time
          WHERE d.symbol = $1
          LIMIT 1`,
          [symbolToQuery]
        );

        if (!latestData || latestData.length === 0) {
          this.logger.warn(`No data found for symbol: ${symbolToQuery}`);
          throw new NotFoundException(`No data available for symbol: ${symbolToQuery}`);
        }

        this.logger.log(`Found data for symbol: ${symbolToQuery}`);

        // Format the response as a unified object with all possible fields
        const formattedData = latestData.map((record: CryptoRecord) => ({
          High: record.high ?? record.price ?? null,
          ChangeTrade: record.price_change ?? 0,
          Symbol: record.symbol ?? '',
          Mid: record.price ?? null,
          Change: record.change ?? record.price_change ?? 0,
          ChangePercentUSD: record.change_due_usd_percent,
          Unit: record.unit ?? (record.asset_type === 'PRECIOUS_METALS' ? 'GRAM' : 'SHARES'),
          Timestamp: this.formatTimestamp(record.timestamp, dateFormat, timeFormat),
          ChangePercentage: record.price_change_percent ?? 0,
          ChangePercentTrade: record.change_due_trade_percent ?? record.price_change_percent ?? 0,
          Low: record.low ?? record.price ?? null,
          Currency: 'USD',
          Ask: record.ask ?? (record.price ? record.price * 1.001 : null),
          ChangeUSD: record.change_usd ?? record.change_due_usd ?? 0,
          Bid: record.bid ?? (record.price ? record.price * 0.999 : null),
          Name: record.name ?? record.symbol ?? '',
          Price: record.price ?? null,
          KgxValue: record.kgx_value ?? null,
          asset_type: record.asset_type ?? null,
          metadata: record.metadata ?? null,
          extra: record.extra ?? null
        }));

        this.logger.log(`Returning value for ${symbol} in oil API format`);
        return formattedData;
      } catch (error: any) {
        if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
          throw new InternalServerErrorException('Database connection error. Please try again later.');
        }
        throw error;
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
      this.logger.error(`Error getting crypto value: ${errorMessage}`, errorStack);

      if (errorMessage.includes('timeout') || errorMessage.includes('connection')) {
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      throw new InternalServerErrorException('Failed to retrieve crypto value. Please try again later.');
    }
  }

  @Get('getPM')
  @ApiOperation({ summary: 'Get crypto data in precious metals API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching precious metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values (set to any value to enable)' })
  async getPreciousMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in PM format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array (uppercase, trimmed)
      const symbols = symbolParam
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;
      // Use service for safe, parameterized query
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No crypto data found');
        throw new NotFoundException('No data available');
      }

      this.logger.log(`Found ${latestData.length} crypto records`);

      const formattedData = latestData.map((record: CryptoRecord) => {
        const baseData = {
          High: record.high || record.price,
          ChangeTrade: record.price_change || 0,
          Symbol: record.symbol,
          Mid: record.price,
          Change: record.change || record.price_change || 0,
          ChangePercentUSD: record.change_due_usd_percent,
          Unit: 'USD',
          Timestamp: this.formatTimestamp(record.timestamp, dateFormat, timeFormat),
          ChangePercentage: record.price_change_percent || 0,
          ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
          Low: record.low || record.price,
          Currency: 'USD',
          Ask: record.ask || (record.price * 1.001),
          ChangeUSD: record.change_usd || record.change_due_usd || 0,
          Bid: record.bid || (record.price * 0.999),
          KgxValue: record.kgx_value,
        };

        return baseData;
      });

      const response = {
        CryptoMetals: {
          crypto: formattedData,
          ...(includeKgx && { KgxValues: formattedData.map((item: any) => item.KgxValue) })
        }
      };

      this.logger.log(`Returning ${formattedData.length} crypto records in PM format  data: ${JSON.stringify(formattedData)}`);
      return response;
    } catch (error) {
      // Log and rethrow mapped exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getPM: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve crypto data');
    }
  }

  @Get('getBM')
  @ApiOperation({ summary: 'Get crypto data in base metals API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching base metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getBaseMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in BM format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array
      const symbols = symbolParam
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;
      // Fetch latest records via service for safe queries
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No crypto data found');
        throw new NotFoundException('No data available');
      }

      this.logger.log(`Found ${latestData.length} crypto records`);

      const formattedData = latestData.map((record: CryptoRecord) => ({
        Symbol: record.symbol,
        High: record.high || record.price,
        Low: record.low || record.price,
        ChangeTrade: record.price_change || 0,
        Price: record.price,
        ChangeUSD: record.change_usd || record.change_due_usd || 0,
        Change: record.change || record.price_change || 0,
        ChangePercentUSD: record.change_due_usd_percent,
        ChangePercentage: record.price_change_percent || 0,
        ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
        Timestamp: this.formatTimestamp(record.timestamp, dateFormat, timeFormat),
        Currency: 'USD'
      }));

      const response = {
        BaseMetals: {
          BM: formattedData
        }
      };

      this.logger.log(`Returning ${formattedData.length} crypto records in BM format`);
      return response;
    } catch (error) {
      // Log and map exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getBM: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve crypto data');
    }
  }

  @Get('getCR')
  @ApiOperation({ summary: 'Get cryptocurrency data in crypto-specific format' })
  @ApiResponse({ status: 200, description: 'Return the cryptocurrency data in crypto-specific format' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated) or "all" for all cryptos' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getCryptoFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in CR format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array
      const symbols = symbolParam && symbolParam.toLowerCase() !== 'all'
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;
      
      // Fetch latest crypto records via service for safe queries
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No cryptocurrency data found');
        throw new NotFoundException('No cryptocurrency data available');
      }

      this.logger.log(`Found ${latestData.length} cryptocurrency records`);

      const formattedData = latestData.map((record: CryptoRecord) => ({
        Symbol: record.symbol,
        Name: record.name || record.symbol,
        Price: record.price,
        High: record.high || record.price,
        Low: record.low || record.price,
        Change: record.change || record.price_change || 0,
        ChangeTrade: record.price_change || 0,
        ChangeUSD: record.change_usd || record.change_due_usd || 0,
        ChangePercentage: record.price_change_percent || 0,
        ChangePercentTrade: record.change_due_trade_percent || record.price_change_percent || 0,
        ChangePercentUSD: record.change_due_usd_percent,
        Volume: record.volume || null,
        MarketCap: record.market_cap || null,
        Timestamp: this.formatTimestamp(record.timestamp, dateFormat, timeFormat),
        Currency: 'USD',
        Unit: 'SHARES',
        Ask: record.ask || (record.price ? record.price * 1.001 : null),
        Bid: record.bid || (record.price ? record.price * 0.999 : null),
        KgxValue: record.kgx_value || null,
        AssetType: record.asset_type || 'CRYPTOCURRENCY'
      }));

      const response = {
        Cryptocurrencies: {
          CR: formattedData,
          ...(includeKgx && { KgxValues: formattedData.map((item: any) => item.KgxValue) })
        }
      };

      this.logger.log(`Returning ${formattedData.length} cryptocurrency records in CR format`);
      return response;
    } catch (error) {
      // Log and map exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getCR: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve cryptocurrency data');
    }
  }

  /**
   * Format timestamp based on provided formats
   * @param timestamp The timestamp to format (can be Unix timestamp in seconds, ISO string, or Date object)
   * @param dateFormat Optional date format (1=MM/DD/YYYY, 2=YYYY-MM-DD)
   * @param timeFormat Optional time format (1=12h, 2=24h)
   * @returns Formatted timestamp string
   */
  private formatTimestamp(timestamp: string | number | Date, dateFormat?: string, timeFormat?: string): string {
    let date: Date;

    // Handle different timestamp formats
    if (typeof timestamp === 'number') {
      // If it's a Unix timestamp (in seconds), convert to milliseconds
      date = new Date(timestamp * 1000);
    } else if (typeof timestamp === 'string') {
      // If it's a string, try to parse it as a date
      const parsedDate = new Date(timestamp);
      if (isNaN(parsedDate.getTime())) {
        // If string parsing fails, try parsing as Unix timestamp
        const unixTimestamp = parseInt(timestamp, 10);
        if (!isNaN(unixTimestamp)) {
          date = new Date(unixTimestamp * 1000);
        } else {
          throw new Error('Invalid timestamp format');
        }
      } else {
        date = parsedDate;
      }
    } else {
      // If it's already a Date object, use it directly
      date = timestamp;
    }

    // Validate the date
    if (isNaN(date.getTime())) {
      throw new Error('Invalid time value');
    }

    // Default format is YYYY-MM-DD HH:MM:SS (24h)
    if (!dateFormat && !timeFormat) {
      return date.toISOString()
        .replace('T', ' ')
        .replace(/\.\d+Z$/, '');
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const day = String(date.getDate()).padStart(2, '0');

    let formattedDate: string;
    if (dateFormat === '1') {
      formattedDate = `${month}/${day}/${year}`;
    } else {
      formattedDate = `${year}-${month}-${day}`;
    }

    let hours = date.getHours();
    const minutes = String(date.getMinutes()).padStart(2, '0');
    const seconds = String(date.getSeconds()).padStart(2, '0');

    let formattedTime: string;
    if (timeFormat === '1') {
      const ampm = hours >= 12 ? 'PM' : 'AM';
      hours = hours % 12;
      hours = hours ? hours : 12;
      formattedTime = `${hours}:${minutes}:${seconds} ${ampm}`;
    } else {
      formattedTime = `${String(hours).padStart(2, '0')}:${minutes}:${seconds}`;
    }

    return `${formattedDate} ${formattedTime}`;
  }
}
