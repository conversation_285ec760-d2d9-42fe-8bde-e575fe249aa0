#!/bin/bash

# KGX Crypto Data Application - Backup Script
# Author: System Administrator
# Version: 1.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
APP_DIR="/opt/kgx-app"
BACKUP_DIR="/opt/kgx-app/data/backups"
DATE=$(date +%Y%m%d_%H%M%S)
RETENTION_DAYS=${RETENTION_DAYS:-7}
COMPRESS=${COMPRESS:-true}

# Logging functions
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    if [[ ! -d "$APP_DIR" ]]; then
        error "Application directory $APP_DIR does not exist"
    fi
    
    if ! command -v docker &> /dev/null; then
        error "Docker is not installed"
    fi
    
    # Create backup directory if it doesn't exist
    mkdir -p "$BACKUP_DIR"
    
    log "Prerequisites check passed"
}

# Load environment variables
load_environment() {
    log "Loading environment variables..."
    
    if [[ -f "$APP_DIR/src/.env.local" ]]; then
        source "$APP_DIR/src/.env.local"
    elif [[ -f "$APP_DIR/src/.env" ]]; then
        source "$APP_DIR/src/.env"
    else
        error "No environment file found"
    fi
    
    log "Environment variables loaded"
}

# Backup PostgreSQL database
backup_postgres() {
    log "Backing up PostgreSQL database..."
    
    cd "$APP_DIR/src"
    
    local backup_file="$BACKUP_DIR/postgres_${DATE}.sql"
    
    if docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres pg_isready -U postgres; then
        docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres pg_dump \
            -U postgres \
            -h localhost \
            --verbose \
            --clean \
            --if-exists \
            --create \
            kitco > "$backup_file"
        
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
        fi
        
        log "PostgreSQL backup completed: $(basename "$backup_file")"
        echo "$backup_file"
    else
        warn "PostgreSQL is not running, skipping backup"
    fi
}

# Backup MySQL database
backup_mysql() {
    log "Backing up MySQL database..."
    
    cd "$APP_DIR/src"
    
    local backup_file="$BACKUP_DIR/mysql_${DATE}.sql"
    
    if docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysqladmin ping -h localhost -u root -p"$MYSQL_ROOT_PASSWORD" &>/dev/null; then
        docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysqldump \
            -u root \
            -p"$MYSQL_ROOT_PASSWORD" \
            --single-transaction \
            --routines \
            --triggers \
            --all-databases > "$backup_file"
        
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
        fi
        
        log "MySQL backup completed: $(basename "$backup_file")"
        echo "$backup_file"
    else
        warn "MySQL is not running, skipping backup"
    fi
}

# Backup Redis data
backup_redis() {
    log "Backing up Redis data..."
    
    cd "$APP_DIR/src"
    
    local backup_file="$BACKUP_DIR/redis_${DATE}.rdb"
    
    if docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T redis redis-cli ping &>/dev/null; then
        # Force Redis to save current state
        docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T redis redis-cli BGSAVE
        
        # Wait for background save to complete
        sleep 5
        
        # Copy the RDB file
        docker compose -f docker-compose.yml -f docker-compose.prod.yml cp redis:/data/dump.rdb "$backup_file"
        
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$backup_file"
            backup_file="${backup_file}.gz"
        fi
        
        log "Redis backup completed: $(basename "$backup_file")"
        echo "$backup_file"
    else
        warn "Redis is not running, skipping backup"
    fi
}

# Backup application configuration
backup_config() {
    log "Backing up application configuration..."
    
    local config_backup="$BACKUP_DIR/config_${DATE}.tar"
    
    tar -cf "$config_backup" \
        -C "$APP_DIR" \
        src/.env \
        src/.env.local \
        src/docker-compose.yml \
        src/docker-compose.prod.yml \
        2>/dev/null || true
    
    if [[ "$COMPRESS" == "true" ]]; then
        gzip "$config_backup"
        config_backup="${config_backup}.gz"
    fi
    
    log "Configuration backup completed: $(basename "$config_backup")"
    echo "$config_backup"
}

# Backup application logs
backup_logs() {
    log "Backing up application logs..."
    
    local logs_backup="$BACKUP_DIR/logs_${DATE}.tar"
    
    if [[ -d "$APP_DIR/data/logs" ]]; then
        tar -cf "$logs_backup" -C "$APP_DIR/data" logs/ 2>/dev/null || true
        
        if [[ "$COMPRESS" == "true" ]]; then
            gzip "$logs_backup"
            logs_backup="${logs_backup}.gz"
        fi
        
        log "Logs backup completed: $(basename "$logs_backup")"
        echo "$logs_backup"
    else
        warn "Logs directory not found, skipping logs backup"
    fi
}

# Create backup manifest
create_manifest() {
    local manifest_file="$BACKUP_DIR/manifest_${DATE}.txt"
    
    log "Creating backup manifest..."
    
    cat > "$manifest_file" <<EOF
KGX Crypto Data Application Backup Manifest
Date: $(date)
Backup ID: $DATE
Application Directory: $APP_DIR
Backup Directory: $BACKUP_DIR

Files in this backup:
EOF
    
    # List all backup files for this date
    find "$BACKUP_DIR" -name "*_${DATE}*" -type f | while read -r file; do
        local size=$(du -h "$file" | cut -f1)
        echo "- $(basename "$file") ($size)" >> "$manifest_file"
    done
    
    log "Backup manifest created: $(basename "$manifest_file")"
}

# Cleanup old backups
cleanup_old_backups() {
    log "Cleaning up backups older than $RETENTION_DAYS days..."
    
    local deleted_count=0
    
    # Find and delete old backup files
    find "$BACKUP_DIR" -name "*.sql*" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        ((deleted_count++))
        info "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "*.rdb*" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        ((deleted_count++))
        info "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "*.tar*" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        ((deleted_count++))
        info "Deleted old backup: $(basename "$file")"
    done
    
    find "$BACKUP_DIR" -name "manifest_*.txt" -mtime +$RETENTION_DAYS -type f | while read -r file; do
        rm -f "$file"
        ((deleted_count++))
        info "Deleted old manifest: $(basename "$file")"
    done
    
    log "Cleanup completed"
}

# Verify backup integrity
verify_backups() {
    log "Verifying backup integrity..."
    
    local verification_failed=false
    
    # Check PostgreSQL backup
    local postgres_backup=$(find "$BACKUP_DIR" -name "postgres_${DATE}*" -type f | head -1)
    if [[ -n "$postgres_backup" ]]; then
        if [[ "$postgres_backup" == *.gz ]]; then
            if ! gzip -t "$postgres_backup" 2>/dev/null; then
                error "PostgreSQL backup verification failed: $postgres_backup"
                verification_failed=true
            fi
        else
            if [[ ! -s "$postgres_backup" ]]; then
                error "PostgreSQL backup is empty: $postgres_backup"
                verification_failed=true
            fi
        fi
    fi
    
    # Check MySQL backup
    local mysql_backup=$(find "$BACKUP_DIR" -name "mysql_${DATE}*" -type f | head -1)
    if [[ -n "$mysql_backup" ]]; then
        if [[ "$mysql_backup" == *.gz ]]; then
            if ! gzip -t "$mysql_backup" 2>/dev/null; then
                error "MySQL backup verification failed: $mysql_backup"
                verification_failed=true
            fi
        else
            if [[ ! -s "$mysql_backup" ]]; then
                error "MySQL backup is empty: $mysql_backup"
                verification_failed=true
            fi
        fi
    fi
    
    # Check Redis backup
    local redis_backup=$(find "$BACKUP_DIR" -name "redis_${DATE}*" -type f | head -1)
    if [[ -n "$redis_backup" ]]; then
        if [[ "$redis_backup" == *.gz ]]; then
            if ! gzip -t "$redis_backup" 2>/dev/null; then
                error "Redis backup verification failed: $redis_backup"
                verification_failed=true
            fi
        else
            if [[ ! -s "$redis_backup" ]]; then
                error "Redis backup is empty: $redis_backup"
                verification_failed=true
            fi
        fi
    fi
    
    if [[ "$verification_failed" == "true" ]]; then
        error "Backup verification failed"
    else
        log "Backup verification completed successfully"
    fi
}

# Send backup notification (optional)
send_notification() {
    local status="$1"
    local message="$2"
    
    # This function can be extended to send notifications via email, Slack, etc.
    # For now, it just logs the notification
    
    if [[ "$status" == "success" ]]; then
        log "NOTIFICATION: $message"
    else
        error "NOTIFICATION: $message"
    fi
}

# Display backup summary
show_backup_summary() {
    log "Backup Summary"
    echo
    info "Backup completed at: $(date)"
    info "Backup ID: $DATE"
    info "Backup location: $BACKUP_DIR"
    echo
    info "Backup files created:"
    
    find "$BACKUP_DIR" -name "*_${DATE}*" -type f | while read -r file; do
        local size=$(du -h "$file" | cut -f1)
        info "- $(basename "$file") ($size)"
    done
    
    echo
    local total_size=$(du -sh "$BACKUP_DIR" | cut -f1)
    info "Total backup directory size: $total_size"
    
    local available_space=$(df -h "$BACKUP_DIR" | awk 'NR==2 {print $4}')
    info "Available disk space: $available_space"
}

# Full backup function
full_backup() {
    log "Starting full backup..."
    
    check_prerequisites
    load_environment
    
    local backup_files=()
    
    # Perform backups
    if postgres_file=$(backup_postgres); then
        backup_files+=("$postgres_file")
    fi
    
    if mysql_file=$(backup_mysql); then
        backup_files+=("$mysql_file")
    fi
    
    if redis_file=$(backup_redis); then
        backup_files+=("$redis_file")
    fi
    
    if config_file=$(backup_config); then
        backup_files+=("$config_file")
    fi
    
    if logs_file=$(backup_logs); then
        backup_files+=("$logs_file")
    fi
    
    create_manifest
    verify_backups
    cleanup_old_backups
    show_backup_summary
    
    send_notification "success" "KGX Application backup completed successfully (ID: $DATE)"
    
    log "Full backup completed successfully!"
}

# Database-only backup function
database_backup() {
    log "Starting database-only backup..."
    
    check_prerequisites
    load_environment
    
    backup_postgres
    backup_mysql
    backup_redis
    
    create_manifest
    verify_backups
    
    log "Database backup completed successfully!"
}

# Restore function
restore_backup() {
    local backup_id="$1"
    
    if [[ -z "$backup_id" ]]; then
        error "Backup ID is required for restore operation"
    fi
    
    log "Starting restore from backup ID: $backup_id"
    
    check_prerequisites
    load_environment
    
    # Stop application services
    warn "Stopping application services..."
    cd "$APP_DIR/src"
    docker compose -f docker-compose.yml -f docker-compose.prod.yml stop api scheduler collector processor writer dashboard
    
    # Restore PostgreSQL
    local postgres_backup=$(find "$BACKUP_DIR" -name "postgres_${backup_id}*" -type f | head -1)
    if [[ -n "$postgres_backup" ]]; then
        log "Restoring PostgreSQL from: $(basename "$postgres_backup")"
        
        if [[ "$postgres_backup" == *.gz ]]; then
            zcat "$postgres_backup" | docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres psql -U postgres
        else
            cat "$postgres_backup" | docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T postgres psql -U postgres
        fi
        
        log "PostgreSQL restore completed"
    fi
    
    # Restore MySQL
    local mysql_backup=$(find "$BACKUP_DIR" -name "mysql_${backup_id}*" -type f | head -1)
    if [[ -n "$mysql_backup" ]]; then
        log "Restoring MySQL from: $(basename "$mysql_backup")"
        
        if [[ "$mysql_backup" == *.gz ]]; then
            zcat "$mysql_backup" | docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysql -u root -p"$MYSQL_ROOT_PASSWORD"
        else
            cat "$mysql_backup" | docker compose -f docker-compose.yml -f docker-compose.prod.yml exec -T mysql mysql -u root -p"$MYSQL_ROOT_PASSWORD"
        fi
        
        log "MySQL restore completed"
    fi
    
    # Restore Redis
    local redis_backup=$(find "$BACKUP_DIR" -name "redis_${backup_id}*" -type f | head -1)
    if [[ -n "$redis_backup" ]]; then
        log "Restoring Redis from: $(basename "$redis_backup")"
        
        # Stop Redis
        docker compose -f docker-compose.yml -f docker-compose.prod.yml stop redis
        
        # Copy backup file
        if [[ "$redis_backup" == *.gz ]]; then
            zcat "$redis_backup" > /tmp/dump.rdb
        else
            cp "$redis_backup" /tmp/dump.rdb
        fi
        
        docker compose -f docker-compose.yml -f docker-compose.prod.yml cp /tmp/dump.rdb redis:/data/dump.rdb
        rm -f /tmp/dump.rdb
        
        # Start Redis
        docker compose -f docker-compose.yml -f docker-compose.prod.yml start redis
        
        log "Redis restore completed"
    fi
    
    # Start application services
    log "Starting application services..."
    docker compose -f docker-compose.yml -f docker-compose.prod.yml start api scheduler collector processor writer dashboard
    
    log "Restore completed successfully!"
}

# List available backups
list_backups() {
    log "Available backups:"
    echo
    
    # Get unique backup IDs
    local backup_ids=$(find "$BACKUP_DIR" -name "*_[0-9]*" -type f | sed 's/.*_\([0-9]\{8\}_[0-9]\{6\}\).*/\1/' | sort -u)
    
    for backup_id in $backup_ids; do
        info "Backup ID: $backup_id"
        
        # Show manifest if available
        local manifest="$BACKUP_DIR/manifest_${backup_id}.txt"
        if [[ -f "$manifest" ]]; then
            cat "$manifest" | grep -E "^Date:|^Files in this backup:" -A 20
        else
            # List files for this backup ID
            find "$BACKUP_DIR" -name "*_${backup_id}*" -type f | while read -r file; do
                local size=$(du -h "$file" | cut -f1)
                echo "  - $(basename "$file") ($size)"
            done
        fi
        echo
    done
}

# Main function
main() {
    case "${1:-full}" in
        "full")
            full_backup
            ;;
        "database"|"db")
            database_backup
            ;;
        "restore")
            restore_backup "${2:-}"
            ;;
        "list")
            list_backups
            ;;
        "cleanup")
            check_prerequisites
            cleanup_old_backups
            ;;
        *)
            echo "Usage: $0 {full|database|restore <backup_id>|list|cleanup}"
            echo
            echo "Commands:"
            echo "  full      - Create full backup (databases, config, logs)"
            echo "  database  - Create database-only backup"
            echo "  restore   - Restore from backup (requires backup ID)"
            echo "  list      - List available backups"
            echo "  cleanup   - Remove old backups"
            echo
            echo "Environment variables:"
            echo "  RETENTION_DAYS - Number of days to keep backups (default: 7)"
            echo "  COMPRESS       - Compress backup files (default: true)"
            exit 1
            ;;
    esac
}

# Run main function
main "$@" 