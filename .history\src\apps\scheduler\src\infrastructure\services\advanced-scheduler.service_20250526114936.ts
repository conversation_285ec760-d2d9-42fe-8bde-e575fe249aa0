import { Injectable, Logger, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
import { getErrorMessage, getErrorStack } from '@data-pipeline/core';
import { Job } from '@data-pipeline/storage';
import * as schedule from 'node-schedule';
import { JobRepositoryAdapter } from '../adapters/job.adapter';
import { JobService } from '../../domain/services/job.service';
import { v4 as uuidv4 } from 'uuid';

/**
 * Service for advanced job scheduling using node-schedule
 */
@Injectable()
export class AdvancedSchedulerService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(AdvancedSchedulerService.name);
  private readonly scheduledJobs = new Map<string, schedule.Job>();
  private readonly jobMetadata = new Map<string, {
    jobId: string;
    scheduleId: string;
    lastRun: Date | null;
    nextRun: Date | null;
    cronExpression: string;
  }>();
  
  // Flag to indicate if the service is initialized
  private initialized = false;
  
  // Flag to indicate if the service is shutting down
  private isShuttingDown = false;

  constructor(
    private readonly jobRepository: JobRepositoryAdapter,
    private readonly jobService: JobService
  ) {}

  /**
   * Initialize the service
   */
  async onModuleInit(): Promise<void> {
    try {
      this.logger.log('Initializing advanced scheduler service...');
      
      // Load all active jobs
      await this.loadActiveJobs();
      
      this.initialized = true;
      this.logger.log(`Advanced scheduler service initialized with ${this.scheduledJobs.size} jobs`);
    } catch (error) {
      this.logger.error(
        `Failed to initialize advanced scheduler service: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Clean up when the service is destroyed
   */
  async onModuleDestroy(): Promise<void> {
    this.logger.log('Shutting down advanced scheduler service...');
    this.isShuttingDown = true;
    
    // Cancel all scheduled jobs
    for (const [scheduleId, job] of this.scheduledJobs.entries()) {
      job.cancel();
      this.logger.debug(`Cancelled scheduled job: ${scheduleId}`);
    }
    
    this.scheduledJobs.clear();
    this.jobMetadata.clear();
    
    this.logger.log('Advanced scheduler service shut down');
  }

  /**
   * Load all active jobs from the database
   */
  private async loadActiveJobs(): Promise<void> {
    try {
      // Get all enabled jobs
      const jobs = await this.jobRepository.findAll({ filters: { enabled: true } });
      this.logger.log(`Loading ${jobs.length} active jobs`);
      
      // Schedule each job
      for (const job of jobs) {
        await this.scheduleJob(job);
      }
    } catch (error) {
      this.logger.error(
        `Failed to load active jobs: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Schedule a job using node-schedule
   * @param job Job to schedule
   */
  async scheduleJob(job: Job): Promise<void> {
    try {
      // Skip if shutting down
      if (this.isShuttingDown) {
        return;
      }
      
      // Validate cron expression
      if (!job.schedule) {
        this.logger.warn(`Job ${job.id} has no schedule, skipping`);
        return;
      }
      
      // Generate a unique schedule ID
      const scheduleId = `${job.id}-${uuidv4()}`;
      
      // Cancel existing schedule for this job if it exists
      await this.cancelJobSchedule(job.id);
      
      // Schedule the job
      const scheduledJob = schedule.scheduleJob(scheduleId, job.schedule, async () => {
        await this.executeScheduledJob(job.id, scheduleId);
      });
      
      if (!scheduledJob) {
        throw new Error(`Failed to schedule job ${job.id} with cron expression: ${job.schedule}`);
      }
      
      // Store the scheduled job
      this.scheduledJobs.set(scheduleId, scheduledJob);
      
      // Store metadata
      this.jobMetadata.set(scheduleId, {
        jobId: job.id,
        scheduleId,
        lastRun: job.last_run ? new Date(job.last_run) : null,
        nextRun: scheduledJob.nextInvocation(),
        cronExpression: job.schedule
      });
      
      this.logger.log(`Scheduled job ${job.id} with cron expression: ${job.schedule}, next run: ${scheduledJob.nextInvocation()}`);
    } catch (error) {
      this.logger.error(
        `Failed to schedule job ${job.id}: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Cancel a job's schedule
   * @param jobId Job ID
   */
  async cancelJobSchedule(jobId: string): Promise<void> {
    try {
      // Find all schedules for this job
      const scheduleIds = Array.from(this.jobMetadata.entries())
        .filter(([_, metadata]) => metadata.jobId === jobId)
        .map(([scheduleId]) => scheduleId);
      
      // Cancel each schedule
      for (const scheduleId of scheduleIds) {
        const scheduledJob = this.scheduledJobs.get(scheduleId);
        if (scheduledJob) {
          scheduledJob.cancel();
          this.scheduledJobs.delete(scheduleId);
          this.jobMetadata.delete(scheduleId);
          this.logger.debug(`Cancelled schedule for job ${jobId}: ${scheduleId}`);
        }
      }
    } catch (error) {
      this.logger.error(
        `Failed to cancel job schedule for ${jobId}: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Execute a scheduled job
   * @param jobId Job ID
   * @param scheduleId Schedule ID
   */
  private async executeScheduledJob(jobId: string, scheduleId: string): Promise<void> {
    try {
      // Skip if shutting down
      if (this.isShuttingDown) {
        return;
      }
      
      this.logger.log(`Executing scheduled job: ${jobId}`);
      
      // Update metadata
      const metadata = this.jobMetadata.get(scheduleId);
      if (metadata) {
        metadata.lastRun = new Date();
        
        // Get next run time
        const scheduledJob = this.scheduledJobs.get(scheduleId);
        if (scheduledJob) {
          metadata.nextRun = scheduledJob.nextInvocation();
        }
      }
      
      // Process the job
      await this.jobService.processJob(jobId);
      
      this.logger.log(`Completed scheduled job: ${jobId}`);
    } catch (error) {
      this.logger.error(
        `Failed to execute scheduled job ${jobId}: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Update a job's schedule
   * @param jobId Job ID
   * @param cronExpression New cron expression
   */
  async updateJobSchedule(jobId: string, cronExpression: string): Promise<void> {
    try {
      // Skip if shutting down
      if (this.isShuttingDown) {
        return;
      }
      
      // Cancel existing schedule
      await this.cancelJobSchedule(jobId);
      
      // Get the job
      const job = await this.jobRepository.findById(jobId);
      if (!job) {
        throw new Error(`Job ${jobId} not found`);
      }
      
      // Update the job's schedule
      job.schedule = cronExpression;
      await this.jobRepository.updateJob(jobId, { schedule: cronExpression });
      
      // Schedule the job
      await this.scheduleJob(job);
      
      this.logger.log(`Updated schedule for job ${jobId}: ${cronExpression}`);
    } catch (error) {
      this.logger.error(
        `Failed to update job schedule for ${jobId}: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
    }
  }

  /**
   * Get all scheduled jobs
   */
  getScheduledJobs(): any[] {
    const jobs = [];
    
    for (const [scheduleId, metadata] of this.jobMetadata.entries()) {
      jobs.push({
        scheduleId,
        jobId: metadata.jobId,
        cronExpression: metadata.cronExpression,
        lastRun: metadata.lastRun,
        nextRun: metadata.nextRun
      });
    }
    
    return jobs;
  }

  /**
   * Get a scheduled job by ID
   * @param jobId Job ID
   */
  getScheduledJob(jobId: string): any | null {
    for (const [scheduleId, metadata] of this.jobMetadata.entries()) {
      if (metadata.jobId === jobId) {
        return {
          scheduleId,
          jobId: metadata.jobId,
          cronExpression: metadata.cronExpression,
          lastRun: metadata.lastRun,
          nextRun: metadata.nextRun
        };
      }
    }
    
    return null;
  }

  /**
   * Manually trigger a job
   * @param jobId Job ID
   */
  async triggerJob(jobId: string): Promise<void> {
    try {
      // Skip if shutting down
      if (this.isShuttingDown) {
        return;
      }
      
      // Find the schedule for this job
      const scheduleId = Array.from(this.jobMetadata.entries())
        .find(([_, metadata]) => metadata.jobId === jobId)?.[0];
      
      if (scheduleId) {
        await this.executeScheduledJob(jobId, scheduleId);
      } else {
        // If no schedule exists, process the job directly
        await this.jobService.processJob(jobId);
      }
    } catch (error) {
      this.logger.error(
        `Failed to trigger job ${jobId}: ${getErrorMessage(error)}`,
        getErrorStack(error)
      );
      throw error;
    }
  }
}
