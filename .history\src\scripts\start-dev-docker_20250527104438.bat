@echo off
setlocal enabledelayedexpansion

REM Development Docker Startup Script for Windows
REM This script starts the development environment with hot-reload capabilities

echo 🚀 Starting Development Environment with Docker Hot-Reload
echo ==================================================

REM Check if Docker is running
docker info >nul 2>&1
if errorlevel 1 (
    echo ❌ Docker is not running. Please start Docker first.
    exit /b 1
)

REM Check if docker-compose is available
docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ❌ docker-compose is not installed. Please install docker-compose first.
    exit /b 1
)

REM Clean up any existing containers
echo 🧹 Cleaning up existing containers...
docker-compose down -v

REM Build and start services
echo 🔨 Building and starting development services...
docker-compose up --build -d postgres mysql redis

REM Wait for databases to be ready
echo ⏳ Waiting for databases to be ready...
timeout /t 10 /nobreak >nul

REM Start application services with hot-reload
echo 🔥 Starting application services with hot-reload...
docker-compose up --build api scheduler collector processor writer dashboard

echo ✅ Development environment started!
echo.
echo 📋 Available services:
echo   - API: http://localhost:3000
echo   - Scheduler: http://localhost:3001
echo   - Collector: http://localhost:3002
echo   - Processor: http://localhost:3003
echo   - Writer: http://localhost:3004
echo   - Dashboard: http://localhost:3010
echo.
echo 📝 Useful commands:
echo   - View logs: docker-compose logs -f [service-name]
echo   - Stop services: docker-compose down
echo   - Rebuild services: docker-compose up --build
echo.
echo 🔥 Hot-reload is enabled! Your code changes will be reflected automatically. 