FROM node:22-alpine

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json ./

# Install all dependencies (including dev dependencies)
RUN yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 && \
    yarn global add nx

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/dashboard ./apps/dashboard/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3010 \
    CHOKIDAR_USEPOLLING=true \
    WATCHPACK_POLLING=true

# Expose port
EXPOSE 3010

# Development command with hot reload for React
CMD ["npx", "nx", "serve", "dashboard", "--host", "0.0.0.0", "--port", "3010"] 