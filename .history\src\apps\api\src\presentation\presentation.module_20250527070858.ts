import { forward<PERSON><PERSON>, Module, NestModule, MiddlewareConsumer, RequestMethod } from "@nestjs/common";
import { HttpModule } from "@nestjs/axios";
import { JsonLoggerModule } from '@data-pipeline/logging';
import { SecurityModule } from "@data-pipeline/security";
import { ApplicationModule } from "../application/application.module";
import { DomainModule } from "../domain/domain.module";
import { CollectorController } from "./controllers/collectors.controller";
import { PipelinesController } from "./controllers/pipelines.controller";
import { DestinationsController } from "./controllers/destinations.controller";
import { JobsController } from "./controllers/jobs.controller";
import { CollectionsController } from "./controllers/collections.controller";
import { ProcessingController } from "./controllers/processing.controller";
import { WritingController } from "./controllers/writing.controller";
import { HealthController } from "./controllers/health.controller";
import { AuthController } from "./controllers/auth.controller";
import { KgxDataController } from "./controllers/v1/kgx/kgx-crypto-data.controller";
import { ApiKeyNormalizerMiddleware } from "./middleware/api-key-normalizer.middleware";
import { ServiceClientModule } from "../infrastructure/clients/client.module";
import { MessagingModule } from "../infrastructure/messaging/messaging.module";
import { DatabaseModule } from "../infrastructure/database/database.module";
import { ConfigModule } from "../infrastructure/config/config.module";
import { HealthModule } from "../infrastructure/health/health.module";

@Module({
  imports: [
    forwardRef(() => ApplicationModule),
    forwardRef(() => DomainModule),
    ServiceClientModule,
    MessagingModule,
    DatabaseModule,
    ConfigModule,
    HealthModule,
    HttpModule.register({
      timeout: 10000,
      maxRedirects: 5,
    }),
    SecurityModule.register({
      jwt: {
        secret: process.env.JWT_SECRET || "secret",
        expiresIn: process.env.JWT_EXPIRES_IN || "3600s",
        issuer: process.env.API_SERVICE_URL || "http://api:3000",
        audience: process.env.API_AUDIENCE || "*",
      },
      apiKey: {
        keys: {
          [process.env.API_KEY || 'default_key']: { name: 'default', roles: [], permissions: [] },
        },
      },
    }),
    JsonLoggerModule,
  ],
  controllers: [
    CollectorController,
    PipelinesController,
    DestinationsController,
    JobsController,
    CollectionsController,
    ProcessingController,
    WritingController,
    HealthController,
    AuthController,
    KgxCryptoDataController,
  ],
})
export class PresentationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    // Apply API key normalizer middleware to all KGX crypto data routes
    consumer
      .apply(ApiKeyNormalizerMiddleware)
      .forRoutes(
        { path: 'kgx-crypto-data*', method: RequestMethod.ALL },
        { path: 'v1/kgx-crypto-data*', method: RequestMethod.ALL }
      );
  }
}
