import { Injectable, Logger } from "@nestjs/common";
import {
  SchedulerRepository as CentralizedSchedulerRepository,
  MySQLServiceBase,
  Scheduler,
} from "@data-pipeline/storage";

/**
 * Adapter to extend the centralized SchedulerRepository with additional methods
 * specific to the scheduler service
 */
@Injectable()
export class SchedulerRepositoryAdapter {
  private readonly logger = new Logger(SchedulerRepositoryAdapter.name);
  private readonly tableName = "schedulers";

  constructor(
    private readonly schedulerRepository: CentralizedSchedulerRepository,
    private readonly mysqlService: MySQLServiceBase
  ) {}

  /**
   * Find a scheduler by ID
   * @param id Scheduler ID
   * @returns Scheduler or null if not found
   */
  async findById(id: string): Promise<Scheduler | null> {
    return this.schedulerRepository.findById(id);
  }

  /**
   * Find all schedulers
   * @param options Query options
   * @returns Array of schedulers
   */
  async findAll(options: any = {}): Promise<Scheduler[]> {
    return this.schedulerRepository.find(options);
  }

  /**
   * Create a scheduler
   * @param scheduler Scheduler data
   * @returns Created scheduler
   */
  async createScheduler(
    scheduler: Omit<Scheduler, "id" | "created_at" | "updated_at">
  ): Promise<Scheduler> {
    return this.schedulerRepository.create(scheduler);
  }

  /**
   * Update a scheduler
   * @param id Scheduler ID
   * @param scheduler Scheduler data
   * @returns Updated scheduler
   */
  async updateScheduler(
    id: string,
    scheduler: Partial<Scheduler>
  ): Promise<Scheduler | null> {
    const existingScheduler = await this.schedulerRepository.findById(id);
    if (!existingScheduler) {
      return null;
    }

    await this.schedulerRepository.update(id, scheduler);
    return this.schedulerRepository.findById(id);
  }

  /**
   * Delete a scheduler
   * @param id Scheduler ID
   * @returns True if deleted, false if not found
   */
  async deleteScheduler(id: string): Promise<boolean> {
    const query = `DELETE FROM ${this.tableName} WHERE id = ?`;
    const result = await this.mysqlService.query<any>(query, [id]);
    return result.affectedRows > 0;
  }

  /**
   * Find schedulers by type
   * @param type Scheduler type
   * @returns Array of schedulers
   */
  async findByType(type: string): Promise<Scheduler[]> {
    const query = `SELECT * FROM ${this.tableName} WHERE type = ?`;
    return this.mysqlService.query(query, [type]);
  }

  /**
   * Find active schedulers
   * @returns Array of active schedulers
   */
  async findActive(): Promise<Scheduler[]> {
    const query = `SELECT * FROM ${this.tableName} WHERE active = 1`;
    return this.mysqlService.query(query);
  }
}
