

// src/components/crypto/CryptoTable.tsx
import React, { useMemo, useState } from "react";
import { useCrypto } from "@/contexts/CryptoContext";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Skeleton } from "@/components/ui/skeleton";
import { Input } from "@/components/ui/input";
import { Search, Download, RefreshCw, ArrowUp, ArrowDown, Bitcoin, DollarSign, Coins, Sparkles, Gem, CircleDollarSign, Landmark, WalletCards, PiggyBank, Wallet, Banknote, Wallet2, FileDown, FileSpreadsheet, Factory, Zap, TrendingUp, Crown, Star, Shield, Hexagon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { formatDateTime } from "@/lib/utils";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";
import { CryptoData } from "@/types/api.types";

interface SortConfig {
  key: keyof CryptoData;
  direction: "asc" | "desc";
}

// Asset type definitions
type AssetType = 'all' | 'base-metals' | 'precious-metals' | 'energies' | 'crypto';

interface AssetTypeConfig {
  id: AssetType;
  label: string;
  icon: React.ReactNode;
  color: string;
  description: string;
}

// Asset type configurations
const assetTypes: AssetTypeConfig[] = [
  {
    id: 'all',
    label: 'All Assets',
    icon: <TrendingUp className="h-4 w-4" />,
    color: 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200',
    description: 'All available assets'
  },
  {
    id: 'base-metals',
    label: 'Base Metals',
    icon: <Factory className="h-4 w-4" />,
    color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
    description: 'Industrial metals like copper, aluminum'
  },
  {
    id: 'precious-metals',
    label: 'Precious Metals',
    icon: <Gem className="h-4 w-4" />,
    color: 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
    description: 'Gold, silver, platinum, palladium'
  },
  {
    id: 'energies',
    label: 'Energies',
    icon: <Zap className="h-4 w-4" />,
    color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
    description: 'Oil, gas, and energy commodities'
  },
  {
    id: 'crypto',
    label: 'Cryptocurrencies',
    icon: <Bitcoin className="h-4 w-4" />,
    color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
    description: 'Digital currencies and tokens'
  }
];

// Table styles configuration
const tableStyles = {
  container: "bg-white dark:bg-gray-900 rounded-xl border border-gray-200 dark:border-gray-800 shadow-sm overflow-hidden",
  table: "min-w-full divide-y divide-gray-200 dark:divide-gray-800",
  thead: "bg-gray-50 dark:bg-gray-800",
  th: "px-6 py-4 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider",
  tbody: "bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800",
  td: "px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-gray-300",
  symbolCell: "font-medium text-gray-900 dark:text-white sticky left-0 bg-white dark:bg-gray-900 z-10",
  iconContainer: "w-8 h-8 rounded-full flex items-center justify-center",
  numberCell: "text-right font-mono text-gray-900 dark:text-gray-200",
  changePositive: "text-green-600 dark:text-green-400",
  changeNegative: "text-red-600 dark:text-red-400",
  hoverRow: "hover:bg-gray-50 dark:hover:bg-gray-800/80 transition-colors duration-150",
  loadingRow: "h-16 animate-pulse bg-gray-100 dark:bg-gray-800",
  emptyRow: "px-6 py-16 text-center text-gray-500 dark:text-gray-400 text-inline-block",
  searchContainer: "relative w-full sm:w-96",
  searchIcon: "absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400",
  searchInput: "pl-10 w-full bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-900 dark:text-white placeholder-gray-400 dark:placeholder-gray-500",
  actionButton: "flex items-center gap-2 bg-white dark:bg-gray-800 border-gray-300 dark:border-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700",
  cardHeader: "px-6 pt-6 pb-4",
  cardTitle: "text-xl font-semibold text-gray-900 dark:text-white",
  cardDescription: "text-sm text-gray-500 dark:text-gray-400 mt-1",
  currencyBadge: "w-8 h-8 rounded-full bg-gray-100 dark:bg-gray-800 flex items-center justify-center text-xs font-medium text-gray-600 dark:text-gray-300",
};

// Crypto icon mapping
const cryptoIconMapping: Record<string, React.ReactNode> = {
  'BTC': <Bitcoin className="w-5 h-5 text-orange-500" />,
  'ETH': <Coins className="w-4 h-4 text-purple-500" />, // Using Coins as a fallback for ETH
  'USDT': <DollarSign className="w-4 h-4 text-emerald-500" />,
  'XRP': <Coins className="w-4 h-4 text-gray-400" />,
  'SOL': <Sparkles className="w-4 h-4 text-blue-400" />,
  'ADA': <Gem className="w-4 h-4 text-blue-600" />,
  'DOGE': <CircleDollarSign className="w-4 h-4 text-yellow-500" />,
  'DOT': <Landmark className="w-4 h-4 text-pink-500" />,
  'MATIC': <WalletCards className="w-4 h-4 text-purple-400" />,
  'LTC': <PiggyBank className="w-4 h-4 text-gray-400" />,
  'LINK': <Wallet className="w-4 h-4 text-blue-400" />,
  'USDC': <Banknote className="w-4 h-4 text-blue-500" />,
  'DAI': <Wallet2 className="w-4 h-4 text-yellow-400" />,
  'BNB': <Banknote className="w-4 h-4 text-blue-500" />,
  'SUI': <Wallet2 className="w-4 h-4 text-yellow-400" />,
};

// Unit color configurations
const unitColors: Record<string, string> = {
  'SHARES': 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200',
  'GRAM': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200',
  'TONNE': 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200',
  'BARREL': 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200',
  'UNIT': 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200',
  'OZ': 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200',
  'OUNCE': 'bg-amber-100 text-amber-800 dark:bg-amber-900 dark:text-amber-200'
};

// Function to get specific icons for precious metals
const getPreciousMetalIcon = (symbol: string) => {
  switch (symbol?.toUpperCase()) {
    case 'AU':
    case 'GOLD':
      return <Crown className="h-4 w-4 text-yellow-600" />;
    case 'AG':
    case 'SILVER':
      return <Star className="h-4 w-4 text-gray-500" />;
    case 'PT':
    case 'PLATINUM':
      return <Shield className="h-4 w-4 text-gray-700" />;
    case 'PD':
    case 'PALLADIUM':
      return <Hexagon className="h-4 w-4 text-gray-600" />;
    default:
      return <Gem className="h-4 w-4 text-purple-600" />;
  }
};

// Get the appropriate icon for a symbol or fallback to the first letter
const getCryptoIcon = (symbol: string) => {
  // Check if it's a precious metal first
  const preciousMetals = ['AU', 'AG', 'PT', 'PD', 'GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM'];
  if (preciousMetals.includes(symbol?.toUpperCase())) {
    return getPreciousMetalIcon(symbol);
  }

  // Check crypto icon mapping
  const icon = cryptoIconMapping[symbol];
  if (icon) return icon;

  // Fallback to first letter
  return (
    <div className="w-6 h-6 rounded-full bg-gray-100 dark:bg-gray-700 flex items-center justify-center">
      <span className="text-xs font-medium text-gray-600 dark:text-gray-300">
        {symbol?.charAt(0) || '?'}
      </span>
    </div>
  );
};

export const CryptoTable: React.FC = () => {
  const { data, loading, error, refresh } = useCrypto();
  const [searchTerm, setSearchTerm] = useState("");
  const [activeTab, setActiveTab] = useState<AssetType>('all');
  const [sortConfig, setSortConfig] = useState<SortConfig>({
    key: "price",
    direction: "desc",
  });

  const exportToCSV = () => {
    if (!filteredAndSortedData.length) return;

    // Define headers
    const headers = [
      'Symbol',
      'Name',
      'Bid',
      'Ask',
      'Price',
      '24h High',
      '24h Low',
      '24h Change',
      '24h Change %',
      'Trade Δ',
      'Trade Δ%',
      'USD Δ',
      'USD Δ%',
      'KGX Value',
      'Last Updated'
    ];

    // Convert data to CSV rows
    const rows = filteredAndSortedData.map(item => {
      return [
        `"${item.Symbol}"`,
        `"${item.name || ''}"`,
        item.Bid,
        item.Ask,
        item.Mid,
        item.High,
        item.Low,
        item.Change,
        item.ChangePercentage,
        item.ChangeTrade,
        item.ChangePercentTrade,
        item.ChangeUSD,
        item.ChangePercentUSD,
        item.kgxValue || '',
        `"${new Date(item.Timestamp).toLocaleString()}"`
      ].join(',');
    });

    // Combine headers and rows
    const csvContent = [headers.join(','), ...rows].join('\n');

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `crypto-data-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  const exportToExcel = async () => {
    try {
      // Dynamically import xlsx to reduce bundle size
      const XLSX = await import('xlsx');

      // Prepare data for Excel
      const excelData = filteredAndSortedData.map(item => ({
        'Symbol': item.Symbol,
        'Name': item.name || '',
        'Bid': item.Bid,
        'Ask': item.Ask,
        'Price': item.Mid,
        '24h High': item.High,
        '24h Low': item.Low,
        '24h Change': item.Change,
        '24h Change %': item.ChangePercentage,
        'Trade Δ': item.ChangeTrade,
        'Trade Δ%': item.ChangePercentTrade,
        'USD Δ': item.ChangeUSD,
        'USD Δ%': item.ChangePercentUSD,
        'KGX Value': item.KgxValue || null,
        'Last Updated': new Date(item.Timestamp)
      }));

      // Create worksheet and workbook
      const worksheet = XLSX.utils.json_to_sheet(excelData);
      const workbook = XLSX.utils.book_new();
      XLSX.utils.book_append_sheet(workbook, worksheet, 'Crypto Data');

      // Generate Excel file and trigger download
      XLSX.writeFile(workbook, `crypto-data-${new Date().toISOString().split('T')[0]}.xlsx`);
    } catch (error) {
      console.error('Error exporting to Excel:', error);
      toast.error('Failed to export to Excel. Please try again.');
    }
  };

  // Helper function to determine asset type from category
  const getAssetTypeFromCategory = (category: string): AssetType => {
    switch (category) {
      case 'base-metals':
        return 'base-metals';
      case 'precious-metals':
        return 'precious-metals';
      case 'energies':
        return 'energies';
      case 'crypto':
        return 'crypto';
      default:
        return 'crypto';
    }
  };

  // Group data by asset type with proper ordering
  const groupedData = useMemo(() => {
    const groups: Record<AssetType, CryptoData[]> = {
      'all': [],
      'base-metals': [],
      'precious-metals': [],
      'energies': [],
      'crypto': []
    };

    (data || []).forEach(item => {
      const assetType = getAssetTypeFromCategory(item.category || 'crypto');
      groups[assetType].push(item);
      groups.all.push(item);
    });

    return groups;
  }, [data]);

  const filteredAndSortedData = useMemo(() => {
    let result = [...(groupedData[activeTab] || [])];

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      result = result.filter(
        (item) =>
          item.Symbol?.toLowerCase().includes(term) ||
          item.name?.toLowerCase().includes(term) ||
          item.category?.toLowerCase().includes(term)
      );
    }

    // Sort
    result.sort((a, b) => {
      const aValue = a[sortConfig.key] ?? 0;
      const bValue = b[sortConfig.key] ?? 0;

      if (aValue < bValue) {
        return sortConfig.direction === "asc" ? -1 : 1;
      }
      if (aValue > bValue) {
        return sortConfig.direction === "asc" ? 1 : -1;
      }
      return 0;
    });

    return result;
  }, [groupedData, activeTab, searchTerm, sortConfig]);

  const requestSort = (key: keyof CryptoData) => {
    setSortConfig((prev) => ({
      key,
      direction: prev.key === key && prev.direction === "asc" ? "desc" : "asc",
    }));
  };

  const SortableHeader: React.FC<{
    field: keyof CryptoData;
    children: React.ReactNode;
  }> = ({ field, children }) => (
    <div
      className="flex items-center cursor-pointer"
      onClick={() => requestSort(field)}
    >
      {children}
      {sortConfig.key === field && (
        <span className="ml-1">
          {sortConfig.direction === "asc" ? (
            <ArrowUp size={14} />
          ) : (
            <ArrowDown size={14} />
          )}
        </span>
      )}
    </div>
  );

  if (loading && data.length === 0) {
    return (
      <div className="space-y-2">
        {[...Array(5)].map((_, i) => (
          <Skeleton key={i} className="h-12 w-full" />
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  // Helper function to format numbers with consistent decimal places
  const formatNumber = (value: unknown, decimals: number, showSign = false) => {
    if (value === undefined || value === null) return "-";

    const num = Number(value);
    if (isNaN(num)) {
      console.warn("Invalid number value:", value);
      return "-";
    }

    const options: Intl.NumberFormatOptions = {
      minimumFractionDigits: decimals,
      maximumFractionDigits: decimals,
    };

    if (showSign) {
      options.signDisplay = "exceptZero";
    }

    return num.toLocaleString(undefined, options);
  };

  // Helper function to determine cell color based on value
  const getChangeCellClass = (value: number, additionalClasses = "") => {
    if (value === undefined || value === null) {
      return `text-right ${additionalClasses}`;
    }
    return `text-right ${
      value >= 0 ? "text-green-600" : "text-red-600"
    } ${additionalClasses}`;
  };

  return (
    <Card className={tableStyles.container}>
      <CardHeader className={tableStyles.cardHeader}>
        <div className="flex flex-col space-y-4">
          <div>
            <h2 className={tableStyles.cardTitle}>Asset Prices & KGX Values</h2>
            <p className={tableStyles.cardDescription}>
              Real-time prices for cryptocurrencies, precious metals, base metals, and energy commodities
            </p>
            <p className={tableStyles.cardDescription}>
              Updated every 10 minutes
            </p>
          </div>

          {/* Asset Type Tabs */}
          <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as AssetType)} className="w-full">
            <TabsList className="grid w-full grid-cols-5 mb-4">
              {assetTypes.map((assetType) => (
                <TabsTrigger
                  key={assetType.id}
                  value={assetType.id}
                  className="flex items-center gap-2 text-xs"
                >
                  {assetType.icon}
                  <span className="hidden sm:inline">{assetType.label}</span>
                  <span className="sm:hidden">{assetType.label.split(' ')[0]}</span>
                  <Badge
                    variant="secondary"
                    className={`ml-1 text-xs ${
                      groupedData[assetType.id]?.length > 0
                        ? assetType.color
                        : 'bg-gray-100 text-gray-400 dark:bg-gray-800 dark:text-gray-500'
                    }`}
                  >
                    {groupedData[assetType.id]?.length > 0 ? groupedData[assetType.id].length : '-'}
                  </Badge>
                </TabsTrigger>
              ))}
            </TabsList>
          </Tabs>

          {/* Search and Actions */}
          <div className="flex flex-col sm:flex-row gap-4 justify-between items-start sm:items-center w-full">
            <div className={tableStyles.searchContainer}>
              <Search className={tableStyles.searchIcon} />
              <Input
                placeholder={`Search ${assetTypes.find(t => t.id === activeTab)?.label.toLowerCase() || 'assets'}...`}
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className={tableStyles.searchInput}
              />
            </div>
            <div className="flex gap-2 w-full sm:w-auto">
              <div className="relative group">
                <Button variant="outline" size="sm" className={tableStyles.actionButton}>
                  <Download className="h-4 w-4" />
                  <span>Export</span>
                </Button>
                <div className="absolute right-0 mt-1 w-40 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 z-30 hidden group-hover:block">
                  <button
                    onClick={exportToExcel}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <FileSpreadsheet className="h-4 w-4" />
                    <span>Export to Excel</span>
                  </button>
                  <button
                    onClick={exportToCSV}
                    className="w-full text-left px-4 py-2 text-sm text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center gap-2"
                  >
                    <FileDown className="h-4 w-4" />
                    <span>Export to CSV</span>
                  </button>
                </div>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={refresh}
                disabled={loading}
                className={tableStyles.actionButton}
              >
                <RefreshCw className={`h-4 w-4 ${loading ? 'animate-spin' : ''}`} />
                <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
              </Button>
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table className={tableStyles.table}>
            <TableHeader className={tableStyles.thead}>
              <TableRow>
                <TableHead className={`${tableStyles.th} w-48 sticky left-0 z-20`}>Asset</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Timestamp</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Unit</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>KGX</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Bid</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Ask</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Price</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>24h High</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>24h Low</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>24h Change</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>% Change</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Trade Δ</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>Trade Δ%</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>USD Δ</TableHead>
                <TableHead className={`${tableStyles.th} text-left`}>USD Δ%</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredAndSortedData.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={15}
                    className={tableStyles.emptyRow}
                  >
                    {loading ? "Loading data..." : "No matching assets found"}
                  </TableCell>
                </TableRow>
              ) : (
                filteredAndSortedData.map((crypto, index) => (
                  <TableRow
                    key={`${crypto.Symbol}-${index}`}
                    className={tableStyles.hoverRow}
                  >
                    <TableCell className={`${tableStyles.td} ${tableStyles.symbolCell}`}>
                      <div className="flex items-center gap-3">
                        <div className={`${tableStyles.iconContainer} bg-gray-100 dark:bg-gray-800`}>
                          {getCryptoIcon(crypto.Symbol)}
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{crypto.Symbol}</span>
                            {activeTab === 'all' && (
                              <Badge
                                variant="outline"
                                className={`text-xs ${assetTypes.find(t => t.id === getAssetTypeFromCategory(crypto.category || 'crypto'))?.color || 'bg-gray-100'}`}
                              >
                                {assetTypes.find(t => t.id === getAssetTypeFromCategory(crypto.category || 'crypto'))?.icon}
                              </Badge>
                            )}
                          </div>
                          {crypto.name && (
                            <div className="text-xs text-gray-500 dark:text-gray-400">{crypto.name}</div>
                          )}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className={`${tableStyles.td} text-left text-gray-500 dark:text-gray-400 text-xs`}>
                      {crypto.Timestamp
                        ? new Date(crypto.Timestamp).toLocaleTimeString()
                        : '-'}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} text-left`}>
                      <Badge
                        variant="outline"
                        className={`text-xs font-mono ${unitColors[crypto.Unit || 'SHARES'] || unitColors['SHARES']}`}
                      >
                        {crypto.Unit || 'SHARES'}
                      </Badge>
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} font-semibold text-left`}>
                      {crypto.KgxValue ? formatNumber(crypto.KgxValue, 4) : '-'}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} text-left`}>
                      {formatNumber(crypto.Bid, 4)}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} text-left`}>
                      {formatNumber(crypto.Ask, 4)}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} font-semibold text-left`}>
                      {formatNumber(crypto.Mid, 4)}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} text-left`}>
                      {formatNumber(crypto.High, 4)}
                    </TableCell>
                    <TableCell className={`${tableStyles.td} ${tableStyles.numberCell} text-left`}>
                      {formatNumber(crypto.Low, 4)}
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.Change) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.Change, 4, true)}
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.ChangePercentage) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.ChangePercentage, 2, true)}%
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.ChangeTrade) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.ChangeTrade, 4, true)}
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.ChangePercentTrade) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.ChangePercentTrade, 2, true)}%
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.ChangeUSD) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.ChangeUSD, 4, true)}
                    </TableCell>
                    <TableCell
                      className={`${tableStyles.td} ${tableStyles.numberCell} text-left ${
                        Number(crypto.ChangePercentUSD) >= 0
                          ? tableStyles.changePositive
                          : tableStyles.changeNegative
                      }`}
                    >
                      {formatNumber(crypto.ChangePercentUSD, 2, true)}%
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </CardContent>
    </Card>
  );
};
