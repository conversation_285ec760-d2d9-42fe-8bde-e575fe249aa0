---
description: Describes the high-level architecture of the Task Master CLI application.
globs: scripts/modules/*.js
alwaysApply: false
---

# Application Architecture Overview

- **Modular Structure**: The Task Master CLI is built using a modular architecture, with distinct modules responsible for different aspects of the application. This promotes separation of concerns, maintainability, and testability.

- **Main Modules and Responsibilities**:

  - **[`commands.js`](mdc:scripts/modules/commands.js): Command Handling**
    - **Purpose**: Defines and registers all CLI commands using Commander.js.
    - **Responsibilities**:
      - Parses command-line arguments and options.
      - Invokes appropriate functions from other modules to execute commands.
      - Handles user input and output related to command execution.
      - Implements input validation and error handling for CLI commands.
    - **Key Components**:
      - `programInstance` (Commander.js `Command` instance): Manages command definitions.
      - `registerCommands(programInstance)`: Function to register all application commands.
      - Command action handlers: Functions executed when a specific command is invoked.

  - **[`task-manager.js`](mdc:scripts/modules/task-manager.js): Task Data Management**
    - **Purpose**: Manages task data, including loading, saving, creating, updating, deleting, and querying tasks.
    - **Responsibilities**:
      - Reads and writes task data to `tasks.json` file.
      - Implements functions for task CRUD operations (Create, Read, Update, Delete).
      - Handles task parsing from PRD documents using AI.
      - Manages task expansion and subtask generation.
      - Updates task statuses and properties.
      - Implements task listing and display logic.
      - Performs task complexity analysis using AI.
    - **Key Functions**:
      - `readTasks(tasksPath)` / `writeTasks(tasksPath, tasksData)`: Load and save task data.
      - `parsePRD(prdFilePath, outputPath, numTasks)`: Parses PRD document to create tasks.
      - `expandTask(taskId, numSubtasks, useResearch, prompt, force)`: Expands a task into subtasks.
      - `setTaskStatus(tasksPath, taskIdInput, newStatus)`: Updates task status.
      - `listTasks(tasksPath, statusFilter, withSubtasks)`: Lists tasks with filtering and subtask display options.
      - `analyzeComplexity(tasksPath, reportPath, useResearch, thresholdScore)`: Analyzes task complexity.

  - **[`dependency-manager.js`](mdc:scripts/modules/dependency-manager.js): Dependency Management**
    - **Purpose**: Manages task dependencies, including adding, removing, validating, and fixing dependency relationships.
    - **Responsibilities**:
      - Adds and removes task dependencies.
      - Validates dependency relationships to prevent circular dependencies and invalid references.
      - Fixes invalid dependencies by removing non-existent or self-referential dependencies.
      - Provides functions to check for circular dependencies.
    - **Key Functions**:
      - `addDependency(tasksPath, taskId, dependencyId)`: Adds a dependency between tasks.
      - `removeDependency(tasksPath, taskId, dependencyId)`: Removes a dependency.
      - `validateDependencies(tasksPath)`: Validates task dependencies.
      - `fixDependencies(tasksPath)`: Fixes invalid task dependencies.
      - `isCircularDependency(tasks, taskId, dependencyChain)`: Detects circular dependencies.

  - **[`ui.js`](mdc:scripts/modules/ui.js): User Interface Components**
    - **Purpose**: Handles all user interface elements, including displaying information, formatting output, and providing user feedback.
    - **Responsibilities**:
      - Displays task lists, task details, and command outputs in a formatted way.
      - Uses `chalk` for colored output and `boxen` for boxed messages.
      - Implements table display using `cli-table3`.
      - Shows loading indicators using `ora`.
      - Provides helper functions for status formatting, dependency display, and progress reporting.
      - Suggests next actions to the user after command execution.
    - **Key Functions**:
      - `displayTaskList(tasks, statusFilter, withSubtasks)`: Displays a list of tasks in a table.
      - `displayTaskDetails(task)`: Displays detailed information for a single task.
      - `displayComplexityReport(reportPath)`: Displays the task complexity report.
      - `startLoadingIndicator(message)` / `stopLoadingIndicator(indicator)`: Manages loading indicators.
      - `getStatusWithColor(status)`: Returns status string with color formatting.
      - `formatDependenciesWithStatus(dependencies, allTasks, inTable)`: Formats dependency list with status indicators.

  - **[`ai-services.js`](mdc:scripts/modules/ai-services.js) (Conceptual): AI Integration**
    - **Purpose**:  Abstracts interactions with AI models (like Anthropic Claude and Perplexity AI) for various features. *Note: This module might be implicitly implemented within `task-manager.js` and `utils.js` or could be explicitly created for better organization as the project evolves.*
    - **Responsibilities**:
      - Handles API calls to AI services.
      - Manages prompts and parameters for AI requests.
      - Parses AI responses and extracts relevant information.
      - Implements logic for task complexity analysis, task expansion, and PRD parsing using AI.
    - **Potential Functions**:
      - `getAIResponse(prompt, model, maxTokens, temperature)`: Generic function to interact with AI model.
      - `analyzeTaskComplexityWithAI(taskDescription)`: Sends task description to AI for complexity analysis.
      - `expandTaskWithAI(taskDescription, numSubtasks, researchContext)`: Generates subtasks using AI.
      - `parsePRDWithAI(prdContent)`: Extracts tasks from PRD content using AI.

  - **[`utils.js`](mdc:scripts/modules/utils.js): Utility Functions and Configuration**
    - **Purpose**: Provides reusable utility functions and global configuration settings used across the application.
    - **Responsibilities**:
      - Manages global configuration settings loaded from environment variables and defaults.
      - Implements logging utility with different log levels and output formatting.
      - Provides file system operation utilities (read/write JSON files).
      - Includes string manipulation utilities (e.g., `truncate`, `sanitizePrompt`).
      - Offers task-specific utility functions (e.g., `formatTaskId`, `findTaskById`, `taskExists`).
      - Implements graph algorithms like cycle detection for dependency management.
    - **Key Components**:
      - `CONFIG`: Global configuration object.
      - `log(level, ...args)`: Logging function.
      - `readJSON(filepath)` / `writeJSON(filepath, data)`: File I/O utilities for JSON files.
      - `truncate(text, maxLength)`: String truncation utility.
      - `formatTaskId(id)` / `findTaskById(tasks, taskId)`: Task ID and search utilities.
      - `findCycles(subtaskId, dependencyMap)`: Cycle detection algorithm.

- **Data Flow and Module Dependencies**:

  - **Commands Initiate Actions**: User commands entered via the CLI (handled by [`commands.js`](mdc:scripts/modules/commands.js)) are the entry points for most operations.
  - **Command Handlers Delegate to Managers**: Command handlers in [`commands.js`](mdc:scripts/modules/commands.js) call functions in [`task-manager.js`](mdc:scripts/modules/task-manager.js) and [`dependency-manager.js`](mdc:scripts/modules/dependency-manager.js) to perform core task and dependency management logic.
  - **UI for Presentation**:  [`ui.js`](mdc:scripts/modules/ui.js) is used by command handlers and task/dependency managers to display information to the user. UI functions primarily consume data and format it for output, without modifying core application state.
  - **Utilities for Common Tasks**: [`utils.js`](mdc:scripts/modules/utils.js) provides helper functions used by all other modules for configuration, logging, file operations, and common data manipulations.
  - **AI Services Integration**: AI functionalities (complexity analysis, task expansion, PRD parsing) are invoked from [`task-manager.js`](mdc:scripts/modules/task-manager.js) and potentially [`commands.js`](mdc:scripts/modules/commands.js), likely using functions that would reside in a dedicated `ai-services.js` module or be integrated within `utils.js` or `task-manager.js`.

- **Testing Architecture**:

  - **Test Organization Structure**:
    - **Unit Tests**: Located in `tests/unit/`, reflect the module structure with one test file per module
    - **Integration Tests**: Located in `tests/integration/`, test interactions between modules
    - **End-to-End Tests**: Located in `tests/e2e/`, test complete workflows from a user perspective
    - **Test Fixtures**: Located in `tests/fixtures/`, provide reusable test data

  - **Module Design for Testability**:
    - **Explicit Dependencies**: Functions accept their dependencies as parameters rather than using globals
    - **Functional Style**: Pure functions with minimal side effects make testing deterministic
    - **Separate Logic from I/O**: Core business logic is separated from file system operations
    - **Clear Module Interfaces**: Each module has well-defined exports that can be mocked in tests
    - **Callback Isolation**: Callbacks are defined as separate functions for easier testing
    - **Stateless Design**: Modules avoid maintaining internal state where possible

  - **Mock Integration Patterns**:
    - **External Libraries**: Libraries like `fs`, `commander`, and `@anthropic-ai/sdk` are mocked at module level
    - **Internal Modules**: Application modules are mocked with appropriate spy functions
    - **Testing Function Callbacks**: Callbacks are extracted from mock call arguments and tested in isolation
    - **UI Elements**: Output functions from `ui.js` are mocked to verify display calls

  - **Testing Flow**:
    - Module dependencies are mocked (following Jest's hoisting behavior)
    - Test modules are imported after mocks are established
    - Spy functions are set up on module methods
    - Tests call the functions under test and verify behavior
    - Mocks are reset between test cases to maintain isolation

- **Benefits of this Architecture**:

  - **Maintainability**: Modules are self-contained and focused, making it easier to understand, modify, and debug specific features.
  - **Testability**:  Each module can be tested in isolation (unit testing), and interactions between modules can be tested (integration testing).
    - **Mocking Support**: The clear dependency boundaries make mocking straightforward
    - **Test Isolation**: Each component can be tested without affecting others
    - **Callback Testing**: Function callbacks can be extracted and tested independently
  - **Reusability**: Utility functions and UI components can be reused across different parts of the application.
  - **Scalability**:  New features can be added as new modules or by extending existing ones without significantly impacting other parts of the application.
  - **Clarity**: The modular structure provides a clear separation of concerns, making the codebase easier to navigate and understand for developers.

This architectural overview should help AI models understand the structure and organization of the Task Master CLI codebase, enabling them to more effectively assist with code generation, modification, and understanding.