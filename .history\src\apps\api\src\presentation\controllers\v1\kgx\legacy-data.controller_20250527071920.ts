import { <PERSON>, Get, Query, Logger, <PERSON><PERSON>, Req, Inject } from '@nestjs/common';
import { Response, Request } from 'express';
import { ApiTags, ApiOperation, ApiResponse, ApiQuery } from '@nestjs/swagger';
import { KgxDataController } from './data.controller';

/**
 * Legacy controller to maintain backward compatibility for old KGX API URLs
 * Redirects to the new API structure while maintaining the same functionality
 */
@ApiTags('kgx-legacy')
@Controller(['kgx', 'v1/kgx'])
export class LegacyKgxDataController {
  private readonly logger = new Logger(LegacyKgxDataController.name);

  constructor(
    private readonly kgxDataController: KgxDataController
  ) {}

  @Get('getValue')
  @ApiOperation({ 
    summary: 'Get single crypto value in oil API format (Legacy)',
    description: 'Legacy endpoint. Please use /api/v1/kgx/getValue for new implementations.'
  })
  @ApiResponse({ status: 200, description: 'Return the crypto data for a specific symbol in oil API format' })
  @ApiResponse({ status: 400, description: 'Bad request - symbol parameter is required' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: true, description: 'Cryptocurrency symbol or comma-separated list of symbols' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getValueFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('symbol') symbol?: string,
    @Query('apikey') apikey?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Legacy getValue endpoint called, redirecting to new API structure`);
    
    // Add deprecation warning header
    res.setHeader('X-API-Deprecated', 'true');
    res.setHeader('X-API-New-Endpoint', '/api/v1/kgx/getValue');
    
    return this.kgxDataController.getValueFormat(
      req, res, symbol, apikey, type, version, dateFormat, timeFormat, includeKgx
    );
  }

  @Get('getPM')
  @ApiOperation({ 
    summary: 'Get crypto data in precious metals API format (Legacy)',
    description: 'Legacy endpoint. Please use /api/v1/kgx/getPM for new implementations.'
  })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching precious metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values (set to any value to enable)' })
  async getPreciousMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Legacy getPM endpoint called, redirecting to new API structure`);
    
    // Add deprecation warning header
    res.setHeader('X-API-Deprecated', 'true');
    res.setHeader('X-API-New-Endpoint', '/api/v1/kgx/getPM');
    
    return this.kgxDataController.getPreciousMetalsFormat(
      req, res, apikey, symbolParam, type, version, dateFormat, timeFormat, includeKgx
    );
  }

  @Get('getBM')
  @ApiOperation({ 
    summary: 'Get crypto data in base metals API format (Legacy)',
    description: 'Legacy endpoint. Please use /api/v1/kgx/getBM for new implementations.'
  })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching base metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getBaseMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Legacy getBM endpoint called, redirecting to new API structure`);
    
    // Add deprecation warning header
    res.setHeader('X-API-Deprecated', 'true');
    res.setHeader('X-API-New-Endpoint', '/api/v1/kgx/getBM');
    
    return this.kgxDataController.getBaseMetalsFormat(
      req, res, apikey, symbolParam, type, version, dateFormat, timeFormat, includeKgx
    );
  }
} 