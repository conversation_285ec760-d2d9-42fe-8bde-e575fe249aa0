# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root temporarily to copy files
USER root

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/dashboard ./apps/dashboard/

# Switch back to non-root user
USER nextjs

# Set service-specific environment variables
ENV PORT=3010 \
    CHOKIDAR_USEPOLLING=true \
    WATCHPACK_POLLING=true

# Expose port
EXPOSE 3010

# Development command with hot reload for React
CMD ["nx", "serve", "dashboard", "--host", "0.0.0.0", "--port", "3010"] 