# Extend from shared base image
FROM data-pipeline-base:dev

# Install build tools needed for SQLite rebuild in entrypoint
RUN apk add --no-cache python3 make g++

# Copy service-specific source code
COPY apps/collector ./apps/collector/

# Copy entrypoint script
COPY apps/collector/entrypoint.dev.sh /entrypoint.dev.sh
RUN chmod +x /entrypoint.dev.sh

# Set service-specific environment variables
ENV PORT=3002

# Expose port
EXPOSE 3002

# Use entrypoint to rebuild native dependencies after volume mounts
ENTRYPOINT ["/entrypoint.dev.sh"]

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/collector", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/collector/src/main.ts"] 