import { <PERSON>, <PERSON>, Param, Query, Logger, <PERSON><PERSON>, HttpStatus, NotFoundException, Header, Inject, UnauthorizedException, BadRequestException, InternalServerErrorException } from '@nestjs/common';
import { Response, Request } from 'express';
import { Req } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiQuery } from '@nestjs/swagger';
import { SSEService } from '@data-pipeline/messaging';
import { Public, ApiKeyService } from '@data-pipeline/security';
import { DataCacheService } from '../../../../application/services/data-cache.service';
import { CryptoDataService } from '../../../../application/services/crypto-data.service';
import { PostgresService } from '../../../../infrastructure/database/services/postgres.service';
import { AssetDataTransformerService } from '../../../../application/services/asset-data-transformer.service';
import {
  KgxRecord,
  TransformationOptions,
  ValueFormatResponse,
  PreciousMetalsFormatResponse,
  BaseMetalsFormatResponse,
  CryptoFormatResponse
} from '../../../../application/interfaces/asset-data.interface';

@ApiTags('kgx')
@Controller('v1/kgx')
export class KgxDataController {
  private readonly logger = new Logger(KgxDataController.name);

  constructor(
    private readonly dataCacheService: DataCacheService,
    private readonly cryptoDataService: CryptoDataService,
    private readonly sseService: SSEService,
    @Inject(PostgresService) private readonly postgresService: PostgresService,
    private readonly apiKeyService: ApiKeyService,
    private readonly assetDataTransformer: AssetDataTransformerService
  ) { }

  @Get('getValue')
  @ApiOperation({ summary: 'Get single crypto value in oil API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data for a specific symbol in oil API format' })
  @ApiResponse({ status: 400, description: 'Bad request - symbol parameter is required' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Cryptocurrency symbol, comma-separated list of symbols, or "all" for all assets' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getValueFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('symbol') symbol?: string,
    @Query('apikey') apikey?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    try {
      this.logger.log(`Received request for crypto value for symbol(s): ${symbol || 'all'}`);

      // Handle symbol=all case
      if (!symbol || symbol.toLowerCase() === 'all') {
        this.logger.log('Fetching all available assets');
        const latestData = await this.postgresService.getCryptoPrices();
        if (!latestData || latestData.length === 0) {
          this.logger.warn('No data found for all assets');
          throw new NotFoundException('No data available');
        }
        this.logger.log(`Found data for ${latestData.length} assets`);

        const options: TransformationOptions = {
          dateFormat,
          timeFormat,
          includeKgx: !!includeKgx,
          format: 'value'
        };

        const formattedData = this.assetDataTransformer.transformToValueFormat(latestData, options);
        this.logger.log(`Returning values for all ${latestData.length} assets`);
        return formattedData;
      }

      // Handle multiple symbols (comma-separated)
      const symbolsToQuery = symbol.split(',').map(s => s.trim().toUpperCase());
      if (symbolsToQuery.length > 1) {
        // Fetch all requested symbols (crypto, metals, or both)
        const latestData = await this.postgresService.getCryptoPrices(symbolsToQuery);
        if (!latestData || latestData.length === 0) {
          this.logger.warn('No data found for requested symbols');
          throw new NotFoundException('No data available');
        }
        this.logger.log(`Found data for symbols: ${symbolsToQuery.join(', ')}`);

        const options: TransformationOptions = {
          dateFormat,
          timeFormat,
          includeKgx: !!includeKgx,
          format: 'value'
        };

        const formattedData = this.assetDataTransformer.transformToValueFormat(latestData, options);
        this.logger.log(`Returning values for symbols: ${symbolsToQuery.join(', ')}`);
        return formattedData;
      }

      // Single symbol logic (as before)
      const symbolToQuery = symbol.trim().toUpperCase();
      if (!/^[A-Z0-9-]+$/.test(symbolToQuery)) {
        this.logger.warn(`Invalid symbol format: ${symbolToQuery}`);
        throw new BadRequestException('Invalid symbol format. Only alphanumeric characters and hyphens are allowed.');
      }
      try {
        // Fetch the latest record for the symbol, regardless of asset_type
        const latestData = await this.postgresService.query(
          `WITH latest_times AS (
            SELECT symbol, MAX(timestamp) as latest_time
            FROM kgx
            WHERE symbol = $1
            GROUP BY symbol
          )
          SELECT d.*
          FROM kgx d
          JOIN latest_times lt ON d.symbol = lt.symbol AND d.timestamp = lt.latest_time
          WHERE d.symbol = $1
          LIMIT 1`,
          [symbolToQuery]
        );

        if (!latestData || latestData.length === 0) {
          this.logger.warn(`No data found for symbol: ${symbolToQuery}`);
          throw new NotFoundException(`No data available for symbol: ${symbolToQuery}`);
        }

        this.logger.log(`Found data for symbol: ${symbolToQuery}`);

        // Format the response using the transformer service
        const options: TransformationOptions = {
          dateFormat,
          timeFormat,
          includeKgx: !!includeKgx,
          format: 'value'
        };

        const formattedData = this.assetDataTransformer.transformToValueFormat(latestData, options);

        this.logger.log(`Returning value for ${symbol} in oil API format`);
        return formattedData;
      } catch (error: any) {
        if (error.code === 'ECONNREFUSED' || error.code === 'ETIMEDOUT') {
          throw new InternalServerErrorException('Database connection error. Please try again later.');
        }
        throw error;
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      const errorStack = error instanceof Error ? error.stack : 'No stack trace available';
      this.logger.error(`Error getting crypto value: ${errorMessage}`, errorStack);

      if (errorMessage.includes('timeout') || errorMessage.includes('connection')) {
        throw new InternalServerErrorException('Database connection error. Please try again later.');
      }

      throw new InternalServerErrorException('Failed to retrieve crypto value. Please try again later.');
    }
  }

  @Get('getPM')
  @ApiOperation({ summary: 'Get crypto data in precious metals API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching precious metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values (set to any value to enable)' })
  async getPreciousMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in PM format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array (uppercase, trimmed)
      const symbols = symbolParam && symbolParam.toLowerCase() !== 'all'
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;
      // Use service for safe, parameterized query
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No crypto data found');
        throw new NotFoundException('No data available');
      }

      this.logger.log(`Found ${latestData.length} crypto records`);

      const options: TransformationOptions = {
        dateFormat,
        timeFormat,
        includeKgx: !!includeKgx,
        format: 'precious-metals'
      };

      const response = this.assetDataTransformer.transformToPreciousMetalsFormat(latestData, options);

      this.logger.log(`Returning ${latestData.length} crypto records in PM format`);
      return response;
    } catch (error) {
      // Log and rethrow mapped exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getPM: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve crypto data');
    }
  }

  @Get('getBM')
  @ApiOperation({ summary: 'Get crypto data in base metals API format' })
  @ApiResponse({ status: 200, description: 'Return the crypto data in format matching base metals API' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated)' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getBaseMetalsFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in BM format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array
      const symbols = symbolParam && symbolParam.toLowerCase() !== 'all'
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;
      // Fetch latest records via service for safe queries
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No crypto data found');
        throw new NotFoundException('No data available');
      }

      this.logger.log(`Found ${latestData.length} crypto records`);

      const options: TransformationOptions = {
        dateFormat,
        timeFormat,
        includeKgx: !!includeKgx,
        format: 'base-metals'
      };

      const response = this.assetDataTransformer.transformToBaseMetalsFormat(latestData, options);

      this.logger.log(`Returning ${latestData.length} crypto records in BM format`);
      return response;
    } catch (error) {
      // Log and map exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getBM: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve crypto data');
    }
  }

  @Get('getCR')
  @ApiOperation({ summary: 'Get cryptocurrency data in crypto-specific format' })
  @ApiResponse({ status: 200, description: 'Return the cryptocurrency data in crypto-specific format' })
  @ApiQuery({ name: 'apikey', required: false, description: 'API key for authentication (legacy format)' })
  @ApiQuery({ name: 'symbol', required: false, description: 'Filter by cryptocurrency symbols (comma-separated) or "all" for all cryptos' })
  @ApiQuery({ name: 'type', required: false, description: 'Response format type (json by default)' })
  @ApiQuery({ name: 'ver', required: false, description: 'API version' })
  @ApiQuery({ name: 'df', required: false, description: 'Date format' })
  @ApiQuery({ name: 'tf', required: false, description: 'Time format' })
  @ApiQuery({ name: 'kgx', required: false, description: 'Include KGX values' })
  async getCryptoFormat(
    @Req() req: Request,
    @Res({ passthrough: true }) res: Response,
    @Query('apikey') apikey?: string,
    @Query('symbol') symbolParam?: string,
    @Query('type') type: string = 'json',
    @Query('ver') version?: string,
    @Query('df') dateFormat?: string,
    @Query('tf') timeFormat?: string,
    @Query('kgx') includeKgx?: string
  ) {
    this.logger.log(`Received request for crypto data in CR format, symbols: ${symbolParam || 'all'}`);

    try {
      // Prepare symbol filter array
      const symbols = symbolParam && symbolParam.toLowerCase() !== 'all'
        ? symbolParam.split(',').map(s => s.trim().toUpperCase())
        : undefined;

      // Fetch latest crypto records via service for safe queries
      const latestData = await this.postgresService.getCryptoPrices(symbols);
      if (!latestData || latestData.length === 0) {
        this.logger.warn('No cryptocurrency data found');
        throw new NotFoundException('No cryptocurrency data available');
      }

      this.logger.log(`Found ${latestData.length} cryptocurrency records`);

      const options: TransformationOptions = {
        dateFormat,
        timeFormat,
        includeKgx: !!includeKgx,
        format: 'crypto'
      };

      const response = this.assetDataTransformer.transformToCryptoFormat(latestData, options);

      this.logger.log(`Returning ${latestData.length} cryptocurrency records in CR format`);
      return response;
    } catch (error) {
      // Log and map exceptions
      const msg = error instanceof Error ? error.message : 'Unknown error';
      this.logger.error(`Error in getCR: ${msg}`, error instanceof Error ? error.stack : undefined);
      if (error instanceof NotFoundException) throw error;
      throw new InternalServerErrorException('Failed to retrieve cryptocurrency data');
    }
  }


}
