import React from "react";
import { DashboardLayout } from "@/components/layout/DashboardLayout";
import { CryptoTable } from "@/components/crypto/CryptoTable";
import { ThemeProvider } from "@/components/theme/ThemeProvider";
import { CryptoProvider } from "@/contexts/CryptoContext";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { RefreshCw } from "lucide-react";
import { useCryptoData } from "@/hooks/useApi";

const CryptoDataPage = () => {
  const { refetch } = useCryptoData();

  return (
    <ThemeProvider>
      <DashboardLayout>
        <div className="space-y-6">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold tracking-tight">
                KGX Data
              </h1>
              <p className="text-muted-foreground">
                Real-time data with KGX values
              </p>
            </div>
            <Button onClick={() => refetch()} size="sm" variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              Refresh Data
            </Button>
          </div>

          <Card>
            <CryptoProvider>
              <CryptoTable />
            </CryptoProvider>
          </Card>
        </div>
      </DashboardLayout>
    </ThemeProvider>
  );
};

export default CryptoDataPage;
