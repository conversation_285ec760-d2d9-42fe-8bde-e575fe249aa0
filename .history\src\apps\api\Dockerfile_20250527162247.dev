# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root temporarily to copy files
USER root

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/api ./apps/api/

# Switch back to non-root user
USER nextjs

# Set service-specific environment variables
ENV PORT=3000

# Expose port
EXPOSE 3000

# Development command with hot reload
CMD ["nodemon", "--watch", "apps/api", "--watch", "libs", "--ext", "ts,js,json", "--exec", "ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/api/src/main.ts"] 