FROM node:22-alpine

WORKDIR /app

# Install development dependencies
RUN apk add --no-cache python3 make g++

# Copy package files
COPY package.json ./
COPY nx.json tsconfig.json tsconfig.base.json tsconfig.node.json ./

# Install all dependencies (including dev dependencies)
RUN yarn config set network-timeout 300000 && \
    yarn install --network-timeout 300000 && \
    yarn global add nx nodemon ts-node

# Rebuild native dependencies for Alpine Linux
RUN yarn rebuild sqlite3 better-sqlite3 || true

# Copy source code (this will be overridden by volume mounts in development)
COPY libs ./libs/
COPY config ./config/
COPY apps/processor ./apps/processor/

# Set environment variables
ENV NODE_ENV=development \
    PORT=3003

# Expose port
EXPOSE 3003

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/processor", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/processor/src/main.ts"] 