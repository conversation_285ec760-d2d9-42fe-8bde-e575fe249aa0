{"types": ["@hapi/shot"], "compileOnSave": false, "compilerOptions": {"rootDir": ".", "sourceMap": true, "declaration": false, "moduleResolution": "node", "emitDecoratorMetadata": true, "experimentalDecorators": true, "importHelpers": true, "target": "es2020", "module": "esnext", "lib": ["es2020", "dom"], "skipLibCheck": true, "skipDefaultLibCheck": true, "baseUrl": ".", "allowSyntheticDefaultImports": true, "esModuleInterop": true, "useDefineForClassFields": false, "strict": true, "strictPropertyInitialization": false, "forceConsistentCasingInFileNames": true, "paths": {"@data-pipeline/cache": ["libs/cache/src/index.ts"], "@data-pipeline/config": ["libs/config/src/index.ts"], "@data-pipeline/core": ["libs/core/src/index.ts"], "@data-pipeline/kgx": ["libs/kgx/src/index.ts"], "@data-pipeline/logging": ["libs/logging/src/index.ts"], "@data-pipeline/messaging": ["libs/messaging/src/index.ts"], "@data-pipeline/monitoring": ["libs/monitoring/src/index.ts"], "@data-pipeline/security": ["libs/security/src/index.ts"], "@data-pipeline/storage": ["libs/storage/src/index.ts"], "@data-pipeline/utils": ["libs/utils/src/index.ts"]}}, "exclude": ["node_modules", "tmp"]}