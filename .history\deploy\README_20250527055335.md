# KGX Crypto Data Application - Deployment Scripts

This directory contains comprehensive deployment scripts for setting up and managing the KGX Crypto Data Application on Debian 12 servers.

## Overview

The KGX application is a microservices-based crypto data platform that provides real-time cryptocurrency data through multiple API endpoints. It consists of 6 main services:

- **API Service** (Port 3000): Main API endpoints for crypto data
- **Scheduler Service** (Port 3001): Task scheduling and coordination
- **Collector Service** (Port 3002): Data collection from external sources
- **Processor Service** (Port 3003): Data processing and transformation
- **Writer Service** (Port 3004): Data persistence and storage
- **Dashboard Service** (Port 3010): Web-based monitoring dashboard

## Scripts Overview

### 1. `install.sh` - Server Installation Script
Sets up the server with all required dependencies and system configuration.

**Features:**
- OS compatibility check (Debian 12)
- Docker and Docker Compose installation
- Node.js 20 installation
- Nginx reverse proxy setup
- SSL certificate support (Certbot)
- Firewall configuration (UFW)
- Security hardening (fail2ban)
- Log rotation setup

### 2. `deploy.sh` - Application Deployment Script
Handles the complete application deployment process.

**Features:**
- Repository management (clone/update)
- Environment configuration generation
- Secure password and API key generation
- Docker Compose orchestration
- Database initialization
- Service health verification
- SSL certificate setup
- Systemd service creation

### 3. `backup.sh` - Backup and Restore Script
Comprehensive backup solution for all application data.

**Features:**
- Full system backup (databases, config, logs)
- Database-only backup option
- Backup verification and integrity checks
- Automated cleanup of old backups
- Point-in-time restore functionality
- Backup manifest generation

### 4. `monitor.sh` - Monitoring and Alerting Script
Real-time monitoring with alerting capabilities.

**Features:**
- Service health monitoring
- API endpoint testing
- Database connectivity checks
- System resource monitoring
- Log analysis for errors
- External connectivity verification
- SSL certificate expiration monitoring
- Email and webhook alerts

## Quick Start

### Prerequisites
- Debian 12 (Bookworm) server
- Root or sudo access
- Internet connectivity
- Access to Kitco internal network (for Hades database)

### Step 1: Initial Server Setup
```bash
# Download and run the installation script
wget https://your-repo/deploy/install.sh
chmod +x install.sh
./install.sh
```

### Step 2: Deploy the Application
```bash
# Set environment variables (optional)
export REPO_URL="https://github.com/your-org/kgx-app.git"
export DOMAIN="your-domain.com"
export EMAIL="<EMAIL>"

# Run deployment
./deploy.sh
```

### Step 3: Configure External Credentials
```bash
# Edit the environment file with actual credentials
nano /opt/kgx-app/src/.env.local

# Update the following:
# - HADES_USER and HADES_PASSWORD
# - KDS2_API_KEY
# - Any other external API keys

# Restart the application
sudo systemctl restart kgx-app
```

### Step 4: Setup Monitoring (Optional)
```bash
# Setup monitoring service
./monitor.sh setup

# Start monitoring
sudo systemctl start kgx-monitor

# Test alerts
./monitor.sh test-alert
```

## Environment Variables

### Deployment Configuration
```bash
REPO_URL="https://github.com/your-org/kgx-app.git"  # Repository URL
BRANCH="main"                                        # Git branch
DOMAIN="your-domain.com"                            # Domain name
EMAIL="<EMAIL>"                       # Admin email
```

### Application Configuration
```bash
# Database Credentials (auto-generated if not set)
POSTGRES_PASSWORD="secure_password"
MYSQL_ROOT_PASSWORD="secure_password"
MYSQL_PASSWORD="secure_password"
REDIS_PASSWORD="secure_password"

# External Database (Hades)
HADES_SERVER="hades.kitco.com"
HADES_PORT="5432"
HADES_USER="your_username"
HADES_PASSWORD="your_password"

# External APIs
KDS2_API_KEY="your_api_key"
```

### Monitoring Configuration
```bash
ALERT_EMAIL="<EMAIL>"                 # Alert email
ALERT_WEBHOOK="https://hooks.slack.com/..."         # Webhook URL
CHECK_INTERVAL="60"                                  # Check interval (seconds)
CPU_THRESHOLD="80"                                   # CPU alert threshold (%)
MEMORY_THRESHOLD="80"                                # Memory alert threshold (%)
DISK_THRESHOLD="85"                                  # Disk alert threshold (%)
RESPONSE_TIME_THRESHOLD="5000"                       # API response time (ms)
```

### Backup Configuration
```bash
RETENTION_DAYS="7"                                   # Backup retention (days)
COMPRESS="true"                                      # Compress backups
```

## API Endpoints

After deployment, the following endpoints will be available:

### Health Check
```bash
curl https://your-domain.com/api/health
```

### Crypto Data Endpoints
```bash
# Get single crypto value
curl "https://your-domain.com/kgx-crypto-data/getValue?symbol=BTC"

# Get precious metals format
curl "https://your-domain.com/kgx-crypto-data/getPM"

# Get base metals format
curl "https://your-domain.com/kgx-crypto-data/getBM"
```

### Dashboard
```bash
# Access web dashboard
https://your-domain.com/
```

## Management Commands

### Application Management
```bash
# View application status
./deploy.sh status

# View application logs
./deploy.sh logs

# Restart application
./deploy.sh restart

# Stop application
./deploy.sh stop

# Start application
./deploy.sh start

# Update application
./deploy.sh update
```

### Backup Management
```bash
# Create full backup
./backup.sh full

# Create database-only backup
./backup.sh database

# List available backups
./backup.sh list

# Restore from backup
./backup.sh restore 20241201_143022

# Cleanup old backups
./backup.sh cleanup
```

### Monitoring
```bash
# Run monitoring check
./monitor.sh check

# Generate status report
./monitor.sh report

# Test alert system
./monitor.sh test-alert
```

### System Services
```bash
# Application service
sudo systemctl status kgx-app
sudo systemctl restart kgx-app
sudo systemctl stop kgx-app
sudo systemctl start kgx-app

# Monitoring service
sudo systemctl status kgx-monitor
sudo systemctl restart kgx-monitor

# View service logs
journalctl -u kgx-app -f
journalctl -u kgx-monitor -f
```

## Directory Structure

```
/opt/kgx-app/
├── src/                          # Application source code
│   ├── .env                      # Environment configuration
│   ├── .env.local               # Local environment overrides
│   ├── docker-compose.yml      # Docker Compose configuration
│   ├── docker-compose.prod.yml # Production overrides
│   └── apps/                    # Application services
├── data/                        # Persistent data
│   ├── postgres/               # PostgreSQL data
│   ├── mysql/                  # MySQL data
│   ├── redis/                  # Redis data
│   ├── logs/                   # Application logs
│   └── backups/                # Backup files
├── config/                     # Configuration files
└── scripts/                    # Custom scripts
```

## Security Considerations

### Firewall Configuration
- SSH (22/tcp): Allowed
- HTTP (80/tcp): Allowed
- HTTPS (443/tcp): Allowed
- Application ports: Restricted to localhost only

### SSL/TLS
- Automatic SSL certificate generation via Let's Encrypt
- HTTPS redirect for all traffic
- Security headers configured

### Database Security
- Strong auto-generated passwords
- Database access restricted to Docker network
- Regular backup encryption

### Application Security
- JWT-based authentication
- API rate limiting
- Input validation and sanitization
- Security headers in Nginx

## Troubleshooting

### Common Issues

#### Services Not Starting
```bash
# Check Docker status
sudo systemctl status docker

# Check service logs
docker compose logs -f

# Check system resources
df -h
free -h
```

#### Database Connection Issues
```bash
# Check database health
docker compose exec postgres pg_isready -U postgres
docker compose exec mysql mysqladmin ping -u root -p

# Check network connectivity
docker network ls
docker network inspect kgx-app_default
```

#### API Endpoints Not Responding
```bash
# Check API service logs
docker compose logs api

# Check Nginx configuration
sudo nginx -t
sudo systemctl status nginx

# Test internal connectivity
curl http://localhost:3000/api/health
```

#### External Database (Hades) Connectivity
```bash
# Test network connectivity
ping hades.kitco.com
telnet hades.kitco.com 5432

# Check credentials in environment file
grep HADES /opt/kgx-app/src/.env.local
```

### Log Locations
- Application logs: `/opt/kgx-app/data/logs/`
- Nginx logs: `/var/log/nginx/`
- System logs: `/var/log/syslog`
- Monitoring logs: `/var/log/kgx-monitor.log`

### Performance Tuning

#### Database Optimization
```bash
# PostgreSQL
docker compose exec postgres psql -U postgres -c "SHOW shared_buffers;"

# MySQL
docker compose exec mysql mysql -u root -p -e "SHOW VARIABLES LIKE 'innodb_buffer_pool_size';"
```

#### Resource Monitoring
```bash
# Monitor resource usage
htop
docker stats
df -h
```

## Maintenance

### Regular Tasks
1. **Daily**: Monitor application health and logs
2. **Weekly**: Review backup integrity and cleanup
3. **Monthly**: Update system packages and security patches
4. **Quarterly**: Review and update SSL certificates

### Update Procedure
1. Create backup before updates
2. Test updates in staging environment
3. Schedule maintenance window
4. Apply updates using deployment scripts
5. Verify functionality post-update

### Backup Strategy
- **Frequency**: Daily automated backups
- **Retention**: 7 days (configurable)
- **Storage**: Local storage with optional remote sync
- **Testing**: Monthly restore testing

## Support

### Getting Help
1. Check application logs for error messages
2. Review monitoring alerts and status reports
3. Consult troubleshooting section
4. Contact system administrator

### Reporting Issues
When reporting issues, include:
- Error messages from logs
- System status report (`./monitor.sh report`)
- Steps to reproduce the issue
- Expected vs actual behavior

## License

This deployment package is part of the KGX Crypto Data Application project.
Please refer to the main project license for usage terms. 