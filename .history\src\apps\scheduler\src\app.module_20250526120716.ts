import { forwardRef, Module } from "@nestjs/common";
import { ConfigModule } from "@nestjs/config";
import { ScheduleModule } from "@nestjs/schedule";
import { TypeOrmModule } from "@nestjs/typeorm";
import { EventEmitterModule } from "@nestjs/event-emitter";
import { PinoLoggerModule } from "@data-pipeline/logging";
import { InfrastructureModule } from "./infrastructure/infrastructure.module";
import { DomainModule } from "./domain/domain.module";
import { ApplicationModule } from "./application/application.module";
import { PresentationModule } from "./presentation/presentation.module";
import { JwtModule } from "@nestjs/jwt";
import { SecurityModule } from "@data-pipeline/security";

@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: [".env.local"],
    }),
    PinoLoggerModule.register({
      serviceName: "scheduler-service",
      logLevel: process.env.LOG_LEVEL || "info",
      prettyPrint: process.env.NODE_ENV !== "production",
    }),
    ScheduleModule.forRoot(),
    EventEmitterModule.forRoot({
      wildcard: true,
      delimiter: ".",
      newListener: false,
      removeListener: false,
      maxListeners: 10,
      verboseMemoryLeak: false,
      ignoreErrors: false,
    }),
    JwtModule.register({
      global: true,
      secret: process.env.JWT_SECRET || "secret",
      signOptions: { expiresIn: "1h" },
    }),
    TypeOrmModule.forRoot({
      name: "mysql",
      type: "mysql",
      host:
        process.env.NODE_ENV === "development"
          ? "localhost"
          : process.env.MYSQL_HOST || "mysql",
      port: parseInt(process.env.MYSQL_PORT || "3306", 10),
      username: process.env.MYSQL_USERNAME || "kitco_user",
      password: process.env.MYSQL_PASSWORD || "password",
      database: process.env.MYSQL_DATABASE || "kitco",
      entities: [],
      synchronize: false,
      autoLoadEntities: true,
      retryAttempts: 20,
      retryDelay: 3000,
      keepConnectionAlive: true, // Keep connection alive
      extra: {
        connectionLimit: 10,
        waitForConnections: true,
        connectTimeout: 60000, // 60 seconds connection timeout
      },
    }),
    TypeOrmModule.forFeature([], "mysql"),
    SecurityModule.register({
      jwt: {
        secret: process.env.JWT_SECRET || "secret",
        expiresIn: process.env.JWT_EXPIRES_IN || "3600s",
        issuer:
          process.env.SCHEDULER_SERVICE_URL ||
          `http://${process.env.SCHEDULER_SERVICE_HOST}:3001`,
        audience: process.env.API_SERVICE_URL || "*",
      },
      apiKey: {
        keys: {
          [process.env.API_KEY || "default_key"]: {
            name: "default",
            roles: [],
            permissions: [],
          },
        },
      },
    }),
    // forwardRef(() => InfrastructureModule),
    forwardRef(() => DomainModule),
    forwardRef(() => ApplicationModule),
    forwardRef(() => PresentationModule),
  ],
})
export class AppModule {}
