import { Injectable, Logger, OnModuleInit } from "@nestjs/common";
import { BullMQService, WorkflowService } from "@data-pipeline/messaging";
import { SchedulerService } from "./scheduler.service";
import { JobService } from "../../domain/services/job.service";
import { getErrorMessage, getErrorStack } from "@data-pipeline/utils";
import { WorkflowData, WorkflowLogger } from "@data-pipeline/core";

@Injectable()
export class WorkflowSchedulerService implements OnModuleInit {
  private readonly logger = new Logger(WorkflowSchedulerService.name);
  private readonly workflowLogger = new WorkflowLogger('scheduler');

  constructor(
    private readonly workflowService: WorkflowService,
    private readonly bullMQService: BullMQService,
    private readonly schedulerService: SchedulerService,
    private readonly jobService: JobService
  ) { }

  async onModuleInit() {
    await this.workflowService.configReady();
    await this.setupWorkflowQueues();
  }

  private async setupWorkflowQueues() {
    try {
      await this.workflowService.setupWorkflowQueues();
      this.logger.log("Workflow queues set up successfully");
    } catch (error: any) {
      this.logger.error(`Error setting up workflow queues: ${getErrorMessage(error)}`, getErrorStack(error));
    }
  }

  async startWorkflow(workflowId: string, data: any) {
    try {
      const workflow = this.workflowService.getWorkflow(workflowId);
      if (!workflow) {
        throw new Error(`Workflow ${workflowId} not found`);
      }

      if (!workflow.enabled) {
        throw new Error(`Workflow ${workflowId} is disabled`);
      }

      const firstStep = workflow.steps[0];
      if (firstStep.service !== "scheduler") {
        throw new Error(`First step of workflow ${workflowId} must be a scheduler step`);
      }

      await this.workflowService.executeWorkflowStep(
        workflowId,
        firstStep.service,
        firstStep.action,
        data
      );

      this.logger.log(`Started workflow ${workflowId} with data: ${JSON.stringify(data)}`);
      return { success: true, workflowId, message: `Workflow ${workflowId} started` };
    } catch (error: any) {
      this.logger.error(`Error starting workflow ${workflowId}: ${getErrorMessage(error)}`, getErrorStack(error));
      throw error;
    }
  }

  async handleCollectionJob(job: any) {
    try {
      const workflowData = job.data as WorkflowData;
      const { _workflow, ...jobData } = workflowData;

      this.workflowLogger.logDataReceived(workflowData);

      const result = await this.jobService.runJobImmediately(jobData.id || jobData.job_id);

      if (_workflow && _workflow.next) {
        const nextStep = this.workflowService.getNextStep(_workflow.id, _workflow.step.service);

        if (nextStep) {
          const nextWorkflowData: WorkflowData = {
            success: result.success,
            metadata: result.metadata,
            collectorId: jobData.collectorId || jobData.collector_id,
            jobId: jobData.id || jobData.job_id,
          };

          await this.workflowService.executeWorkflowStep(
            _workflow.id,
            nextStep.service,
            nextStep.action,
            nextWorkflowData
          );

          this.logger.log(
            `Continued workflow ${_workflow.id} to next step: ${nextStep.service}:${nextStep.action}`
          );
        } else {
          this.logger.log(`Workflow ${_workflow.id} completed at step: ${_workflow.step.service}:${_workflow.step.action}`);
        }
      }

      return result;
    } catch (error: any) {
      this.logger.error(`Error handling collection job: ${getErrorMessage(error)}`, getErrorStack(error));
      throw error;
    }
  }
}
