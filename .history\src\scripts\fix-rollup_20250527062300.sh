#!/bin/bash

# Fix Rollup native dependency issue on Linux, macOS, and Windows
echo "Fixing Rollup native dependencies..."

# Detect platform
PLATFORM=$(uname -s)
ARCH=$(uname -m)

echo "Platform: $PLATFORM, Architecture: $ARCH"

# Install appropriate Rollup native binary
if [[ "$PLATFORM" == "Linux" ]]; then
    if [[ "$ARCH" == "x86_64" ]]; then
        echo "Installing Rollup for Linux x64..."
        yarn add @rollup/rollup-linux-x64-gnu --optional -W || {
            echo "Failed to install via yarn, trying npm..."
            npm install @rollup/rollup-linux-x64-gnu --save-optional --no-save
        }
    elif [[ "$ARCH" == "aarch64" ]]; then
        echo "Installing Rollup for Linux ARM64..."
        yarn add @rollup/rollup-linux-arm64-gnu --optional -W || {
            echo "Failed to install via yarn, trying npm..."
            npm install @rollup/rollup-linux-arm64-gnu --save-optional --no-save
        }
    fi
elif [[ "$PLATFORM" == "Darwin" ]]; then
    if [[ "$ARCH" == "x86_64" ]]; then
        echo "Installing Rollup for macOS x64..."
        yarn add @rollup/rollup-darwin-x64 --optional -W
    elif [[ "$ARCH" == "arm64" ]]; then
        echo "Installing Rollup for macOS ARM64..."
        yarn add @rollup/rollup-darwin-arm64 --optional -W
    fi
elif [[ "$PLATFORM" =~ ^MINGW64 ]] || [[ "$PLATFORM" =~ ^MSYS ]] || [[ "$PLATFORM" =~ ^CYGWIN ]]; then
    echo "Installing Rollup for Windows x64..."
    yarn add @rollup/rollup-win32-x64-msvc --optional -W || {
        echo "Failed to install via yarn, trying npm..."
        npm install @rollup/rollup-win32-x64-msvc --save-optional --no-save
    }
fi

echo "Rollup fix completed." 