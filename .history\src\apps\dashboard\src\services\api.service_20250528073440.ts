/**
 * API Service
 *
 * This service handles all communication with the API gateway.
 * It provides methods for fetching data and creating SSE connections.
 */

import { API_CONFIG } from "../config/api.config";
import {
  CryptoData,
  KgxFormula,
  SystemHealth,
  ApiError,
  CryptoHistory,
  InfrastructureHealth,
  ServiceHealth,
} from "../types/api.types";
import { toast } from "@/components/ui/sonner";

/**
 * Interface for raw crypto data that may have different property names
 */
interface RawCryptoItem {
  Symbol: string;
  name?: string;
  price?: number | string;
  currentPrice?: number | string;
  kgxValue?: number | string;
  kgx_value?: number | string;
  currentUsdIndex?: number | string;
  usd_index?: number | string;
  previousUsdIndex?: number | string;
  prev_usd_index?: number | string;
  usdRatio?: number | string;
  marketStatus?: string;
  timestamp?: string;
  metadata?: Record<string, unknown>;
  category?: string;
  source?: string;
  changeDueToUsd?: number | string;
  changeDueToUsdPercent?: number | string;
  changeDueToTrading?: number | string;
  changeDueToTradingPercent?: number | string;
  change?: number | string;
  change_percent?: number | string;
  [key: string]:
  | string
  | number
  | boolean
  | undefined
  | null
  | Record<string, unknown>; // Allow other properties
}

/**
 * Interface for SSE event data with typed events
 */
interface SseEvent<T = unknown> {
  type: string;
  data: T;
  timestamp?: string;
  source?: string;
}

/**
 * Interface for KGX Crypto SSE event data
 */
interface KgxCryptoSseEvent extends SseEvent<CryptoData[]> {
  type: "crypto";
}

/**
 * Type definition for an HTTP Error with potential response status
 */
type HttpError = Error & {
  status?: number;
  statusCode?: number;
  response?: { status: number };
};

export interface CryptoApiResponse {
  data: CryptoData[];
  timestamp: string;
}

export class CryptoApiService {
  private static baseUrl = `${API_CONFIG.baseUrl}/v1/kgx`;
  private static apiKey = API_CONFIG.apiKey;

  private static getHeaders() {
    return {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'x-api-key': this.apiKey || '',
    };
  }

  /**
   * Fetches crypto data from the API
   * @param symbols Comma-separated list of symbols to fetch, or 'all' for all available assets (crypto, precious metals, base metals)
   */
  static async fetchCryptoData(symbols: string = 'all'): Promise<CryptoData[]> {
    try {
      console.log('Fetching crypto data from API...');
      console.log('Symbols:', symbols);

      // Build the URL with query parameters
      const params = new URLSearchParams({
        symbol: symbols,
        type: 'json',
        apikey: this.apiKey
      });

      const url = `${this.baseUrl}/getValue?${params.toString()}`;
      console.log('Request URL:', url);

      const response = await fetch(url);
      console.log('Response:', response);

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Error response:', errorText);
        throw new Error(`HTTP error! status: ${response.status}: ${errorText}`);
      }

      const data = await response.json();
      console.log('Raw API response:', data);

      let rawData: any[] = [];

      // The new KGX API returns data directly as an array for getValue endpoint
      // Handle both array response and object response formats
      if (Array.isArray(data)) {
        rawData = data;
      } else {
        // Fallback for other response formats (getPM, getBM, getCR)
        rawData = data?.CryptoMetals?.crypto || data?.BaseMetals?.BM || data?.Cryptocurrencies?.CR || [];
      }

      // Transform the data to match the expected CryptoData interface
      return rawData.map(item => this.transformApiDataToCryptoData(item));
    } catch (error) {
      console.error('Error fetching crypto data:', error);
      throw error;
    }
  }

  /**
   * Transform API response data to match the expected CryptoData interface
   * @param item Raw API response item
   * @returns Transformed CryptoData object
   */
  private static transformApiDataToCryptoData(item: any): CryptoData {
    console.log('Transforming API item:', item);

    // Handle different field name conventions from the new API
    return {
      // Core fields - map from new API format
      Symbol: item.Symbol || item.symbol,
      Currency: item.Currency || 'USD',
      Unit: item.Unit || 'SHARES',
      Timestamp: item.Timestamp || item.timestamp || new Date().toISOString(),

      // Price levels - map from new API format
      Ask: parseFloat(item.Ask || item.ask || item.price || 0),
      Bid: parseFloat(item.Bid || item.bid || item.price || 0),
      Mid: parseFloat(item.Mid || item.mid || item.price || 0),
      High: parseFloat(item.High || item.high || item.price || 0),
      Low: parseFloat(item.Low || item.low || item.price || 0),

      // Change values - map from new API format
      Change: parseFloat(item.Change || item.change || item.ChangeTrade || 0),
      ChangePercentage: parseFloat(item.ChangePercentage || item.changePercentage || item.ChangePercentTrade || 0),
      ChangeTrade: parseFloat(item.ChangeTrade || item.changeTrade || item.Change || 0),
      ChangePercentTrade: parseFloat(item.ChangePercentTrade || item.changePercentTrade || item.ChangePercentage || 0),
      ChangeUSD: parseFloat(item.ChangeUSD || item.changeUSD || item.Change || 0),
      ChangePercentUSD: parseFloat(item.ChangePercentUSD || item.changePercentUSD || item.ChangePercentage || 0),

      // Metadata and additional fields
      name: item.Name || item.name || item.Symbol || item.symbol,
      category: this.determineAssetCategory(item),
      price: parseFloat(item.Price || item.price || item.Mid || 0),
      currentPrice: parseFloat(item.Price || item.price || item.Mid || 0),
      marketStatus: 'OPEN' as const,
      lastUpdated: item.Timestamp || item.timestamp || new Date().toISOString(),

      // KGX specific fields
      KgxValue: parseFloat(item.KgxValue || item.kgxValue || item.kgx_value || 0),

      metadata: {
        source: 'kgx-api',
        apiVersion: 'v1',
        assetType: item.asset_type || 'CRYPTOCURRENCY',
        ...item.metadata
      }
    };
  }

  /**
   * Determine the asset category based on the item data
   * @param item Raw API response item
   * @returns Asset category string
   */
  private static determineAssetCategory(item: any): string {
    // Check if asset_type is provided in the response
    if (item.asset_type) {
      switch (item.asset_type.toUpperCase()) {
        case 'CRYPTOCURRENCY':
          return 'crypto';
        case 'PRECIOUS_METALS':
          return 'precious-metals';
        case 'BASE_METALS':
          return 'base-metals';
        case 'FOREX':
          return 'forex';
        default:
          return 'crypto'; // Default fallback
      }
    }

    // Fallback: determine by symbol patterns
    const symbol = (item.Symbol || item.symbol || '').toUpperCase();

    // Common precious metals (including chemical symbols)
    if (['GOLD', 'SILVER', 'PLATINUM', 'PALLADIUM', 'XAU', 'XAG', 'XPT', 'XPD', 'AU', 'AG', 'PT', 'PD'].includes(symbol)) {
      return 'precious-metals';
    }

    // Common base metals (including chemical symbols)
    if (['COPPER', 'ALUMINUM', 'ZINC', 'NICKEL', 'LEAD', 'TIN', 'CU', 'AL', 'ZN', 'NI', 'PB', 'SN'].includes(symbol)) {
      return 'base-metals';
    }

    // Energy commodities
    if (['OIL', 'GAS', 'WTI', 'BRENT', 'CRUDE', 'GASOLINE', 'HEATING', 'NATURAL'].includes(symbol)) {
      return 'energies';
    }

    // Common forex pairs
    if (symbol.length === 6 && symbol.includes('USD')) {
      return 'forex';
    }

    // Default to crypto for everything else
    return 'crypto';
  }

  static createEventSource(onMessage: (data: CryptoData[]) => void) {
    // Use the SSE endpoint which is at /api/sse/crypto
    let sseUrl = `${API_CONFIG.baseUrl}/sse/crypto`;
    const headers = this.getHeaders();

    // For browsers that support custom headers with EventSource
    if (headers['x-api-key']) {
      sseUrl += `${sseUrl.includes('?') ? '&' : '?'}apiKey=${encodeURIComponent(headers['x-api-key'])}`;
    }

    const eventSource = new EventSource(sseUrl);

    eventSource.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data);
        if (data?.data) {
          onMessage(Array.isArray(data.data) ? data.data : [data.data]);
        }
      } catch (error) {
        console.error('Error parsing SSE message:', error);
      }
    };

    eventSource.onerror = (error) => {
      console.error('SSE Error:', error);
      // Consider implementing reconnection logic here
      if (eventSource.readyState === EventSource.CLOSED) {
        console.log('SSE connection closed by the server');
      }
    };

    return eventSource;
  }
}

/**
 * Handles API errors and returns a standardized error object
 */
const handleApiError = (
  error: Error | HttpError | unknown,
  endpoint: string
): ApiError => {
  console.error(`API Error (${endpoint}):`, error);

  // Safely extract properties with type narrowing
  const errorMessage =
    error instanceof Error ? error.message : "An unknown error occurred";

  // Try to extract status code from various error formats
  let statusCode = 500;
  if (error instanceof Error) {
    if ("status" in error) statusCode = (error as HttpError).status || 500;
    if ("statusCode" in error)
      statusCode = (error as HttpError).statusCode || 500;
    // Interface for errors with response property
    interface ErrorWithResponse {
      response?: { status: number };
    }

    if (
      "response" in error &&
      (error as ErrorWithResponse).response?.status !== undefined
    ) {
      statusCode = (error as ErrorWithResponse).response!.status;
    }
  }

  const apiError: ApiError = {
    message: errorMessage,
    statusCode: statusCode,
    timestamp: new Date().toISOString(),
    path: endpoint,
  };

  // Show toast notification for API errors
  toast.error(`API Error: ${apiError.message}`);

  return apiError;
};

/**
 * API Service class
 */
class ApiService {
  /**
   * Get a friendly name for a cryptocurrency symbol
   * @param symbol The cryptocurrency symbol to get a name for
   */
  private getCryptoName(symbol: string): string {
    const names: Record<string, string> = {
      BTC: "Bitcoin",
      ETH: "Ethereum",
      USDT: "Tether",
      BNB: "Binance Coin",
      XRP: "Ripple",
      DOGE: "Dogecoin",
      ADA: "Cardano",
      DOT: "Polkadot",
      SOL: "Solana",
      LINK: "Chainlink",
      AVAX: "Avalanche",
      LTC: "Litecoin",
      MATIC: "Polygon",
      UNI: "Uniswap",
      SHIB: "Shiba Inu",
      TRX: "TRON",
      XLM: "Stellar",
      SUI: "Sui",
    };

    return names[symbol] || symbol;
  }
  /**
   * Get the appropriate base URL based on configuration
   * @param endpoint The endpoint being requested
   * @returns The appropriate base URL and whether to use credentials
   */
  private getBaseUrl(endpoint: string): {
    url: string;
    useCredentials: boolean;
  } {
    // Always use the API gateway - no direct connections to microservices
    console.log(`Using API gateway for ${endpoint}`);

    // Return API gateway URL with credentials
    return {
      url: API_CONFIG.baseUrl,
      useCredentials: true,
    };
  }

  /**
   * Normalize crypto data to ensure it matches the CryptoData interface
   * This handles different field naming conventions from different API sources
   * without adding fallbacks or calculations
   * @param item Raw crypto data item from API
   * @param formulaData Optional formula data to include in metadata
   */
  private normalizeCryptoData(
    item: RawCryptoItem,
    formulaData: KgxFormula | null = null
  ): CryptoData {
    if (!item || typeof item !== "object") {
      throw new Error(
        `Invalid crypto data item received: ${JSON.stringify(item)}`
      );
    }

    console.log('normalizeCryptoData', item);

    // Extract required price values without fallbacks
    const price = this.parseRequiredFloat(item.currentPrice || item.price, 'price');
    const kgxValue = this.parseRequiredFloat(item.kgxValue || item.kgx_value, 'kgxValue');
    const currentUsdIndex = this.parseRequiredFloat(item.currentUsdIndex || item.usd_index, 'currentUsdIndex');
    const previousUsdIndex = this.parseRequiredFloat(item.previousUsdIndex || item.prev_usd_index, 'previousUsdIndex');

    // Extract change values without calculations or fallbacks
    const changeDueToUsd = this.parseRequiredFloat(item.changeDueToUsd || item.change_due_usd, 'changeDueToUsd');
    const changeDueToUsdPercent = this.parseRequiredFloat(item.changeDueToUsdPercent || item.change_due_usd_percent, 'changeDueToUsdPercent');
    const changeDueToTrade = this.parseRequiredFloat(item.changeDueToTrading || item.change_due_trade, 'changeDueToTrade');
    const changeDueToTradePercent = this.parseRequiredFloat(item.changeDueToTradingPercent || item.change_due_trade_percent, 'changeDueToTradePercent');
    const change = this.parseRequiredFloat(item.change || item.price_change, 'change');
    const changePercent = this.parseRequiredFloat(item.change_percent, 'change_percent');

    // Parse metadata without defaults
    const metadata = item.metadata && typeof item.metadata === "object"
      ? {
        ...item.metadata,
        // Include formula data if provided
        ...(formulaData ? { formula: formulaData } : {}),
      }
      : {
        data_type: "crypto",
        category: "crypto",
      };

    // Parse market status
    const marketStatus = typeof item.marketStatus === "string"
      ? item.marketStatus.toUpperCase() === "OPEN" ? "OPEN" : "CLOSED"
      : "CLOSED";

    // Parse timestamp with validation
    let timestamp: string;
    if (typeof item.timestamp === "string" && item.timestamp) {
      timestamp = item.timestamp;
    } else if (typeof item.lastUpdated === "string" && item.lastUpdated) {
      timestamp = item.lastUpdated;
    } else {
      throw new Error(`Missing required timestamp in crypto data item`);
    }

    // Parse category
    const category = typeof item.category === "string"
      ? item.category.toLowerCase()
      : "crypto";

    return {
      // Core fields
      Symbol: item.Symbol,
      Currency: item.Currency as string,
      Unit: item.Unit as string,
      Timestamp: timestamp,

      // Price fields
      Ask: item.Ask as number,
      Bid: item.Bid as number,
      Mid: item.Mid as number,
      High: item.High as number,
      Low: item.Low as number,

      // Change fields
      Change: item.Change as number,
      ChangePercentage: item.ChangePercentage as number,
      ChangeTrade: item.ChangeTrade as number,
      ChangePercentTrade: item.ChangePercentTrade as number,
      ChangeUSD: item.ChangeUSD as number,
      ChangePercentUSD: item.ChangePercentUSD as number,

      // Metadata
      name: item.name || this.getCryptoName(item.Symbol) || item.Symbol,
      category,
      metadata,

      // Backward compatibility
      price,
      currentPrice: price,
      marketStatus: marketStatus as "OPEN" | "CLOSED",
      lastUpdated: timestamp,
    } as CryptoData;
  }

  /**
   * Parse a float value from an item field, throwing an error if the value is missing or invalid
   * @param value The value to parse
   * @param fieldName The name of the field for error reporting
   * @returns The parsed float value
   */
  private parseRequiredFloat(value: unknown, fieldName: string): number {
    if (value === undefined || value === null) {
      throw new Error(`Missing required field '${fieldName}' in crypto data item`);
    }

    const parsed = parseFloat(String(value));
    if (isNaN(parsed)) {
      throw new Error(`Invalid value for '${fieldName}': ${value}`);
    }

    return parsed;
  }

  /**
   * Fetch system health data
   */
  async getSystemHealth(): Promise<SystemHealth | null> {
    try {
      const response = await fetch(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.systemHealth}`,
        {
          credentials: "include", // Include credentials (cookies) with the request
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch system health: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      handleApiError(error, API_CONFIG.endpoints.systemHealth);
      return null;
    }
  }

  /**
   * Fetch detailed microservices health data
   */
  async getServicesHealth(): Promise<ServiceHealth[] | null> {
    try {
      const response = await fetch(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.services}`,
        {
          credentials: "include", // Include credentials (cookies) with the request
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch services health: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      handleApiError(error, API_CONFIG.endpoints.services);
      return null;
    }
  }

  /**
   * Fetch detailed infrastructure health data
   */
  async getInfrastructureHealth(): Promise<InfrastructureHealth[] | null> {
    try {
      const response = await fetch(
        `${API_CONFIG.baseUrl}${API_CONFIG.endpoints.infrastructure}`,
        {
          credentials: "include", // Include credentials (cookies) with the request
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(
          `Failed to fetch infrastructure health: ${response.statusText}`
        );
      }

      return await response.json();
    } catch (error) {
      handleApiError(error, API_CONFIG.endpoints.infrastructure);
      return null;
    }
  }

  /**
   * Fetch crypto history data for a specific symbol
   * @param symbol Cryptocurrency symbol (e.g., 'BTC')
   * @param days Number of days of history to fetch (default: 7)
   * @returns Historical price/KGX value data
   */
  async getCryptoHistory(
    symbol: string,
    days: number = 7
  ): Promise<CryptoHistory | null> {
    try {
      const { url: baseUrl, useCredentials } = this.getBaseUrl(
        API_CONFIG.endpoints.cryptoHistory
      );
      const url = `${baseUrl}${API_CONFIG.endpoints.cryptoHistory}/${symbol}?days=${days}`;

      console.log(
        `Fetching crypto history for ${symbol} from: ${url} (credentials: ${useCredentials})`
      );

      const fetchOptions: RequestInit = {
        headers: {
          "Content-Type": "application/json",
        },
      };

      // Only include credentials when using the API gateway (not for direct connections)
      if (useCredentials) {
        fetchOptions.credentials = "include";
      }

      const response = await fetch(url, fetchOptions);

      if (!response.ok) {
        throw new Error(
          `Failed to fetch crypto history for ${symbol}: ${response.status} ${response.statusText}`
        );
      }

      const data = await response.json();
      console.log(`Fetched history for ${symbol}:`, data);
      return data;
    } catch (error) {
      // Implement multi-level fallback pattern
      console.warn(`Error fetching history for ${symbol}:`, error);
      handleApiError(error, `${API_CONFIG.endpoints.cryptoHistory}/${symbol}`);

      try {
        // No fallback to writer service - all requests should go through the API gateway
        console.log(
          `No fallback available for ${symbol} history - all requests should go through the API gateway`
        );
      } catch (fallbackError) {
        console.error(`Fallback for ${symbol} history failed:`, fallbackError);
        // Continue to next fallback
      }

      // Return null if all fallbacks fail
      return null;
    }
  }

  // Track failed connection attempts to implement circuit breaker pattern
  private sseConnectionAttempts = 0;
  private readonly MAX_SSE_CONNECTION_ATTEMPTS = 5;
  private sseCircuitOpen = false;
  private sseCircuitTimer: NodeJS.Timeout | null = null;

  // Singleton pattern for SSE connection
  private static sseInstance: EventSource | null = null;

  /**
   * Create a Server-Sent Events connection for real-time updates
   * @param onMessage Callback function for handling incoming messages
   * @param onError Callback function for handling errors
   * @returns EventSource instance or null if circuit breaker is open
   */
  createSseConnection(
    onMessage: (data: SseEvent) => void,
    onError?: (error: Event | Error) => void
  ): EventSource | null {
    // Return existing connection if we already have one
    if (ApiService.sseInstance) {
      console.log("Using existing SSE connection");
      return ApiService.sseInstance;
    }
    // If circuit breaker is open, don't try to connect
    if (this.sseCircuitOpen) {
      console.log("SSE circuit breaker is open, not creating connection");
      if (onError) onError(new Error("SSE circuit breaker is open"));
      return null;
    }

    // Create URL with fallback options
    const url = this.getSseUrl();
    console.log(`Creating SSE connection to: ${url}`);

    // Create EventSource without credentials (we're using token in URL if needed)
    const eventSource = new EventSource(url);

    // Store in singleton
    ApiService.sseInstance = eventSource;

    // Track when we last processed a data update
    let lastProcessedTime = 0;
    // Track recently processed messages to avoid duplicates
    const processedEventHashes = new Set<string>();
    // Maximum number of event hashes to retain (sliding window)
    const MAX_EVENT_HASHES = 10;

    // Interface for crypto event data
    interface CryptoEventData {
      type: string;
      data: Array<{
        symbol: string;
        price?: number;
        currentPrice?: number;
        [key: string]:
        | string
        | number
        | boolean
        | undefined
        | null
        | Record<string, unknown>; // Allow other properties with specific types
      }>;
      processedAt?: string;
      updatedAt?: string;
      timestamp?: string;
      [key: string]:
      | string
      | number
      | boolean
      | undefined
      | null
      | Array<Record<string, unknown>>
      | Record<string, unknown>; // Allow other properties with specific types
    }

    // Function to create a hash for an event to detect duplicates
    const getEventHash = (data: CryptoEventData): string => {
      if (
        !data ||
        !data.data ||
        !Array.isArray(data.data) ||
        data.data.length === 0
      ) {
        return "invalid-data";
      }

      // Create a more unique signature using multiple properties and a shorter time period (10 sec)
      const now = new Date();
      const timeKey = `${now.getHours()}:${now.getMinutes()}:${Math.floor(
        now.getSeconds() / 10
      )}`;

      // Include timestamp from metadata if available
      let metadataTime = "";
      // Safe type check for metadata and its timestamp
      if (
        data.data[0].metadata &&
        typeof data.data[0].metadata === "object" &&
        data.data[0].metadata !== null
      ) {
        const meta = data.data[0].metadata as Record<string, unknown>;
        if (meta.timestamp && typeof meta.timestamp === "string") {
          try {
            const metaDate = new Date(meta.timestamp);
            metadataTime = `-${metaDate.getTime()}`;
          } catch (e) {
            // If parsing fails, use an empty string
          }
        }
      }

      // Take first 3 symbols and more values to create a more precise hash
      const symbolData = data.data
        .slice(0, 3)
        .map((item) => {
          const price = item.price || item.currentPrice || 0;
          const kgxValue = item.kgxValue || item.kgx_value || 0;
          const changeUsd = item.changeDueToUsd || item.change_due_usd || 0;

          return `${item.symbol}:${price}:${kgxValue}:${changeUsd}`;
        })
        .join(",");

      return `${timeKey}${metadataTime}-${symbolData}`;
    };

    // Helper to manage the set of processed events (sliding window)
    const trackEventHash = (hash: string): boolean => {
      // Check if we've seen this event before
      if (processedEventHashes.has(hash)) {
        return true; // Already processed
      }

      // Add to the set of processed events
      processedEventHashes.add(hash);

      // If we have too many hashes, remove the oldest ones
      if (processedEventHashes.size > MAX_EVENT_HASHES) {
        // Convert to array, remove oldest items, convert back to set
        const hashesArray = Array.from(processedEventHashes);
        processedEventHashes.clear();
        hashesArray
          .slice(-MAX_EVENT_HASHES)
          .forEach((h) => processedEventHashes.add(h));
      }

      return false; // New event
    };

    // Add event listeners
    eventSource.onmessage = (event) => {
      try {
        console.log(
          `SSE raw event received: ${event.data.substring(0, 200)}...`
        );

        let rawData = JSON.parse(event.data);
        // Reset connection attempts on successful message
        this.sseConnectionAttempts = 0;

        // Get current time for logging and processing decisions
        const now = new Date();
        const timeKey = `${now.getHours()}:${now.getMinutes()}:${Math.floor(
          now.getSeconds() / 10
        )}`;
        const currentTimeKey = Math.floor(now.getTime() / 10000); // One key per 10 seconds

        // Log all received events for debugging
        if (rawData) {
          console.log(
            `SSE event received at ${now.toLocaleTimeString()} - Type: ${rawData.type || "unknown"
            }`
          );
          console.log(`SSE event data structure:`, Object.keys(rawData));

          // Check if this is an array directly
          if (Array.isArray(rawData)) {
            console.log(
              `SSE event is a direct array with ${rawData.length} items`
            );
            // Convert to expected format for processing
            rawData = {
              type: "crypto",
              data: rawData,
            };
          }

          // Check if data property exists and is an array
          if (rawData.data && Array.isArray(rawData.data)) {
            console.log(
              `SSE event contains data array with ${rawData.data.length} items`
            );
            // Log the first item to see its structure
            if (rawData.data.length > 0) {
              console.log(`First item in data array:`, rawData.data[0]);
            }
          }
        }

        // Only process data when we have a valid array with items
        // Support both 'crypto' and 'crypto-kgx' event types from the backend
        if (
          rawData &&
          (rawData.type === "crypto" || rawData.type === "crypto-kgx") &&
          Array.isArray(rawData.data) &&
          rawData.data.length > 0
        ) {
          console.log(
            `Processing ${rawData.data.length} items from ${rawData.type} SSE event`
          );

          // Create a more precise hash for this event to detect duplicates
          const eventHash = getEventHash(rawData);
          const isDuplicate = trackEventHash(eventHash);

          if (isDuplicate) {
            console.log(`Skipping duplicate event with hash: ${eventHash}`);
            return;
          }

          // Important debugging to identify issues with data
          const sampleItem = rawData.data[0];
          console.log(
            `Raw data sample - ${sampleItem.symbol}: ` +
            `Price=${sampleItem.price || sampleItem.currentPrice}, ` +
            `KGX=${sampleItem.kgxValue || sampleItem.kgx_value}, ` +
            `USD=${sampleItem.usd_index || sampleItem.currentUsdIndex}, ` +
            `PrevUSD=${sampleItem.prev_usd_index || sampleItem.previousUsdIndex
            }, ` +
            `Source=${sampleItem.source || "unknown"}`
          );

          // Set a minimum threshold for valid data - check for all required fields in at least 2 items
          const hasValidData =
            rawData.data.filter((item: RawCryptoItem) => {
              return (
                item &&
                item.symbol &&
                (item.price || item.currentPrice) &&
                (item.kgxValue || item.kgx_value) &&
                (item.usd_index || item.currentUsdIndex) &&
                (item.prev_usd_index || item.previousUsdIndex)
              );
            }).length >= 2; // At least 2 valid items required

          if (!hasValidData) {
            console.warn(
              "Skipping data with missing critical fields:",
              rawData.data.map((d) => d.symbol).join(", ")
            );
            return;
          }

          console.log(
            `Processing SSE data at ${now.toLocaleTimeString()} (key: ${timeKey})`
          );

          // Update the last processed time
          lastProcessedTime = currentTimeKey;

          // Filter and normalize the data
          const processedData = rawData.data
            .filter((item: RawCryptoItem) => {
              return (
                item &&
                item.symbol &&
                (typeof item.price !== "undefined" ||
                  typeof item.currentPrice !== "undefined")
              );
            })
            .map((item: RawCryptoItem) => this.normalizeCryptoData(item));

          // Only proceed if we have data after filtering
          if (processedData.length === 0) {
            console.warn("No valid data items after filtering");
            return;
          }

          // Filter by whitelist if defined (limit to only the crypto assets we want to display)
          const whitelistedData =
            API_CONFIG.cryptoWhitelist && API_CONFIG.cryptoWhitelist.length > 0
              ? processedData.filter((item: RawCryptoItem) =>
                API_CONFIG.cryptoWhitelist.includes(item.Symbol)
              )
              : processedData;

          // Sort by whitelist order for consistent presentation
          if (
            API_CONFIG.cryptoWhitelist &&
            API_CONFIG.cryptoWhitelist.length > 0
          ) {
            whitelistedData.sort((a: RawCryptoItem, b: RawCryptoItem) => {
              const indexA = API_CONFIG.cryptoWhitelist.indexOf(a.Symbol);
              const indexB = API_CONFIG.cryptoWhitelist.indexOf(b.Symbol);
              return indexA - indexB;
            });
          }

          // Create a new object with the filtered & normalized data
          // instead of modifying the constant rawData directly
          const updatedData = {
            ...rawData,
            data: whitelistedData,
            processedAt: now.toISOString(),
            updatedAt: now.toLocaleTimeString(),
          };

          // Debug the normalization and filtering results
          if (updatedData.data.length > 0) {
            const sample = updatedData.data[0];
            console.log(
              `Normalized item sample - ${sample.symbol}: ` +
              `Price=${sample.price}, KGX=${sample.kgxValue}, ` +
              `USD=${sample.currentUsdIndex}, Prev=${sample.previousUsdIndex}, ` +
              `USD Δ=${sample.changeDueToUsd.toFixed(
                4
              )} (${sample.changeDueToUsdPercent.toFixed(4)}%), ` +
              `Trade Δ=${sample.changeDueToTrading.toFixed(
                4
              )} (${sample.changeDueToTradingPercent.toFixed(4)}%)`
            );
          }

          console.log(
            `SSE: Filtered from ${processedData.length} to ${whitelistedData.length} whitelisted crypto items`
          );

          // Send the updated data object to the callback
          onMessage(updatedData);
        } else if (rawData && rawData.type && rawData.type !== "crypto") {
          // Always process non-crypto events without throttling
          onMessage(rawData);
        } else {
          console.warn("Received invalid or empty SSE data", rawData);
        }
      } catch (error) {
        console.error("Error parsing SSE event data:", error, event.data);
      }
    };

    // --- BEGIN: Listen for custom event types ---
    const customEventTypes = ["crypto-kgx", "crypto"];
    customEventTypes.forEach((eventType) => {
      eventSource.addEventListener(eventType, (event) => {
        try {
          console.log(`[SSE] Custom event received: ${eventType}`, event);
          if (event.data) {
            const rawData = JSON.parse(event.data);
            onMessage(rawData);
          } else {
            console.warn(
              `[SSE] Custom event '${eventType}' received with no data`,
              event
            );
          }
        } catch (error) {
          console.error(
            `[SSE] Error parsing custom event '${eventType}':`,
            error,
            event
          );
        }
      });
    });
    // --- END: Listen for custom event types ---

    // Handle successful connection
    eventSource.onopen = () => {
      console.log("SSE connection established successfully");
      // Reset connection attempts on successful connection
      this.sseConnectionAttempts = 0;
      // Ensure the singleton is set
      ApiService.sseInstance = eventSource;
    };

    eventSource.onerror = (event) => {
      console.error("SSE connection error:", event);
      if (onError) onError(event);

      this.sseConnectionAttempts++;
      console.log(
        `SSE connection attempt ${this.sseConnectionAttempts} of ${this.MAX_SSE_CONNECTION_ATTEMPTS}`
      );

      // Implement circuit breaker pattern
      if (this.sseConnectionAttempts >= this.MAX_SSE_CONNECTION_ATTEMPTS) {
        console.warn(
          "SSE circuit breaker tripped. Suspending reconnection attempts for 2 minutes."
        );
        this.sseCircuitOpen = true;

        // Reset circuit breaker after 2 minutes
        this.sseCircuitTimer = setTimeout(() => {
          console.log(
            "SSE circuit breaker reset. Reconnection attempts allowed."
          );
          this.sseCircuitOpen = false;
          this.sseConnectionAttempts = 0;

          // Try to reconnect once the circuit is closed
          if (!ApiService.sseInstance) {
            this.createSseConnection(onMessage, onError);
          }
        }, 120000); // 2 minute cool down

        // Close the connection
        eventSource.close();
        // Reset the singleton
        ApiService.sseInstance = null;
        return;
      }

      // Only attempt to reconnect if we haven't exceeded the limit
      setTimeout(() => {
        if (!this.sseCircuitOpen && !ApiService.sseInstance) {
          console.log("Attempting to reconnect SSE...");
          this.createSseConnection(onMessage, onError);
        }
      }, 5000 * Math.min(this.sseConnectionAttempts, 5)); // Exponential backoff up to 25 seconds

      // Close the current connection
      eventSource.close();
      // Reset the singleton
      ApiService.sseInstance = null;
    };

    return eventSource;
  }

  /**
   * Get the appropriate SSE URL with authentication if needed
   * This supports multiple authentication methods based on environment
   */
  private getSseUrl(): string {
    // Always use the API service for SSE connections, regardless of useDirectConnections setting
    // This is part of the SSE centralization refactoring - all SSE connections should go through the API
    const baseUrl = API_CONFIG.sseUrl;

    let sseUrl = baseUrl;

    // Try getting a token from localStorage if available
    const token =
      typeof window !== "undefined" ? localStorage.getItem("auth_token") : null;

    // Always add authentication bypass for development
    if (
      window.location.hostname === "localhost" ||
      window.location.hostname === "127.0.0.1"
    ) {
      console.log(
        "Running in local development mode, adding dev and bypass flags for SSE"
      );
      sseUrl = `${sseUrl}${sseUrl.includes("?") ? "&" : "?"
        }dev=true&bypass_auth=true`;
    }
    // Token auth for non-local environments
    else if (token) {
      // Add token as query parameter to avoid CORS preflight requests
      sseUrl = `${sseUrl}${sseUrl.includes("?") ? "&" : "?"
        }token=${encodeURIComponent(token)}`;
    }
    // No token available, add bypass for testing
    else {
      console.warn(
        "No authentication token available for SSE connection, adding bypass flag"
      );
      sseUrl = `${sseUrl}${sseUrl.includes("?") ? "&" : "?"}bypass_auth=true`;
    }

    console.log(`SSE URL: ${sseUrl}`);
    return sseUrl;
  }
}

// Export as singleton
export default new ApiService();
