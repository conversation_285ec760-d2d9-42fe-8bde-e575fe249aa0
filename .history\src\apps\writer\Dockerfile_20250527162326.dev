# Extend from shared base image
FROM data-pipeline-base:dev

# Switch to root temporarily to copy files
USER root

# Copy service-specific source code with proper ownership
COPY --chown=nextjs:nodejs apps/writer ./apps/writer/

# Switch back to non-root user
USER nextjs

# Set service-specific environment variables
ENV PORT=3004

# Expose port
EXPOSE 3004

# Development command with hot reload
CMD ["nodemon", "--watch", "apps/writer", "--watch", "libs", "--ext", "ts,js,json", "--exec", "ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/writer/src/main.ts"] 