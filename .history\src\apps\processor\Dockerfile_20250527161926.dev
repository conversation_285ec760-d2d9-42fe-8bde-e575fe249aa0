# Extend from shared base image
FROM data-pipeline-base:dev

# Copy service-specific source code
COPY apps/processor ./apps/processor/

# Set service-specific environment variables
ENV PORT=3003

# Expose port
EXPOSE 3003

# Development command with hot reload
CMD ["npx", "nodemon", "--watch", "apps/processor", "--watch", "libs", "--ext", "ts,js,json", "--exec", "npx ts-node --project tsconfig.node.json -r tsconfig-paths/register apps/processor/src/main.ts"] 