---
description: Comprehensive guide for Grafana development best practices, covering code organization, performance, security, testing, and common pitfalls to ensure robust and maintainable Grafana solutions. Includes guidance for creating efficient dashboards, data sources, and plugins.
globs: **/*.{ts,tsx,js,jsx,json,yml,yaml,md,mdc}
---
By adhering to these comprehensive best practices, you can build robust, maintainable, and secure Grafana solutions that provide valuable insights into your data and systems.