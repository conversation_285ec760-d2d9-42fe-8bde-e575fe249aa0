#!/bin/bash

# KGX Crypto Data Application - Server Installation Script
# For Debian 12 (Bookworm)
# Author: System Administrator
# Version: 1.0

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo -e "${GREEN}[$(date +'%Y-%m-%d %H:%M:%S')] $1${NC}"
}

warn() {
    echo -e "${YELLOW}[$(date +'%Y-%m-%d %H:%M:%S')] WARNING: $1${NC}"
}

error() {
    echo -e "${RED}[$(date +'%Y-%m-%d %H:%M:%S')] ERROR: $1${NC}"
    exit 1
}

info() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')] INFO: $1${NC}"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        error "This script should not be run as root. Please run as a regular user with sudo privileges."
    fi
}

# Check if running on Debian 12
check_os() {
    if [[ ! -f /etc/os-release ]]; then
        error "Cannot determine OS version"
    fi
    
    . /etc/os-release
    if [[ "$ID" != "debian" ]] || [[ "$VERSION_ID" != "12" ]]; then
        error "This script is designed for Debian 12 (Bookworm). Current OS: $ID $VERSION_ID"
    fi
    
    log "OS check passed: Debian 12 detected"
}

# Update system packages
update_system() {
    log "Updating system packages..."
    sudo apt update
    sudo apt upgrade -y
    log "System packages updated successfully"
}

# Install essential tools
install_essentials() {
    log "Installing essential tools..."
    sudo apt install -y \
        curl \
        wget \
        git \
        vim \
        htop \
        unzip \
        software-properties-common \
        apt-transport-https \
        ca-certificates \
        gnupg \
        lsb-release \
        jq \
        tree \
        net-tools \
        ufw \
        fail2ban \
        logrotate
    log "Essential tools installed successfully"
}

# Install Docker
install_docker() {
    log "Installing Docker..."
    
    # Remove old versions
    sudo apt remove -y docker docker-engine docker.io containerd runc 2>/dev/null || true
    
    # Add Docker's official GPG key
    curl -fsSL https://download.docker.com/linux/debian/gpg | sudo gpg --dearmor -o /usr/share/keyrings/docker-archive-keyring.gpg
    
    # Add Docker repository
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/docker-archive-keyring.gpg] https://download.docker.com/linux/debian $(lsb_release -cs) stable" | sudo tee /etc/apt/sources.list.d/docker.list > /dev/null
    
    # Install Docker
    sudo apt update
    sudo apt install -y docker-ce docker-ce-cli containerd.io docker-buildx-plugin docker-compose-plugin
    
    # Add user to docker group
    sudo usermod -aG docker $USER
    
    # Start and enable Docker
    sudo systemctl start docker
    sudo systemctl enable docker
    
    log "Docker installed successfully"
}

# Install Node.js 20
install_nodejs() {
    log "Installing Node.js 20..."
    
    # Install Node.js 20 via NodeSource
    curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
    sudo apt install -y nodejs
    
    # Install Yarn globally
    sudo npm install -g yarn
    
    # Verify installation
    node_version=$(node --version)
    yarn_version=$(yarn --version)
    
    log "Node.js installed: $node_version"
    log "Yarn installed: $yarn_version"
}

# Install Nginx
install_nginx() {
    log "Installing Nginx..."
    sudo apt install -y nginx
    sudo systemctl start nginx
    sudo systemctl enable nginx
    log "Nginx installed and started successfully"
}

# Install Certbot for SSL
install_certbot() {
    log "Installing Certbot for SSL certificates..."
    sudo apt install -y certbot python3-certbot-nginx
    sudo systemctl enable certbot.timer
    log "Certbot installed successfully"
}

# Configure firewall
configure_firewall() {
    log "Configuring UFW firewall..."
    
    # Reset UFW to defaults
    sudo ufw --force reset
    
    # Default policies
    sudo ufw default deny incoming
    sudo ufw default allow outgoing
    
    # Allow SSH
    sudo ufw allow ssh
    
    # Allow HTTP and HTTPS
    sudo ufw allow 80/tcp
    sudo ufw allow 443/tcp
    
    # Allow application ports (only from localhost for security)
    sudo ufw allow from 127.0.0.1 to any port 3000  # API
    sudo ufw allow from 127.0.0.1 to any port 3010  # Dashboard
    
    # Enable firewall
    sudo ufw --force enable
    
    log "Firewall configured successfully"
}

# Configure fail2ban
configure_fail2ban() {
    log "Configuring fail2ban..."
    
    sudo tee /etc/fail2ban/jail.local > /dev/null <<EOF
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 3

[sshd]
enabled = true
port = ssh
logpath = /var/log/auth.log
maxretry = 3

[nginx-http-auth]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log

[nginx-limit-req]
enabled = true
port = http,https
logpath = /var/log/nginx/error.log
maxretry = 10
EOF
    
    sudo systemctl restart fail2ban
    sudo systemctl enable fail2ban
    
    log "Fail2ban configured successfully"
}

# Create application directories
create_directories() {
    log "Creating application directories..."
    
    sudo mkdir -p /opt/kgx-app/{data/{postgres,mysql,redis,logs,backups},config,scripts}
    sudo chown -R $USER:$USER /opt/kgx-app
    
    # Create log directories
    mkdir -p /opt/kgx-app/data/logs/{api,scheduler,collector,processor,writer,dashboard,nginx}
    
    log "Application directories created successfully"
}

# Set up log rotation
setup_logrotation() {
    log "Setting up log rotation..."
    
    sudo tee /etc/logrotate.d/kgx-app > /dev/null <<EOF
/opt/kgx-app/data/logs/*/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 $USER $USER
    postrotate
        if [ -f /opt/kgx-app/docker-compose.yml ]; then
            cd /opt/kgx-app && docker compose restart > /dev/null 2>&1 || true
        fi
    endscript
}

/var/log/nginx/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data adm
    postrotate
        systemctl reload nginx > /dev/null 2>&1 || true
    endscript
}
EOF
    
    log "Log rotation configured successfully"
}

# Main installation function
main() {
    log "Starting KGX Crypto Data Application server installation..."
    
    check_root
    check_os
    update_system
    install_essentials
    install_docker
    install_nodejs
    install_nginx
    install_certbot
    configure_firewall
    configure_fail2ban
    create_directories
    setup_logrotation
    
    log "Installation completed successfully!"
    warn "Please log out and log back in for Docker group membership to take effect."
    info "Next steps:"
    info "1. Run './deploy.sh' to deploy the application"
    info "2. Configure your domain and SSL certificates"
    info "3. Set up monitoring and backups"
}

# Run main function
main "$@" 