[NX Daemon Server] - 2025-05-28T15:16:50.995Z - Started listening on: \\.\pipe\nx\C:\Users\<USER>\AppData\Local\Temp\ca17d35630190d8c5486\d.sock
[NX Daemon Server] - 2025-05-28T15:16:51.002Z - [WATCHER]: Subscribed to changes within: C:\workspace\devin\src
[NX Daemon Server] - 2025-05-28T15:16:51.008Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-28T15:16:51.010Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-28T15:16:51.011Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-28T15:16:51.013Z - [REQUEST]: Client Request for Project Graph Received
fatal: no path specified
[NX Daemon Server] - 2025-05-28T15:16:54.109Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:16:56.010Z - Time taken for 'hash changed files from watcher' 964.7428ms
[NX Daemon Server] - 2025-05-28T15:16:56.587Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:16:57.061Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:16:57.095Z - Time taken for 'hash changed files from watcher' 344.6367999999993ms
[NX Daemon Server] - 2025-05-28T15:16:59.080Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:16:59.087Z - Time taken for 'total for creating and serializing project graph' 8066.8328ms
[NX Daemon Server] - 2025-05-28T15:16:59.089Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:16:59.888Z - Time taken for 'total execution time for createProjectGraph()' 2820.6423999999997ms
[NX Daemon Server] - 2025-05-28T15:17:07.724Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:07.724Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:08.127Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:08.127Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:08.751Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:08.751Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:12.435Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:12.436Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:12.887Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:12.888Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:16.002Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:16.002Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:18.698Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:18.698Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:18.904Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:17:20.278Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:20.984Z - Time taken for 'hash changed files from watcher' 1066.1229999999996ms
[NX Daemon Server] - 2025-05-28T15:17:22.449Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:22.449Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:22.579Z - [WATCHER]: apps/dashboard/vite.config.ts.timestamp-1748445442373-b896a94a9582a.mjs was created or restored
[NX Daemon Server] - 2025-05-28T15:17:23.845Z - Error detected when recomputing project file map: Cannot read properties of null (reading 'hash')
[NX Daemon Server] - 2025-05-28T15:17:23.942Z - [WATCHER]: apps/dashboard/vite.config.ts.timestamp-1748445442373-b896a94a9582a.mjs was deleted
[NX Daemon Server] - 2025-05-28T15:17:23.942Z - Time taken for 'init hashing' 93.6166000000012ms
fatal: no path specified
[NX Daemon Server] - 2025-05-28T15:17:25.051Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:25.121Z - Time taken for 'hash changed files from watcher' 968.6526999999987ms
[NX Daemon Server] - 2025-05-28T15:17:27.423Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:27.423Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:27.942Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:27.942Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:29.183Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:17:30.065Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:30.110Z - Time taken for 'hash changed files from watcher' 648.5953000000009ms
[NX Daemon Server] - 2025-05-28T15:17:35.388Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:17:36.744Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:36.787Z - Time taken for 'hash changed files from watcher' 932.8403000000035ms
[NX Daemon Server] - 2025-05-28T15:17:39.756Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:17:41.249Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:41.291Z - Time taken for 'hash changed files from watcher' 670.6175999999978ms
[NX Daemon Server] - 2025-05-28T15:17:49.846Z - [WATCHER]: apps/dashboard/dist was created or restored
fatal: no path specified
[NX Daemon Server] - 2025-05-28T15:17:52.308Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:17:52.341Z - Time taken for 'hash changed files from watcher' 832.8683999999994ms
[NX Daemon Server] - 2025-05-28T15:17:52.344Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:17:52.344Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:20.050Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:20.051Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:20.244Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:20.244Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:24.327Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:24.327Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:28.590Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:18:30.110Z - [WATCHER]: apps/processor/src/domain/plugins/kgx/kgx.processor.ts was modified
[NX Daemon Server] - 2025-05-28T15:18:33.129Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:18:33.225Z - Time taken for 'hash changed files from watcher' 1300.6022999999986ms
[NX Daemon Server] - 2025-05-28T15:18:35.272Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:35.273Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:35.692Z - [REQUEST]: Responding to the client. recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:35.692Z - Done responding to the client recordOutputsHash
[NX Daemon Server] - 2025-05-28T15:18:35.721Z - Closed a connection. Number of open connections: 0
[NX Daemon Server] - 2025-05-28T15:18:51.928Z - [WATCHER]: start_services_temp.bat was created or restored
[NX Daemon Server] - 2025-05-28T15:18:56.853Z - Error detected when recomputing project file map: Cannot read properties of null (reading 'hash')
[NX Daemon Server] - 2025-05-28T15:18:57.075Z - Established a connection. Number of open connections: 1
[NX Daemon Server] - 2025-05-28T15:18:57.077Z - Established a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-28T15:18:57.085Z - [WATCHER]: start_services_temp.bat was deleted
[NX Daemon Server] - 2025-05-28T15:18:57.088Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-05-28T15:18:57.089Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-05-28T15:18:57.090Z - Time taken for 'init hashing' 217.40409999999974ms
[NX Daemon Server] - 2025-05-28T15:18:57.091Z - Closed a connection. Number of open connections: 3
[NX Daemon Server] - 2025-05-28T15:18:57.091Z - Closed a connection. Number of open connections: 2
[NX Daemon Server] - 2025-05-28T15:18:57.091Z - Established a connection. Number of open connections: 3
[NX Daemon Server] - 2025-05-28T15:18:57.092Z - Established a connection. Number of open connections: 4
[NX Daemon Server] - 2025-05-28T15:18:57.099Z - [REQUEST]: Client Request for Project Graph Received
fatal: no path specified
[NX Daemon Server] - 2025-05-28T15:18:58.125Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:18:58.174Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.176Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-05-28T15:18:58.176Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-05-28T15:18:58.178Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-28T15:18:58.178Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.179Z - Time taken for 'total for creating and serializing project graph' 1074.7293000000063ms
[NX Daemon Server] - 2025-05-28T15:18:58.179Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-05-28T15:18:58.179Z - Closed a connection. Number of open connections: 4
[NX Daemon Server] - 2025-05-28T15:18:58.179Z - Established a connection. Number of open connections: 5
[NX Daemon Server] - 2025-05-28T15:18:58.180Z - Established a connection. Number of open connections: 6
[NX Daemon Server] - 2025-05-28T15:18:58.181Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-28T15:18:58.182Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.184Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-28T15:18:58.184Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.185Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.185Z - Time taken for 'total for creating and serializing project graph' 0.20629999999073334ms
[NX Daemon Server] - 2025-05-28T15:18:58.185Z - Closed a connection. Number of open connections: 5
[NX Daemon Server] - 2025-05-28T15:18:58.185Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.187Z - [REQUEST]: Client Request for Project Graph Received
[NX Daemon Server] - 2025-05-28T15:18:58.187Z - [REQUEST]: Responding to the client. project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.189Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.190Z - Time taken for 'total for creating and serializing project graph' 0.20410000000265427ms
[NX Daemon Server] - 2025-05-28T15:18:58.190Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.190Z - Done responding to the client project-graph
[NX Daemon Server] - 2025-05-28T15:18:58.673Z - [WATCHER]: 3 file(s) created or restored, 0 file(s) modified, 0 file(s) deleted
[NX Daemon Server] - 2025-05-28T15:19:01.126Z - [REQUEST]: Updated file-hasher based on watched changes, recomputing project graph...
[NX Daemon Server] - 2025-05-28T15:19:01.272Z - Time taken for 'hash changed files from watcher' 2272.2004000000015ms
