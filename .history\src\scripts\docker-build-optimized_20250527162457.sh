#!/bin/bash

# Docker Build Optimization Script
# This script builds Docker images with advanced caching and optimization

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
REGISTRY=${DOCKER_REGISTRY:-""}
CACHE_FROM=${DOCKER_CACHE_FROM:-""}
BUILD_PARALLEL=${BUILD_PARALLEL:-"true"}
BUILDKIT_ENABLED=${BUILDKIT_ENABLED:-"1"}

# Enable BuildKit for better performance
export DOCKER_BUILDKIT=$BUILDKIT_ENABLED

echo -e "${BLUE}🚀 Starting optimized Docker build process...${NC}"

# Function to build with cache optimization
build_with_cache() {
    local service=$1
    local dockerfile=$2
    local context=${3:-"."}
    
    echo -e "${YELLOW}📦 Building $service...${NC}"
    
    # Build arguments for caching
    local cache_args=""
    if [ ! -z "$CACHE_FROM" ]; then
        cache_args="--cache-from $CACHE_FROM/$service:latest"
    fi
    
    # Registry prefix
    local image_name="$service"
    if [ ! -z "$REGISTRY" ]; then
        image_name="$REGISTRY/$service"
    fi
    
    # Build with optimizations
    docker build \
        --target runtime \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        $cache_args \
        --tag "$image_name:latest" \
        --tag "$image_name:dev" \
        --file "$dockerfile" \
        "$context"
    
    echo -e "${GREEN}✅ $service build completed${NC}"
}

# Function to build base image first
build_base() {
    echo -e "${YELLOW}🏗️  Building base image...${NC}"
    
    local cache_args=""
    if [ ! -z "$CACHE_FROM" ]; then
        cache_args="--cache-from $CACHE_FROM/data-pipeline-base:dev"
    fi
    
    docker build \
        --build-arg BUILDKIT_INLINE_CACHE=1 \
        $cache_args \
        --tag "data-pipeline-base:dev" \
        --file "Dockerfile.base.dev" \
        .
    
    echo -e "${GREEN}✅ Base image build completed${NC}"
}

# Function to build all services
build_all_services() {
    local services=("api" "scheduler" "collector" "processor" "writer" "dashboard")
    
    if [ "$BUILD_PARALLEL" = "true" ]; then
        echo -e "${BLUE}🔄 Building services in parallel...${NC}"
        
        # Build services in parallel (limit to 3 concurrent builds to avoid resource exhaustion)
        printf '%s\n' "${services[@]}" | xargs -n 1 -P 3 -I {} bash -c "
            build_with_cache {} apps/{}/Dockerfile.dev .
        "
    else
        echo -e "${BLUE}🔄 Building services sequentially...${NC}"
        
        for service in "${services[@]}"; do
            build_with_cache "$service" "apps/$service/Dockerfile.dev" "."
        done
    fi
}

# Function to prune build cache
prune_cache() {
    echo -e "${YELLOW}🧹 Pruning build cache...${NC}"
    docker builder prune -f
    echo -e "${GREEN}✅ Build cache pruned${NC}"
}

# Function to show build statistics
show_stats() {
    echo -e "${BLUE}📊 Build Statistics:${NC}"
    echo "Docker images:"
    docker images | grep -E "(data-pipeline|api|scheduler|collector|processor|writer|dashboard)" | head -20
    
    echo -e "\n${BLUE}💾 Disk usage:${NC}"
    docker system df
}

# Main execution
main() {
    local command=${1:-"all"}
    
    case $command in
        "base")
            build_base
            ;;
        "services")
            build_all_services
            ;;
        "all")
            build_base
            build_all_services
            ;;
        "prune")
            prune_cache
            ;;
        "stats")
            show_stats
            ;;
        *)
            echo -e "${RED}❌ Unknown command: $command${NC}"
            echo "Usage: $0 [base|services|all|prune|stats]"
            echo "  base     - Build only the base image"
            echo "  services - Build only the service images"
            echo "  all      - Build base and all services (default)"
            echo "  prune    - Prune build cache"
            echo "  stats    - Show build statistics"
            exit 1
            ;;
    esac
    
    if [ "$command" != "prune" ] && [ "$command" != "stats" ]; then
        show_stats
    fi
    
    echo -e "${GREEN}🎉 Build process completed successfully!${NC}"
}

# Export function for parallel execution
export -f build_with_cache

# Run main function
main "$@" 